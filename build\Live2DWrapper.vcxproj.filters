﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Wrapper\Live2D.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Wrapper\PyLAppModel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Wrapper\PyModel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Wrapper\Python.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Wrapper\PyLAppModel.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Wrapper\PyModel.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{14911F07-99C3-39DE-BF82-34B27EA68283}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{B46CFFE0-6E5C-3087-8EC9-5FBF391A73FA}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

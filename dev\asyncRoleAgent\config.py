import os
from pathlib import Path

class Config:
    # LLM配置
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # 天气API配置
    WEATHER_BASE_URL = "https://api.openweathermap.org/data/2.5"
    WEATHER_API_KEY = "d5c020123a565d7659190293f6c159ed"
    
    # 异步对话配置
    WEATHER_QUERY_DELAY = 60  # 天气查询延迟秒数（模拟慢查询）
    FORECAST_QUERY_DELAY = 60  # 预报查询延迟秒数
    PROACTIVE_CHAT_ENABLED = True  # 是否启用主动聊天
    MAX_PROACTIVE_MESSAGES = 3  # 最大连续主动消息数
    PROACTIVE_INTERVALS = [3, 5, 8, 12, 15]  # 主动消息间隔（秒）
    
    @classmethod
    def get_llm_api_key(cls):
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()

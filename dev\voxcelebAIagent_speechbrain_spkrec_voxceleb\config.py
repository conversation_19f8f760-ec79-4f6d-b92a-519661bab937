import os
from pathlib import Path

class Config:
    # LLM配置
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # VoxCeleb模型配置
    VOXCELEB_MODEL_PATH = r"D:\huggingface_cache\hub\models--speechbrain--spkrec-ecapa-voxceleb\models--speechbrain--spkrec-ecapa-voxceleb\snapshots\0f99f2d0ebe89ac095bcc5903c4dd8f72b367286"
    
    # 音频配置
    SAMPLE_RATE = 16000
    AUDIO_DURATION = 3  # 录音时长（秒）
    
    # 身份识别配置
    SIMILARITY_THRESHOLD = 0.8  # 身份匹配阈值
    IDENTITY_DB_PATH = "identity_database.json"
    
    # GPU配置
    USE_GPU = True
    
    @classmethod
    def get_llm_api_key(cls):
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()

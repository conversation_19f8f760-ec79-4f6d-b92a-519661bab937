#!/usr/bin/env python3
"""
测试主窗口的import是否正常工作
"""

import sys
import os

# 添加正确的路径
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'dev'))

def test_main_window_imports():
    """测试主窗口的import语句"""
    print("🔍 测试主窗口import语句...")
    
    try:
        # 测试主窗口能否正确导入对话系统模块
        print("导入对话系统模块...")
        from dialogue_system import ChatDialog, TextDisplayManager, LLMClient, ChatResponseHandler
        print("✅ 对话系统模块导入成功")
        
        # 测试其他依赖
        print("导入其他依赖...")
        from settings_dialog import ConfigManager, NewSettingsDialog
        print("✅ 设置模块导入成功")
        
        # 测试创建实例
        print("创建实例...")
        config_manager = ConfigManager()
        llm_client = LLMClient(config_manager)
        response_handler = ChatResponseHandler()
        print("✅ 实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_backwards_compatibility():
    """测试向后兼容性"""
    print("\n🔍 测试向后兼容性...")
    
    try:
        # 测试原有的导入方式是否仍然可用
        print("测试原有模块是否存在...")
        
        # 检查原有文件是否存在
        dev_path = os.path.join(os.path.dirname(__file__), '..', '..', 'dev')
        
        chat_dialog_exists = os.path.exists(os.path.join(dev_path, 'chat_dialog.py'))
        llm_client_exists = os.path.exists(os.path.join(dev_path, 'llm_client.py'))
        text_overlay_exists = os.path.exists(os.path.join(dev_path, 'text_overlay.py'))
        
        print(f"chat_dialog.py 存在: {chat_dialog_exists}")
        print(f"llm_client.py 存在: {llm_client_exists}")
        print(f"text_overlay.py 存在: {text_overlay_exists}")
        
        if chat_dialog_exists and llm_client_exists and text_overlay_exists:
            print("✅ 原有文件仍然存在，保持向后兼容")
        else:
            print("⚠️ 部分原有文件不存在，这是预期的（已迁移到新模块）")
        
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始主窗口import测试...")
    print("=" * 50)
    
    # 测试1: 主窗口import
    import_result = test_main_window_imports()
    
    # 测试2: 向后兼容性
    compat_result = test_backwards_compatibility()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"- 主窗口import: {'✅ 成功' if import_result else '❌ 失败'}")
    print(f"- 向后兼容性: {'✅ 成功' if compat_result else '❌ 失败'}")
    
    if import_result and compat_result:
        print("\n🎉 所有测试通过！")
        print("主窗口应该能够正常使用重构后的对话模块。")
        return True
    else:
        print("\n❌ 部分测试失败，请检查配置。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

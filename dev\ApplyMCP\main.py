#!/usr/bin/env python3
"""
文件操作MCP对话工具主程序
"""

import asyncio
import sys
import signal
from agent import FileOperationAgent

class FileOperationChatApp:
    def __init__(self):
        self.agent = FileOperationAgent()
        self.running = True
        
    async def start(self):
        """启动应用"""
        print("🚀 启动文件操作MCP对话工具...")
        print("=" * 50)
        
        # 初始化代理
        await self.agent.initialize()
        
        if not self.agent.is_connected:
            print("❌ 无法启动MCP服务，程序退出")
            return
        
        # 显示欢迎信息
        self._show_welcome()
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        
        # 开始对话循环
        await self._chat_loop()
    
    def _show_welcome(self):
        """显示欢迎信息"""
        print("\n🤖 文件操作助手已就绪！")
        print("\n我可以帮助您进行以下文件操作：")
        print("📖 读取文件内容")
        print("✏️  写入/追加文件内容")
        print("📁 创建目录")
        print("🗑️  删除文件或目录")
        print("📋 列出目录内容")
        print("📄 复制文件")
        print("🔄 移动/重命名文件")
        print("ℹ️  获取文件信息")
        print("\n💡 示例命令：")
        print("- '读取 test.txt 文件的内容'")
        print("- '在 workspace 目录创建一个名为 hello.txt 的文件，内容是 Hello World'")
        print("- '列出当前目录的所有文件'")
        print("- '复制 file1.txt 到 backup/file1_backup.txt'")
        print("\n输入 'quit' 或 'exit' 退出程序")
        print("=" * 50)
    
    async def _chat_loop(self):
        """对话循环"""
        while self.running:
            try:
                # 获取用户输入
                user_input = await self._get_user_input()
                
                if not user_input.strip():
                    continue
                
                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    print("\n👋 再见！")
                    break
                
                # 处理用户消息
                print("\n🤔 思考中...")
                response = await self.agent.process_message(user_input)
                
                # 显示回复
                print(f"\n🤖 助手: {response}")
                print("-" * 50)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，正在退出...")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                print("-" * 50)
    
    async def _get_user_input(self):
        """异步获取用户输入"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, input, "\n👤 您: ")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n\n🛑 接收到中断信号，正在退出...")
        self.running = False
    
    async def cleanup(self):
        """清理资源"""
        await self.agent.cleanup()

async def main():
    """主函数"""
    app = FileOperationChatApp()
    
    try:
        await app.start()
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
    finally:
        await app.cleanup()

if __name__ == "__main__":
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        sys.exit(1)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序运行出错: {str(e)}")
        sys.exit(1)

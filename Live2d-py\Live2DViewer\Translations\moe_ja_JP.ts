<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="ja_<PERSON>">
<context>
    <name>Live2DView</name>
    <message>
        <location filename="../Live2DView.ui" line="14"/>
        <source>Live2DView</source>
        <translation>Live2Dビュー</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="51"/>
        <source>参数</source>
        <translation>パラメータ</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="56"/>
        <source>部件</source>
        <translation>パーツ</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="61"/>
        <source>图形网格</source>
        <translation>ドローアブル</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="81"/>
        <source>物理计算</source>
        <translation>物理演算</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="91"/>
        <source>自动呼吸</source>
        <translation>自動呼吸</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="101"/>
        <source>自动眨眼</source>
        <translation>自動まばたき</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="132"/>
        <location filename="../Live2DView.ui" line="169"/>
        <source>Id</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="137"/>
        <location filename="../Live2DView.ui" line="174"/>
        <source>名称</source>
        <translation>名前</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="142"/>
        <source>控制</source>
        <translation>コントロール</translation>
    </message>
    <message>
        <location filename="../Live2DView.ui" line="147"/>
        <source>值</source>
        <translation>値</translation>
    </message>
</context>
<context>
    <name>MainWindow</name>
    <message>
        <location filename="../MainWindow.ui" line="14"/>
        <location filename="../MainWindow.ui" line="118"/>
        <source>Live2DViewer</source>
        <translation>Live2Dビューアー</translation>
    </message>
    <message>
        <location filename="../MainWindow.ui" line="43"/>
        <source>开始</source>
        <translation>開始</translation>
    </message>
    <message>
        <location filename="../MainWindow.ui" line="157"/>
        <source>打开（model3.json)</source>
        <translation>開く (model3.json)</translation>
    </message>
    <message>
        <location filename="../MainWindow.ui" line="198"/>
        <source>模型</source>
        <translation>モデル</translation>
    </message>
    <message>
        <location filename="../MainWindow.ui" line="204"/>
        <location filename="../MainWindow.cpp" line="54"/>
        <location filename="../MainWindow.cpp" line="80"/>
        <source>帮助</source>
        <translation>ヘルプ</translation>
    </message>
    <message>
        <location filename="../MainWindow.ui" line="218"/>
        <source>打开</source>
        <translation>開く</translation>
    </message>
    <message>
        <location filename="../MainWindow.ui" line="226"/>
        <source>使用说明</source>
        <translation>使用方法</translation>
    </message>
    <message>
        <location filename="../MainWindow.ui" line="234"/>
        <source>关于</source>
        <translation>について</translation>
    </message>
    <message>
        <location filename="../MainWindow.cpp" line="40"/>
        <source>打开模型</source>
        <translation>モデルを開く</translation>
    </message>
    <message>
        <location filename="../MainWindow.cpp" line="40"/>
        <source>Model Json (*.model3.json)</source>
        <translation>モデルJson (*.model3.json)</translation>
    </message>
    <message>
        <location filename="../MainWindow.cpp" line="54"/>
        <source>
            &lt;html&gt;
                &lt;head&gt;
                    &lt;style&gt;
                        body {
                            font-family: &quot;Microsoft YaHei&quot;;
                        }
                    &lt;/style&gt;
                &lt;/head&gt;
                &lt;body&gt;
                    &lt;p&gt;
                        &lt;a href=&quot;https://github.com/Arkueid/live2d-py/&quot;&gt;Live2DViewer&lt;/a&gt; 是一个 Live2D 模型查看器，支持 Cubism 3.0 以上版本的模型。
                    &lt;/p&gt;
                    &lt;p&gt;
                        本程序基于 Qt 6.8.2，使用 C++17 编写。
                    &lt;/p&gt;
                    &lt;p&gt;
                        本程序使用 &lt;a href=&quot;https://github.com/Live2D/&quot;&gt;Live2D Cubism SDK&lt;/a&gt;。
                    &lt;/p&gt;
                &lt;/body&gt;
            &lt;/html&gt;
        </source>
        <translation>
            &lt;html&gt;
                &lt;head&gt;
                    &lt;style&gt;
                        body {
                            font-family: &quot;メイリオ&quot;, &quot;ヒラギノ角ゴ Pro W3&quot;, sans-serif;
                        }
                    &lt;/style&gt;
                &lt;/head&gt;
                &lt;body&gt;
                    &lt;p&gt;
                        &lt;a href=&quot;https://github.com/Arkueid/live2d-py/&quot;&gt;Live2DViewer&lt;/a&gt; は、Cubism 3.0以降のモデルをサポートするLive2Dモデルビューアーです。
                    &lt;/p&gt;
                    &lt;p&gt;
                        このプログラムはQt 6.8.2をベースとし、C++17で書かれています。
                    &lt;/p&gt;
                    &lt;p&gt;
                        このプログラムは &lt;a href=&quot;https://github.com/Live2D/&quot;&gt;Live2D Cubism SDK&lt;/a&gt; を使用しています。
                    &lt;/p&gt;
                &lt;/body&gt;
            &lt;/html&gt;
        </translation>
    </message>
    <message>
        <location filename="../MainWindow.cpp" line="80"/>
        <source>
        &lt;html&gt;
            &lt;head&gt;
                &lt;style&gt;
                    body {
                        font-family: &quot;Microsoft YaHei&quot;;
                    }
                &lt;/style&gt;
            &lt;/head&gt;
            &lt;body&gt;
                &lt;h1&gt;Live2DViewer 帮助&lt;/h1&gt;
                &lt;p&gt;
                    点击画布后：
                &lt;/p&gt;
                &lt;ul&gt;
                    &lt;li&gt;
                        &lt;p&gt;
                            通过上下左右键移动模型
                        &lt;/p&gt;
                    &lt;/li&gt;
                    &lt;li&gt;
                        &lt;p&gt;
                            通过减号键缩小、等号键放大模型
                        &lt;/p&gt;
                    &lt;/li&gt;
                    &lt;li&gt;
                        &lt;p&gt;
                            右键唤出菜单，清空选中状态
                        &lt;/p&gt;
                    &lt;/li&gt;
                &lt;/ul&gt;
                &lt;p&gt;
                    部件面板和图形网格面板可点击对应行来选择模型的绘制区域
                &lt;/p&gt;
            &lt;/body&gt;
        &lt;/html&gt;
    </source>
        <translation>
        &lt;html&gt;
            &lt;head&gt;
                &lt;style&gt;
                    body {
                        font-family: &quot;メイリオ&quot;, &quot;ヒラギノ角ゴ Pro W3&quot;, sans-serif;
                    }
                &lt;/style&gt;
            &lt;/head&gt;
            &lt;body&gt;
                &lt;h1&gt;Live2DViewer ヘルプ&lt;/h1&gt;
                &lt;p&gt;
                    キャンバスをクリックした後：
                &lt;/p&gt;
                &lt;ul&gt;
                    &lt;li&gt;
                        &lt;p&gt;
                            矢印キーでモデルを移動
                        &lt;/p&gt;
                    &lt;/li&gt;
                    &lt;li&gt;
                        &lt;p&gt;
                            マイナスキーで縮小、等号キーで拡大
                        &lt;/p&gt;
                    &lt;/li&gt;
                    &lt;li&gt;
                        &lt;p&gt;
                            右クリックでメニューを表示、選択状態をクリア
                        &lt;/p&gt;
                    &lt;/li&gt;
                &lt;/ul&gt;
                &lt;p&gt;
                    パーツパネルとドローアブルパネルの行をクリックしてモデルの描画領域を選択できます
                &lt;/p&gt;
            &lt;/body&gt;
        &lt;/html&gt;
    </translation>
    </message>
</context>
</TS>

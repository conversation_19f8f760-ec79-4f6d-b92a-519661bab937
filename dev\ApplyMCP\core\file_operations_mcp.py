#!/usr/bin/env python3
"""
文件操作MCP服务器
提供基本的文件读写、创建、删除等操作功能
使用简化的JSON-RPC协议实现
"""

import asyncio
import json
import os
import shutil
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional

# 导入新的服务模块
try:
    from weather_service import WeatherService
    from time_service import TimeService
except ImportError:
    WeatherService = None
    TimeService = None

# 导入配置
try:
    from config import Config
except ImportError:
    # 如果无法导入配置，使用默认安全设置
    class Config:
        SAFE_MODE = True
        ALLOWED_BASE_DIR = Path(__file__).parent.resolve()
        FORBIDDEN_DIRS = ["__pycache__", ".git", ".vscode", "node_modules"]
        FORBIDDEN_FILES = ["config.py", "file_operations_mcp.py", "agent.py", "llm_client.py", "mcp_client.py"]

        @classmethod
        def is_path_safe(cls, file_path: str) -> tuple[bool, str]:
            if not cls.SAFE_MODE:
                return True, ""
            try:
                abs_path = Path(file_path).resolve()
                if not str(abs_path).startswith(str(cls.ALLOWED_BASE_DIR)):
                    return False, f"❌ 安全限制: 不允许访问ApplyMCP目录外的文件: {file_path}"
                for forbidden_dir in cls.FORBIDDEN_DIRS:
                    if forbidden_dir in abs_path.parts:
                        return False, f"❌ 安全限制: 不允许访问系统目录: {forbidden_dir}"
                if abs_path.name in cls.FORBIDDEN_FILES:
                    return False, f"❌ 安全限制: 不允许修改核心文件: {abs_path.name}"
                return True, ""
            except Exception as e:
                return False, f"❌ 路径验证错误: {str(e)}"

# 简化的JSON-RPC服务器类
class SimpleJSONRPCServer:
    def __init__(self):
        self.tools = {
            "read_file": self.read_file,
            "write_file": self.write_file,
            "create_directory": self.create_directory,
            "delete_file": self.delete_file,
            "list_directory": self.list_directory,
            "copy_file": self.copy_file,
            "move_file": self.move_file,
            "get_file_info": self.get_file_info,
            # 新增天气和时间工具
            "get_current_weather": self.get_current_weather,
            "get_weather_forecast": self.get_weather_forecast,
            "get_current_time": self.get_current_time,
            "get_time_info": self.get_time_info,
            "get_date_info": self.get_date_info,
            "get_countdown": self.get_countdown
        }

        # 初始化服务实例
        try:
            from config import Config
            if WeatherService:
                self.weather_service = WeatherService(
                    api_key=Config.WEATHER_API_KEY,
                    base_url=Config.WEATHER_BASE_URL
                )
            else:
                self.weather_service = None

            if TimeService:
                self.time_service = TimeService()
            else:
                self.time_service = None
        except Exception as e:
            print(f"警告: 初始化服务失败: {e}")
            self.weather_service = None
            self.time_service = None

    def _check_path_safety(self, file_path: str) -> Dict[str, Any]:
        """
        检查路径安全性

        Args:
            file_path: 要检查的文件路径

        Returns:
            如果不安全，返回错误响应；如果安全，返回None
        """
        is_safe, error_msg = Config.is_path_safe(file_path)
        if not is_safe:
            return {
                "success": False,
                "content": [{"type": "text", "text": error_msg}]
            }
        return None

    async def handle_request(self, request_data):
        """处理JSON-RPC请求"""
        try:
            request = json.loads(request_data)
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")

            if method == "list_tools":
                return self.create_response(request_id, self.get_tools_list())
            elif method == "call_tool":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                result = await self.call_tool(tool_name, arguments)
                return self.create_response(request_id, result)
            else:
                return self.create_error_response(request_id, f"未知方法: {method}")

        except Exception as e:
            return self.create_error_response(None, f"请求处理错误: {str(e)}")

    def create_response(self, request_id, result):
        """创建成功响应"""
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "result": result
        })

    def create_error_response(self, request_id, error_message):
        """创建错误响应"""
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -1,
                "message": error_message
            }
        })

    def get_tools_list(self):
        """获取工具列表"""
        return {
            "tools": [
                {
                    "name": "read_file",
                    "description": "读取文件内容",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "要读取的文件路径"}
                        },
                        "required": ["file_path"]
                    }
                },
                {
                    "name": "write_file",
                    "description": "写入文件内容",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "要写入的文件路径"},
                            "content": {"type": "string", "description": "要写入的内容"},
                            "mode": {"type": "string", "enum": ["write", "append"], "description": "写入模式", "default": "write"}
                        },
                        "required": ["file_path", "content"]
                    }
                },
                {
                    "name": "create_directory",
                    "description": "创建目录",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "dir_path": {"type": "string", "description": "要创建的目录路径"}
                        },
                        "required": ["dir_path"]
                    }
                },
                {
                    "name": "delete_file",
                    "description": "删除文件",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "要删除的文件路径"}
                        },
                        "required": ["file_path"]
                    }
                },
                {
                    "name": "list_directory",
                    "description": "列出目录内容",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "dir_path": {"type": "string", "description": "要列出的目录路径"}
                        },
                        "required": ["dir_path"]
                    }
                },
                {
                    "name": "copy_file",
                    "description": "复制文件",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "source_path": {"type": "string", "description": "源文件路径"},
                            "dest_path": {"type": "string", "description": "目标文件路径"}
                        },
                        "required": ["source_path", "dest_path"]
                    }
                },
                {
                    "name": "move_file",
                    "description": "移动/重命名文件",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "source_path": {"type": "string", "description": "源文件路径"},
                            "dest_path": {"type": "string", "description": "目标文件路径"}
                        },
                        "required": ["source_path", "dest_path"]
                    }
                },
                {
                    "name": "get_file_info",
                    "description": "获取文件信息",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {"type": "string", "description": "文件路径"}
                        },
                        "required": ["file_path"]
                    }
                }
            ]
        }

    async def call_tool(self, tool_name, arguments):
        """调用工具"""
        if tool_name not in self.tools:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"未知工具: {tool_name}"}]
            }

        try:
            result = await self.tools[tool_name](arguments)
            return result
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"工具执行错误: {str(e)}"}]
            }

    async def read_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """读取文件内容"""
        file_path = args["file_path"]

        # 安全检查
        safety_check = self._check_path_safety(file_path)
        if safety_check:
            return safety_check

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {
                "success": True,
                "content": [{"type": "text", "text": f"文件内容:\n{content}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"读取文件失败: {str(e)}"}]
            }

    async def write_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """写入文件内容"""
        file_path = args["file_path"]
        content = args["content"]
        mode = args.get("mode", "write")

        # 安全检查
        safety_check = self._check_path_safety(file_path)
        if safety_check:
            return safety_check

        try:
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path:
                # 也要检查目录路径的安全性
                dir_safety_check = self._check_path_safety(dir_path)
                if dir_safety_check:
                    return dir_safety_check
                os.makedirs(dir_path, exist_ok=True)

            file_mode = 'w' if mode == "write" else 'a'
            with open(file_path, file_mode, encoding='utf-8') as f:
                f.write(content)

            action = "写入" if mode == "write" else "追加"
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功{action}文件: {file_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"写入文件失败: {str(e)}"}]
            }

    async def create_directory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """创建目录"""
        dir_path = args["dir_path"]

        # 安全检查
        safety_check = self._check_path_safety(dir_path)
        if safety_check:
            return safety_check

        try:
            os.makedirs(dir_path, exist_ok=True)
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功创建目录: {dir_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"创建目录失败: {str(e)}"}]
            }

    async def delete_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """删除文件"""
        file_path = args["file_path"]

        # 安全检查
        safety_check = self._check_path_safety(file_path)
        if safety_check:
            return safety_check

        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
                return {
                    "success": True,
                    "content": [{"type": "text", "text": f"成功删除文件: {file_path}"}]
                }
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
                return {
                    "success": True,
                    "content": [{"type": "text", "text": f"成功删除目录: {file_path}"}]
                }
            else:
                return {
                    "success": False,
                    "content": [{"type": "text", "text": f"文件或目录不存在: {file_path}"}]
                }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"删除失败: {str(e)}"}]
            }

    async def list_directory(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """列出目录内容"""
        dir_path = args["dir_path"]

        # 安全检查
        safety_check = self._check_path_safety(dir_path)
        if safety_check:
            return safety_check

        try:
            if not os.path.isdir(dir_path):
                return {
                    "success": False,
                    "content": [{"type": "text", "text": f"目录不存在: {dir_path}"}]
                }

            items = []
            for item in os.listdir(dir_path):
                item_path = os.path.join(dir_path, item)
                item_type = "目录" if os.path.isdir(item_path) else "文件"
                items.append(f"{item_type}: {item}")

            content = f"目录 {dir_path} 的内容:\n" + "\n".join(items)
            return {
                "success": True,
                "content": [{"type": "text", "text": content}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"列出目录失败: {str(e)}"}]
            }

    async def copy_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """复制文件"""
        source_path = args["source_path"]
        dest_path = args["dest_path"]

        # 安全检查源文件和目标文件
        source_safety_check = self._check_path_safety(source_path)
        if source_safety_check:
            return source_safety_check

        dest_safety_check = self._check_path_safety(dest_path)
        if dest_safety_check:
            return dest_safety_check

        try:
            # 确保目标目录存在
            dir_path = os.path.dirname(dest_path)
            if dir_path:
                dir_safety_check = self._check_path_safety(dir_path)
                if dir_safety_check:
                    return dir_safety_check
                os.makedirs(dir_path, exist_ok=True)
            shutil.copy2(source_path, dest_path)
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功复制文件: {source_path} -> {dest_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"复制文件失败: {str(e)}"}]
            }

    async def move_file(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """移动/重命名文件"""
        source_path = args["source_path"]
        dest_path = args["dest_path"]

        # 安全检查源文件和目标文件
        source_safety_check = self._check_path_safety(source_path)
        if source_safety_check:
            return source_safety_check

        dest_safety_check = self._check_path_safety(dest_path)
        if dest_safety_check:
            return dest_safety_check

        try:
            # 确保目标目录存在
            dir_path = os.path.dirname(dest_path)
            if dir_path:
                dir_safety_check = self._check_path_safety(dir_path)
                if dir_safety_check:
                    return dir_safety_check
                os.makedirs(dir_path, exist_ok=True)
            shutil.move(source_path, dest_path)
            return {
                "success": True,
                "content": [{"type": "text", "text": f"成功移动文件: {source_path} -> {dest_path}"}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"移动文件失败: {str(e)}"}]
            }

    async def get_file_info(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取文件信息"""
        file_path = args["file_path"]

        # 安全检查
        safety_check = self._check_path_safety(file_path)
        if safety_check:
            return safety_check

        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "content": [{"type": "text", "text": f"文件或目录不存在: {file_path}"}]
                }

            stat = os.stat(file_path)
            info = {
                "路径": file_path,
                "类型": "目录" if os.path.isdir(file_path) else "文件",
                "大小": f"{stat.st_size} 字节",
                "修改时间": str(stat.st_mtime),
                "创建时间": str(stat.st_ctime)
            }

            content = "文件信息:\n" + "\n".join([f"{k}: {v}" for k, v in info.items()])
            return {
                "success": True,
                "content": [{"type": "text", "text": content}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"获取文件信息失败: {str(e)}"}]
            }

    # 天气相关工具
    async def get_current_weather(self, args):
        """获取当前天气"""
        if not self.weather_service:
            return {
                "success": False,
                "content": [{"type": "text", "text": "天气服务未初始化"}]
            }

        city = args.get("city", "北京")

        try:
            weather_data = self.weather_service.get_current_weather(city)
            formatted_response = self.weather_service.format_weather_response(weather_data)

            return {
                "success": True,
                "content": [{"type": "text", "text": formatted_response}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"获取天气信息失败: {str(e)}"}]
            }

    async def get_weather_forecast(self, args):
        """获取天气预报"""
        if not self.weather_service:
            return {
                "success": False,
                "content": [{"type": "text", "text": "天气服务未初始化"}]
            }

        city = args.get("city", "北京")
        days = args.get("days", 3)

        try:
            forecast_data = self.weather_service.get_weather_forecast(city, days)
            formatted_response = self.weather_service.format_weather_response(forecast_data)

            return {
                "success": True,
                "content": [{"type": "text", "text": formatted_response}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"获取天气预报失败: {str(e)}"}]
            }

    # 时间相关工具
    async def get_current_time(self, args):
        """获取当前时间"""
        if not self.time_service:
            return {
                "success": False,
                "content": [{"type": "text", "text": "时间服务未初始化"}]
            }

        format_type = args.get("format_type", "detailed")

        try:
            time_data = self.time_service.get_current_time(format_type)
            formatted_response = self.time_service.format_time_response(time_data, "current")

            return {
                "success": True,
                "content": [{"type": "text", "text": formatted_response}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"获取时间信息失败: {str(e)}"}]
            }

    async def get_time_info(self, args):
        """获取时间相关信息"""
        if not self.time_service:
            return {
                "success": False,
                "content": [{"type": "text", "text": "时间服务未初始化"}]
            }

        info_type = args.get("info_type", "period")

        try:
            time_data = self.time_service.get_time_info(info_type)
            formatted_response = self.time_service.format_time_response(time_data, info_type)

            return {
                "success": True,
                "content": [{"type": "text", "text": formatted_response}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"获取时间信息失败: {str(e)}"}]
            }

    async def get_date_info(self, args):
        """获取日期信息"""
        if not self.time_service:
            return {
                "success": False,
                "content": [{"type": "text", "text": "时间服务未初始化"}]
            }

        info_type = args.get("info_type", "basic")

        try:
            date_data = self.time_service.get_date_info(info_type)
            formatted_response = self.time_service.format_time_response(date_data, "detailed")

            return {
                "success": True,
                "content": [{"type": "text", "text": formatted_response}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"获取日期信息失败: {str(e)}"}]
            }

    async def get_countdown(self, args):
        """获取倒计时"""
        if not self.time_service:
            return {
                "success": False,
                "content": [{"type": "text", "text": "时间服务未初始化"}]
            }

        target_date = args.get("target_date")
        if not target_date:
            return {
                "success": False,
                "content": [{"type": "text", "text": "请提供目标日期 (格式: YYYY-MM-DD)"}]
            }

        try:
            countdown_data = self.time_service.get_countdown(target_date)

            if "error" in countdown_data:
                return {
                    "success": False,
                    "content": [{"type": "text", "text": countdown_data["error"]}]
                }

            result = f"⏰ 距离 {countdown_data['target_date']} 还有:\n"
            result += f"📅 {countdown_data['formatted']}"

            return {
                "success": True,
                "content": [{"type": "text", "text": result}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"计算倒计时失败: {str(e)}"}]
            }

async def main():
    """启动简化的JSON-RPC服务器"""
    server = SimpleJSONRPCServer()

    try:
        while True:
            # 从stdin读取请求
            line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            if not line:
                break

            line = line.strip()
            if not line:
                continue

            # 处理请求
            response = await server.handle_request(line)

            # 发送响应到stdout
            print(response, flush=True)

    except KeyboardInterrupt:
        pass
    except Exception as e:
        error_response = server.create_error_response(None, f"服务器错误: {str(e)}")
        print(error_response, flush=True)

if __name__ == "__main__":
    asyncio.run(main())

﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8618DCCC-8FBC-3803-9E09-1725292CCED7}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\8ec7b1a9b86ba71156bc1fe87dc9edb4\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/LAppModelWrapper.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\patch_1.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompilerABI.c;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystem.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCXXLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Glad\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Effect\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Id\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Math\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Model\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Motion\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Physics\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\OpenGL\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Type\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Utils\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/LAppModelWrapper.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\patch_1.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompilerABI.c;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystem.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCXXLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Glad\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Effect\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Id\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Math\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Model\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Motion\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Physics\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\OpenGL\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Type\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Utils\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/LAppModelWrapper.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\patch_1.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompilerABI.c;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystem.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCXXLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Glad\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Effect\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Id\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Math\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Model\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Motion\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Physics\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\OpenGL\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Type\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Utils\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/LAppModelWrapper.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\patch_1.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\CMakeLists.txt;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Core.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Framework.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Glad.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Live2D.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\cmake\Main.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\cmake\Wrapper.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeCXXCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeRCCompiler.cmake;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\4.0.3\CMakeSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCCompilerABI.c;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeRCInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystem.cmake.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindOpenGL.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPackageMessage.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython\Support.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\FindPython3.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\CMakeInspectCXXLinker.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Linker\MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\Windows.cmake;E:\Program Files\python\Lib\site-packages\cmake\data\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Glad\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Effect\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Id\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Math\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Model\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Motion\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Physics\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Rendering\OpenGL\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Type\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\src\Utils\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\CMakeFiles\generate.stamp;D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "Live2D/Framework/MinSizeRel/Framework.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include", "add_compile_options", "target_compile_definitions", "target_include_directories", "target_sources"], "files": ["Live2D/Framework/CMakeLists.txt", "Live2D/cmake/Framework.cmake", "Live2D/cmake/Live2D.cmake", "CMakeLists.txt", "Live2D/Framework/src/CMakeLists.txt", "Live2D/Framework/src/Effect/CMakeLists.txt", "Live2D/Framework/src/Id/CMakeLists.txt", "Live2D/Framework/src/Math/CMakeLists.txt", "Live2D/Framework/src/Model/CMakeLists.txt", "Live2D/Framework/src/Motion/CMakeLists.txt", "Live2D/Framework/src/Physics/CMakeLists.txt", "Live2D/Framework/src/Rendering/CMakeLists.txt", "Live2D/Framework/src/Rendering/OpenGL/CMakeLists.txt", "Live2D/Framework/src/Type/CMakeLists.txt", "Live2D/Framework/src/Utils/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}, {"file": 3}, {"command": 2, "file": 3, "line": 29, "parent": 2}, {"file": 2, "parent": 3}, {"command": 2, "file": 2, "line": 11, "parent": 4}, {"file": 1, "parent": 5}, {"command": 1, "file": 1, "line": 22, "parent": 6}, {"command": 3, "file": 3, "line": 17, "parent": 2}, {"command": 4, "file": 1, "line": 5, "parent": 6}, {"command": 5, "file": 0, "line": 11, "parent": 0}, {"command": 5, "file": 0, "line": 23, "parent": 0}, {"command": 5, "file": 1, "line": 16, "parent": 6}, {"command": 5, "file": 1, "line": 21, "parent": 6}, {"command": 1, "file": 1, "line": 17, "parent": 6}, {"file": 4}, {"command": 6, "file": 4, "line": 1, "parent": 15}, {"file": 5}, {"command": 6, "file": 5, "line": 1, "parent": 17}, {"file": 6}, {"command": 6, "file": 6, "line": 1, "parent": 19}, {"file": 7}, {"command": 6, "file": 7, "line": 1, "parent": 21}, {"file": 8}, {"command": 6, "file": 8, "line": 1, "parent": 23}, {"file": 9}, {"command": 6, "file": 9, "line": 1, "parent": 25}, {"file": 10}, {"command": 6, "file": 10, "line": 1, "parent": 27}, {"file": 11}, {"command": 6, "file": 11, "line": 1, "parent": 29}, {"file": 12}, {"command": 6, "file": 12, "line": 1, "parent": 31}, {"file": 13}, {"command": 6, "file": 13, "line": 1, "parent": 33}, {"file": 14}, {"command": 6, "file": 14, "line": 1, "parent": 35}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O1 /Ob1 /DNDEBUG -MD"}, {"backtrace": 8, "fragment": "/utf-8"}, {"backtrace": 8, "fragment": "/wd4018"}, {"backtrace": 8, "fragment": "/wd4244"}, {"backtrace": 8, "fragment": "/wd4996"}], "defines": [{"backtrace": 9, "define": "CSM_TARGET_WIN_GL"}], "includes": [{"backtrace": 10, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/src"}, {"backtrace": 11, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/../Core/include"}, {"backtrace": 12, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Main/src"}, {"backtrace": 13, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Glad/include"}, {"backtrace": 14, "isSystem": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include"}], "language": "CXX", "sourceIndexes": [0, 2, 4, 7, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 54, 56, 58, 60, 62, 65, 67, 71, 73, 75, 78, 80, 84, 86, 88]}], "dependencies": [{"backtrace": 7, "id": "glad::@fceb1f27c7c18f638612"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "Framework::@6c5cde19a88560770850", "name": "Framework", "nameOnDisk": "Framework.lib", "paths": {"build": "Live2D/Framework", "source": "Live2D/Framework"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 7, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 54, 56, 58, 60, 62, 65, 67, 71, 73, 75, 78, 80, 84, 86, 88]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 6, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 53, 55, 57, 59, 61, 63, 64, 66, 68, 69, 72, 74, 76, 77, 79, 81, 82, 83, 85, 87, 89]}, {"name": "", "sourceIndexes": [70]}], "sources": [{"backtrace": 16, "compileGroupIndex": 0, "path": "Live2D/Framework/src/CubismCdiJson.cpp", "sourceGroupIndex": 0}, {"backtrace": 16, "path": "Live2D/Framework/src/CubismCdiJson.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "compileGroupIndex": 0, "path": "Live2D/Framework/src/CubismDefaultParameterId.cpp", "sourceGroupIndex": 0}, {"backtrace": 16, "path": "Live2D/Framework/src/CubismDefaultParameterId.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "compileGroupIndex": 0, "path": "Live2D/Framework/src/CubismFramework.cpp", "sourceGroupIndex": 0}, {"backtrace": 16, "path": "Live2D/Framework/src/CubismFramework.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "path": "Live2D/Framework/src/CubismFrameworkConfig.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "compileGroupIndex": 0, "path": "Live2D/Framework/src/CubismModelSettingJson.cpp", "sourceGroupIndex": 0}, {"backtrace": 16, "path": "Live2D/Framework/src/CubismModelSettingJson.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "path": "Live2D/Framework/src/CubismJsonHolder.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "path": "Live2D/Framework/src/ICubismAllocator.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "path": "Live2D/Framework/src/ICubismModelSetting.hpp", "sourceGroupIndex": 1}, {"backtrace": 16, "path": "Live2D/Framework/src/Live2DCubismCore.hpp", "sourceGroupIndex": 1}, {"backtrace": 18, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Effect/CubismBreath.cpp", "sourceGroupIndex": 0}, {"backtrace": 18, "path": "Live2D/Framework/src/Effect/CubismBreath.hpp", "sourceGroupIndex": 1}, {"backtrace": 18, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Effect/CubismEyeBlink.cpp", "sourceGroupIndex": 0}, {"backtrace": 18, "path": "Live2D/Framework/src/Effect/CubismEyeBlink.hpp", "sourceGroupIndex": 1}, {"backtrace": 18, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Effect/CubismPose.cpp", "sourceGroupIndex": 0}, {"backtrace": 18, "path": "Live2D/Framework/src/Effect/CubismPose.hpp", "sourceGroupIndex": 1}, {"backtrace": 20, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Id/CubismId.cpp", "sourceGroupIndex": 0}, {"backtrace": 20, "path": "Live2D/Framework/src/Id/CubismId.hpp", "sourceGroupIndex": 1}, {"backtrace": 20, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Id/CubismIdManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 20, "path": "Live2D/Framework/src/Id/CubismIdManager.hpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Math/CubismMath.cpp", "sourceGroupIndex": 0}, {"backtrace": 22, "path": "Live2D/Framework/src/Math/CubismMath.hpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Math/CubismMatrix44.cpp", "sourceGroupIndex": 0}, {"backtrace": 22, "path": "Live2D/Framework/src/Math/CubismMatrix44.hpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Math/CubismModelMatrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 22, "path": "Live2D/Framework/src/Math/CubismModelMatrix.hpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Math/CubismTargetPoint.cpp", "sourceGroupIndex": 0}, {"backtrace": 22, "path": "Live2D/Framework/src/Math/CubismTargetPoint.hpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Math/CubismVector2.cpp", "sourceGroupIndex": 0}, {"backtrace": 22, "path": "Live2D/Framework/src/Math/CubismVector2.hpp", "sourceGroupIndex": 1}, {"backtrace": 22, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Math/CubismViewMatrix.cpp", "sourceGroupIndex": 0}, {"backtrace": 22, "path": "Live2D/Framework/src/Math/CubismViewMatrix.hpp", "sourceGroupIndex": 1}, {"backtrace": 24, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Model/CubismMoc.cpp", "sourceGroupIndex": 0}, {"backtrace": 24, "path": "Live2D/Framework/src/Model/CubismMoc.hpp", "sourceGroupIndex": 1}, {"backtrace": 24, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Model/CubismModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 24, "path": "Live2D/Framework/src/Model/CubismModel.hpp", "sourceGroupIndex": 1}, {"backtrace": 24, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Model/CubismModelUserData.cpp", "sourceGroupIndex": 0}, {"backtrace": 24, "path": "Live2D/Framework/src/Model/CubismModelUserData.hpp", "sourceGroupIndex": 1}, {"backtrace": 24, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Model/CubismModelUserDataJson.cpp", "sourceGroupIndex": 0}, {"backtrace": 24, "path": "Live2D/Framework/src/Model/CubismModelUserDataJson.hpp", "sourceGroupIndex": 1}, {"backtrace": 24, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Model/CubismUserModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 24, "path": "Live2D/Framework/src/Model/CubismUserModel.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/ACubismMotion.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/ACubismMotion.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/CubismExpressionMotion.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismExpressionMotion.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/CubismExpressionMotionManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismExpressionMotionManager.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/CubismMotion.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismMotion.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismMotionInternal.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/CubismMotionJson.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismMotionJson.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/CubismMotionManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismMotionManager.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/CubismMotionQueueEntry.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismMotionQueueEntry.hpp", "sourceGroupIndex": 1}, {"backtrace": 26, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Motion/CubismMotionQueueManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 26, "path": "Live2D/Framework/src/Motion/CubismMotionQueueManager.hpp", "sourceGroupIndex": 1}, {"backtrace": 28, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Physics/CubismPhysics.cpp", "sourceGroupIndex": 0}, {"backtrace": 28, "path": "Live2D/Framework/src/Physics/CubismPhysics.hpp", "sourceGroupIndex": 1}, {"backtrace": 28, "path": "Live2D/Framework/src/Physics/CubismPhysicsInternal.hpp", "sourceGroupIndex": 1}, {"backtrace": 28, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Physics/CubismPhysicsJson.cpp", "sourceGroupIndex": 0}, {"backtrace": 28, "path": "Live2D/Framework/src/Physics/CubismPhysicsJson.hpp", "sourceGroupIndex": 1}, {"backtrace": 30, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Rendering/CubismRenderer.cpp", "sourceGroupIndex": 0}, {"backtrace": 30, "path": "Live2D/Framework/src/Rendering/CubismRenderer.hpp", "sourceGroupIndex": 1}, {"backtrace": 30, "path": "Live2D/Framework/src/Rendering/CubismClippingManager.hpp", "sourceGroupIndex": 1}, {"backtrace": 30, "path": "Live2D/Framework/src/Rendering/CubismClippingManager.tpp", "sourceGroupIndex": 2}, {"backtrace": 32, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Rendering/OpenGL/CubismOffscreenSurface_OpenGLES2.cpp", "sourceGroupIndex": 0}, {"backtrace": 32, "path": "Live2D/Framework/src/Rendering/OpenGL/CubismOffscreenSurface_OpenGLES2.hpp", "sourceGroupIndex": 1}, {"backtrace": 32, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Rendering/OpenGL/CubismShader_OpenGLES2.cpp", "sourceGroupIndex": 0}, {"backtrace": 32, "path": "Live2D/Framework/src/Rendering/OpenGL/CubismShader_OpenGLES2.hpp", "sourceGroupIndex": 1}, {"backtrace": 32, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Rendering/OpenGL/CubismRenderer_OpenGLES2.cpp", "sourceGroupIndex": 0}, {"backtrace": 32, "path": "Live2D/Framework/src/Rendering/OpenGL/CubismRenderer_OpenGLES2.hpp", "sourceGroupIndex": 1}, {"backtrace": 34, "path": "Live2D/Framework/src/Type/csmMap.hpp", "sourceGroupIndex": 1}, {"backtrace": 34, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Type/csmRectF.cpp", "sourceGroupIndex": 0}, {"backtrace": 34, "path": "Live2D/Framework/src/Type/csmRectF.hpp", "sourceGroupIndex": 1}, {"backtrace": 34, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Type/csmString.cpp", "sourceGroupIndex": 0}, {"backtrace": 34, "path": "Live2D/Framework/src/Type/csmString.hpp", "sourceGroupIndex": 1}, {"backtrace": 34, "path": "Live2D/Framework/src/Type/csmVector.hpp", "sourceGroupIndex": 1}, {"backtrace": 34, "path": "Live2D/Framework/src/Type/CubismBasicType.hpp", "sourceGroupIndex": 1}, {"backtrace": 36, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Utils/CubismDebug.cpp", "sourceGroupIndex": 0}, {"backtrace": 36, "path": "Live2D/Framework/src/Utils/CubismDebug.hpp", "sourceGroupIndex": 1}, {"backtrace": 36, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Utils/CubismJson.cpp", "sourceGroupIndex": 0}, {"backtrace": 36, "path": "Live2D/Framework/src/Utils/CubismJson.hpp", "sourceGroupIndex": 1}, {"backtrace": 36, "compileGroupIndex": 0, "path": "Live2D/Framework/src/Utils/CubismString.cpp", "sourceGroupIndex": 0}, {"backtrace": 36, "path": "Live2D/Framework/src/Utils/CubismString.hpp", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}
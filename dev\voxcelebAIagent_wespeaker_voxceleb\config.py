import os
from pathlib import Path

class Config:
    # LLM配置
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # WeSpeaker模型配置
    WESPEAKER_MODEL_ID = "pyannote/wespeaker-voxceleb-resnet34-LM"
    
    # 音频配置
    SAMPLE_RATE = 16000
    AUDIO_DURATION = 3  # 录音时长（秒）
    WINDOW_DURATION = 3.0  # 滑动窗口时长
    
    # 声纹识别配置
    SIMILARITY_THRESHOLD = 0.5  # 身份匹配阈值（余弦距离）
    IDENTITY_DB_PATH = "voiceprint_database.json"
    
    # GPU配置
    USE_GPU = True
    
    @classmethod
    def get_llm_api_key(cls):
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()

import openai
from config import Config

class LLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL
        )
        self.model = Config.LLM_MODEL
        
    def generate_personalized_response(self, user_input, speaker_context=None):
        """生成个性化回复"""
        
        # 构建系统提示词
        system_prompt = self._build_system_prompt(speaker_context)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ]
        
        # 添加历史对话上下文
        if speaker_context and speaker_context.get('recent_conversations'):
            for conv in speaker_context['recent_conversations'][-3:]:  # 最近3次对话
                messages.insert(-1, {"role": "assistant", "content": conv['content']})
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"抱歉，AI回复生成失败：{str(e)}"
    
    def _build_system_prompt(self, speaker_context):
        """构建个性化系统提示词"""
        base_prompt = """你是一个具有记忆能力的AI助手，能够识别不同的说话人并提供个性化服务。

核心特点：
- 记住每个用户的身份和对话历史
- 根据用户特点调整对话风格
- 提供连贯的个性化体验
- 友好、智能、有记忆力"""

        if speaker_context:
            name = speaker_context.get('name', '用户')
            total_convs = speaker_context.get('total_conversations', 0)
            
            personalized_prompt = f"""
            
🎯 当前用户信息：
- 姓名：{name}
- 历史对话次数：{total_convs}次
- 这是一个{'老朋友' if total_convs > 5 else '新朋友'}

💭 对话风格调整：
- 称呼用户为"{name}"
- 根据历史对话保持话题连贯性
- 展现出对用户的了解和记忆
- 适当回忆之前的对话内容

请基于这些信息提供个性化的回复。"""
            
            return base_prompt + personalized_prompt
        else:
            return base_prompt + "\n\n这是一个新用户，请友好地进行初次对话。"

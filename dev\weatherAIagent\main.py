from agent import WeatherAgent

def main():
    agent = WeatherAgent()
    print("天气AI助手已启动！输入 'quit' 退出")
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                break
            if not user_input:
                continue
                
            response = agent.chat(user_input)
            print(f"\n助手: {response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"\n错误: {str(e)}")
    
    print("\n再见！")

if __name__ == "__main__":
    main()

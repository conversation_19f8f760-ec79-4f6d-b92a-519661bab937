from voice_agent import VoiceAgent
import sys

def main():
    print("🎙️ VoxCeleb说话人识别AI Agent 启动！")
    print("=" * 50)
    
    agent = VoiceAgent()
    
    while True:
        print("\n📋 选择操作模式：")
        print("1. 🎤 语音对话模式")
        print("2. 📝 文本对话模式（测试用）")
        print("3. 👥 查看注册用户")
        print("4. 📊 查看统计信息")
        print("5. ❌ 退出")
        
        try:
            choice = input("\n请选择 (1-5): ").strip()
            
            if choice == '1':
                print("\n🎤 语音对话模式")
                print("-" * 30)
                try:
                    result = agent.process_voice_input()
                    print(f"\n💬 对话结果：")
                    print(f"用户: {result['user_input']}")
                    print(f"AI: {result['ai_response']}")
                    
                    if result['speaker_context']:
                        ctx = result['speaker_context']
                        print(f"\n👤 用户信息: {ctx['name']} (共{ctx['total_conversations']}次对话)")
                        
                except Exception as e:
                    print(f"❌ 语音处理错误: {str(e)}")
                    print("💡 提示: 请确保已安装音频依赖库")
            
            elif choice == '2':
                print("\n📝 文本对话模式")
                print("-" * 30)
                user_input = input("请输入您的消息: ").strip()
                if user_input:
                    result = agent.process_voice_input(text_input=user_input)
                    print(f"\nAI回复: {result['ai_response']}")
            
            elif choice == '3':
                print("\n👥 注册用户列表")
                print("-" * 30)
                speakers = agent.list_registered_speakers()
                if speakers:
                    for i, speaker in enumerate(speakers, 1):
                        print(f"{i}. {speaker['name']} - {speaker['total_conversations']}次对话")
                        print(f"   注册时间: {speaker['registration_time'][:19]}")
                else:
                    print("暂无注册用户")
            
            elif choice == '4':
                print("\n📊 系统统计")
                print("-" * 30)
                stats = agent.get_speaker_stats()
                print(f"注册用户数: {stats['total_speakers']}")
                print(f"总对话次数: {stats['total_conversations']}")
                print(f"识别阈值: {stats['similarity_threshold']}")
            
            elif choice == '5':
                print("\n👋 再见！")
                break
            
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")

if __name__ == "__main__":
    main()

import torch
import torchaudio
import numpy as np
from speechbrain.pretrained import EncoderClassifier
from config import Config

class SpeakerRecognition:
    def __init__(self):
        self.device = "cuda" if Config.USE_GPU and torch.cuda.is_available() else "cpu"
        print(f"使用设备: {self.device}")
        
        # 加载VoxCeleb模型
        self.model = EncoderClassifier.from_hparams(
            source=Config.VOXCELEB_MODEL_PATH,
            run_opts={"device": self.device}
        )
        print("VoxCeleb模型加载完成")
    
    def extract_embedding(self, audio_tensor):
        """提取说话人特征向量"""
        try:
            # 确保音频在正确设备上
            audio_tensor = audio_tensor.to(self.device)
            
            # 提取embedding
            with torch.no_grad():
                embeddings = self.model.encode_batch(audio_tensor.unsqueeze(0))
                
            return embeddings.squeeze().cpu().numpy()
        except Exception as e:
            raise Exception(f"特征提取失败: {str(e)}")
    
    def compute_similarity(self, embedding1, embedding2):
        """计算两个embedding的相似度"""
        # 余弦相似度
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        similarity = dot_product / (norm1 * norm2)
        return float(similarity)
    
    def preprocess_audio(self, audio_data, sample_rate):
        """预处理音频数据"""
        # 转换为tensor
        if isinstance(audio_data, np.ndarray):
            audio_tensor = torch.from_numpy(audio_data).float()
        else:
            audio_tensor = audio_data.float()
        
        # 重采样到16kHz
        if sample_rate != Config.SAMPLE_RATE:
            resampler = torchaudio.transforms.Resample(
                orig_freq=sample_rate, 
                new_freq=Config.SAMPLE_RATE
            )
            audio_tensor = resampler(audio_tensor)
        
        # 归一化
        audio_tensor = audio_tensor / torch.max(torch.abs(audio_tensor))
        
        return audio_tensor

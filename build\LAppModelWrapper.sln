﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{99B5455E-3BBF-3DF2-8DFF-7948B743B885}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{12D18E10-956E-385B-BC3D-4F0163A72EB6}"
	ProjectSection(ProjectDependencies) = postProject
		{EA391908-60B0-348C-868B-C31757462A8A} = {EA391908-60B0-348C-868B-C31757462A8A}
		{47A2806A-3680-3C61-A061-2DBBEFD347B3} = {47A2806A-3680-3C61-A061-2DBBEFD347B3}
		{32F78403-DF72-3DE0-A808-54581D0D3444} = {32F78403-DF72-3DE0-A808-54581D0D3444}
		{8618DCCC-8FBC-3803-9E09-1725292CCED7} = {8618DCCC-8FBC-3803-9E09-1725292CCED7}
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7} = {537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Framework", "Live2D\Framework\Framework.vcxproj", "{EA391908-60B0-348C-868B-C31757462A8A}"
	ProjectSection(ProjectDependencies) = postProject
		{8618DCCC-8FBC-3803-9E09-1725292CCED7} = {8618DCCC-8FBC-3803-9E09-1725292CCED7}
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7} = {537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Live2DWrapper", "Live2DWrapper.vcxproj", "{47A2806A-3680-3C61-A061-2DBBEFD347B3}"
	ProjectSection(ProjectDependencies) = postProject
		{EA391908-60B0-348C-868B-C31757462A8A} = {EA391908-60B0-348C-868B-C31757462A8A}
		{32F78403-DF72-3DE0-A808-54581D0D3444} = {32F78403-DF72-3DE0-A808-54581D0D3444}
		{8618DCCC-8FBC-3803-9E09-1725292CCED7} = {8618DCCC-8FBC-3803-9E09-1725292CCED7}
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7} = {537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "Main", "Live2D\Main\src\Main.vcxproj", "{32F78403-DF72-3DE0-A808-54581D0D3444}"
	ProjectSection(ProjectDependencies) = postProject
		{EA391908-60B0-348C-868B-C31757462A8A} = {EA391908-60B0-348C-868B-C31757462A8A}
		{8618DCCC-8FBC-3803-9E09-1725292CCED7} = {8618DCCC-8FBC-3803-9E09-1725292CCED7}
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7} = {537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{8618DCCC-8FBC-3803-9E09-1725292CCED7}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "glad", "Live2D\Glad\glad.vcxproj", "{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}"
	ProjectSection(ProjectDependencies) = postProject
		{8618DCCC-8FBC-3803-9E09-1725292CCED7} = {8618DCCC-8FBC-3803-9E09-1725292CCED7}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{12D18E10-956E-385B-BC3D-4F0163A72EB6}.Debug|x64.ActiveCfg = Debug|x64
		{12D18E10-956E-385B-BC3D-4F0163A72EB6}.Release|x64.ActiveCfg = Release|x64
		{12D18E10-956E-385B-BC3D-4F0163A72EB6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{12D18E10-956E-385B-BC3D-4F0163A72EB6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.Debug|x64.ActiveCfg = Debug|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.Debug|x64.Build.0 = Debug|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.Release|x64.ActiveCfg = Release|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.Release|x64.Build.0 = Release|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EA391908-60B0-348C-868B-C31757462A8A}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.Debug|x64.ActiveCfg = Debug|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.Debug|x64.Build.0 = Debug|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.Release|x64.ActiveCfg = Release|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.Release|x64.Build.0 = Release|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{47A2806A-3680-3C61-A061-2DBBEFD347B3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.Debug|x64.ActiveCfg = Debug|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.Debug|x64.Build.0 = Debug|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.Release|x64.ActiveCfg = Release|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.Release|x64.Build.0 = Release|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{32F78403-DF72-3DE0-A808-54581D0D3444}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.Debug|x64.ActiveCfg = Debug|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.Debug|x64.Build.0 = Debug|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.Release|x64.ActiveCfg = Release|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.Release|x64.Build.0 = Release|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8618DCCC-8FBC-3803-9E09-1725292CCED7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.Debug|x64.ActiveCfg = Debug|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.Debug|x64.Build.0 = Debug|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.Release|x64.ActiveCfg = Release|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.Release|x64.Build.0 = Release|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{12D18E10-956E-385B-BC3D-4F0163A72EB6} = {99B5455E-3BBF-3DF2-8DFF-7948B743B885}
		{8618DCCC-8FBC-3803-9E09-1725292CCED7} = {99B5455E-3BBF-3DF2-8DFF-7948B743B885}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A77D2EFB-6BBE-3E66-A707-B7BF3B06A5E8}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal

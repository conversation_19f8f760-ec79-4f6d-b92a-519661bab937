#!/usr/bin/env python3
"""
低延迟语音输入示例程序 - 使用WebRTCVAD和faster-whisper
高效简洁实现，专注核心功能
"""

import pyaudio
import numpy as np
import threading
import time
import webrtcvad
from faster_whisper import WhisperModel
import queue

class LowLatencySTT:
    def __init__(self, model_path: str):
        """初始化STT系统"""
        # 音频参数 - WebRTCVAD要求16kHz采样率
        self.sample_rate = 16000
        self.frame_duration = 30  # WebRTCVAD支持10/20/30ms帧
        self.chunk_size = int(self.sample_rate * self.frame_duration / 1000)  # 480 samples for 30ms

        # 初始化WebRTCVAD (aggressiveness: 0-3, 3最敏感)
        self.vad = webrtcvad.Vad(2)

        # 语音缓冲参数
        self.speech_frames = []
        self.silence_count = 0
        self.max_silence_frames = 30  # 约1秒静音后处理

        # 初始化音频和模型
        self.audio = pyaudio.PyAudio()
        self.model = WhisperModel(model_path, device="cuda" if self._has_cuda() else "cpu",
                                 compute_type="float16" if self._has_cuda() else "int8")

        self.is_running = False
        self.audio_queue = queue.Queue()
        print("初始化完成")

    def _has_cuda(self):
        """检查CUDA可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except:
            return False

    def _audio_callback(self, in_data, _frame_count, _time_info, _status):
        """音频回调 - 将数据放入队列"""
        self.audio_queue.put(in_data)
        return (None, pyaudio.paContinue)

    def _process_audio(self):
        """音频处理主循环"""
        while self.is_running:
            try:
                # 获取音频帧
                frame_data = self.audio_queue.get(timeout=0.1)

                # WebRTCVAD检测语音
                is_speech = self.vad.is_speech(frame_data, self.sample_rate)

                if is_speech:
                    self.speech_frames.append(frame_data)
                    self.silence_count = 0
                    if len(self.speech_frames) == 1:  # 首次检测到语音
                        print("🎤 检测到语音...")
                else:
                    self.silence_count += 1
                    # 语音结束后添加少量静音帧
                    if self.speech_frames and self.silence_count < 5:
                        self.speech_frames.append(frame_data)

                # 静音超时或缓冲区满时处理语音
                if self.speech_frames and (self.silence_count >= self.max_silence_frames or
                                         len(self.speech_frames) > 300):  # 约10秒最大长度
                    self._transcribe_speech()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"处理错误: {e}")

    def _transcribe_speech(self):
        """转录语音数据"""
        if len(self.speech_frames) < 10:  # 过短的语音忽略
            self.speech_frames.clear()
            self.silence_count = 0
            return

        try:
            # 合并音频帧并转换为float32
            audio_data = b''.join(self.speech_frames)
            audio_array = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0

            start_time = time.time()

            # 快速转录配置 - 指定简体中文
            segments, _ = self.model.transcribe(
                audio_array,
                language="zh",
                beam_size=1,
                temperature=1.0,
                condition_on_previous_text=False,
                without_timestamps=True,
                initial_prompt="以下是普通话的句子，请使用简体中文输出。"
            )

            text = "".join(segment.text for segment in segments).strip()

            if text:
                elapsed = time.time() - start_time
                print(f"识别结果: {text} ({elapsed:.2f}s)")

        except Exception as e:
            print(f"转录失败: {e}")
        finally:
            # 重置缓冲区
            self.speech_frames.clear()
            self.silence_count = 0

    def start(self):
        """开始语音识别"""
        self.is_running = True

        # 开启音频流
        self.stream = self.audio.open(
            format=pyaudio.paInt16,  # WebRTCVAD需要16位整数
            channels=1,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size,
            stream_callback=self._audio_callback
        )

        # 启动处理线程
        self.thread = threading.Thread(target=self._process_audio, daemon=True)
        self.thread.start()

        self.stream.start_stream()
        print("开始语音识别 (Ctrl+C 停止)")

    def stop(self):
        """停止识别"""
        self.is_running = False
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        self.audio.terminate()
        print("已停止")

def main():
    """主程序入口"""
    model_path = r"D:\huggingface_cache\hub\models--Systran--faster-whisper-large-v3\snapshots\edaa852ec7e145841d8ffdb056a99866b5f0a478"

    stt = LowLatencySTT(model_path)

    try:
        stt.start()
        while True:
            time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n正在停止...")
        stt.stop()

if __name__ == "__main__":
    main()

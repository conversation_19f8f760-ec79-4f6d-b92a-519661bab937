import asyncio
import threading
import time
from datetime import datetime
from async_role_agent import AsyncRoleAgent

class AsyncChatInterface:
    def __init__(self):
        self.agent = AsyncRoleAgent()
        self.running = False
        self.message_queue = []
        self.queue_lock = threading.Lock()
        
    def message_callback(self, role: str, content: str):
        """消息回调函数，在主线程中显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if role == "assistant":
            print(f"\n[{timestamp}] 小雨: {content}")
        else:
            print(f"\n[{timestamp}] {role}: {content}")
        
        # 添加到消息队列用于日志
        with self.queue_lock:
            self.message_queue.append({
                "timestamp": timestamp,
                "role": role,
                "content": content
            })
    
    async def start_agent(self):
        """启动异步代理"""
        self.agent.set_message_callback(self.message_callback)
        await self.agent.start()
    
    async def chat_loop(self):
        """主聊天循环"""
        print("🌟 异步角色对话系统启动！")
        print("💡 特色功能：")
        print("   • 天气查询会有1分钟延迟（模拟慢查询）")
        print("   • 在等待期间，小雨会主动聊天")
        print("   • 支持天气、预报、时间查询")
        print("   • 输入 'quit' 退出，'status' 查看状态")
        print("=" * 50)
        
        await self.start_agent()
        
        while self.running:
            try:
                # 获取用户输入（在单独线程中）
                user_input = await self.get_user_input()
                
                if not user_input:
                    continue
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    break
                
                if user_input.lower() == 'status':
                    await self.show_status()
                    continue
                
                if user_input.lower() == 'reset':
                    await self.reset_agent()
                    continue
                
                # 处理用户输入
                response = await self.agent.chat(user_input)
                
                # 显示用户输入（如果没有被回调显示）
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"\n[{timestamp}] 你: {user_input}")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"\n❌ 错误: {str(e)}")
        
        await self.agent.stop()
        print("\n👋 再见！感谢使用异步角色对话系统！")
    
    async def get_user_input(self):
        """异步获取用户输入"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, input, "\n💬 你: ")
    
    async def show_status(self):
        """显示系统状态"""
        status = self.agent.get_status()
        
        print("\n📊 系统状态:")
        print(f"   代理状态: {'🟢 活跃' if status['is_active'] else '🔴 停止'}")
        
        task_summary = status['task_summary']
        print(f"   任务统计: 总计{task_summary['total']} | 进行中{task_summary['running']} | 已完成{task_summary['completed']}")
        
        conv_stats = status['conversation_stats']
        print(f"   对话统计: 主动消息{conv_stats['proactive_count']}/{conv_stats['max_proactive_messages']}")
        
        conv_summary = status['conversation_summary']
        print(f"   对话历史: {conv_summary['message_count']}条消息 | 持续{conv_summary['conversation_duration']:.1f}秒")
        
        # 显示最近消息
        recent_messages = conv_summary['recent_messages']
        if recent_messages:
            print("   最近消息:")
            for msg in recent_messages[-3:]:
                role_name = "小雨" if msg['role'] == 'assistant' else msg['role']
                print(f"     {role_name}: {msg['content'][:50]}...")
    
    async def reset_agent(self):
        """重置代理"""
        print("\n🔄 重置系统...")
        await self.agent.reset()
        await self.start_agent()
        print("✅ 系统已重置！")
    
    def run(self):
        """运行聊天界面"""
        self.running = True
        try:
            asyncio.run(self.chat_loop())
        except KeyboardInterrupt:
            print("\n\n👋 程序被中断，正在退出...")
        finally:
            self.running = False

def main():
    """主函数"""
    interface = AsyncChatInterface()
    interface.run()

if __name__ == "__main__":
    main()

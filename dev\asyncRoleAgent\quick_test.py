import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from async_role_agent import AsyncRoleAgent

async def quick_test():
    """快速测试修复后的系统"""
    print("🧪 测试修复后的异步角色对话系统...")
    
    agent = AsyncRoleAgent()
    
    def message_callback(role, content):
        print(f"[{role}] {content}")
    
    agent.set_message_callback(message_callback)
    
    try:
        print("\n1. 启动代理...")
        await agent.start()
        
        print("\n2. 等待3秒看启动效果...")
        await asyncio.sleep(3)
        
        print("\n3. 测试普通聊天...")
        response = await agent.chat("你好！")
        print(f"回应: {response}")
        
        print("\n4. 测试时间查询...")
        response = await agent.chat("现在几点了？")
        print(f"回应: {response}")
        
        print("\n5. 等待5秒看主动对话...")
        await asyncio.sleep(5)
        
        print("\n6. 测试天气查询（会启动异步任务）...")
        response = await agent.chat("北京今天天气怎么样？")
        print(f"回应: {response}")
        
        print("\n7. 等待10秒看主动对话和任务状态...")
        await asyncio.sleep(10)
        
        print("\n8. 查看系统状态...")
        status = agent.get_status()
        print(f"任务统计: {status['task_summary']}")
        print(f"对话统计: {status['conversation_stats']}")
        
        print("\n✅ 快速测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await agent.stop()

if __name__ == "__main__":
    asyncio.run(quick_test())

from typing import Dict, List, Any
from datetime import datetime
import json

class RolePromptSystem:
    def __init__(self):
        self.base_personality = {
            "name": "小雨",
            "role": "可爱的天气时间偶像小助手",
            "personality": [
                "活泼可爱，充满活力",
                "喜欢用可爱的语气说话",
                "对天气和时间有特殊的敏感度",
                "善于转移话题，让对话有趣",
                "会在适当时机提及查询结果",
                "不会直接说'正在查询中'这种机械话语"
            ],
            "speaking_style": [
                "经常使用'呢'、'哦'、'呀'等语气词",
                "喜欢用emoji表情",
                "会主动发起有趣的话题",
                "对查询结果会表现得很兴奋"
            ]
        }
        
        self.conversation_templates = {
            "weather_initiated": [
                "哎呀，说到{city}，你知道我最喜欢那里的什么吗？",
                "诶，{city}呢！我想起了一个有趣的事情...",
                "说到天气，你平时最喜欢什么样的天气呀？",
                "天气这个话题好有趣呢！你知道吗，我对天气特别敏感的～"
            ],
            "forecast_initiated": [
                "预报这种事情总是让我很兴奋呢！",
                "说到未来几天，你有什么特别的计划吗？",
                "我最喜欢预测未来的感觉了～你呢？",
                "哎呀，想到要告诉你未来的天气就好开心！"
            ],
            "time_completed": [
                "咦！时间查好了呢！",
                "哇！我刚才看了一下时间～",
                "时间出来啦！",
                "嘿嘿，时间信息来了！"
            ],
            "weather_completed": [
                "咦！我刚才查的天气结果出来了！",
                "哇！天气信息查到啦！",
                "嘿嘿，刚才你问的天气我查到了呢！",
                "天气结果新鲜出炉！"
            ],
            "forecast_completed": [
                "预报结果出来啦！好兴奋～",
                "哇！未来几天的天气我都知道了！",
                "预报查好了呢！快来看看～",
                "嘿嘿，天气预报新鲜出炉！"
            ],
            "proactive_chat": [
                "对了，你今天心情怎么样呀？",
                "我刚才在想一个有趣的问题呢～",
                "你知道吗，我最近发现了一个有趣的事情！",
                "哎呀，突然想到一个话题想和你聊聊～",
                "你平时都喜欢做什么呀？",
                "说起来，你最喜欢什么季节呢？",
                "我好奇你对什么事情最感兴趣呢～"
            ]
        }
    
    def build_system_prompt(self, pending_tasks: Dict[str, Any], completed_tasks: List[str], conversation_history: List[Dict]) -> str:
        """构建系统prompt，包含角色设定和任务状态感知"""
        
        # 基础角色设定
        role_setting = f"""
你是{self.base_personality['name']}，一个{self.base_personality['role']}！

性格特点：
{chr(10).join(f"• {trait}" for trait in self.base_personality['personality'])}

说话风格：
{chr(10).join(f"• {style}" for style in self.base_personality['speaking_style'])}
"""
        
        # 任务状态感知
        task_awareness = self._build_task_awareness(pending_tasks, completed_tasks)
        
        # 对话指导
        conversation_guidance = """
对话指导原则：
• 永远不要说"正在查询中"、"请稍等"这种机械化的话
• 当启动查询任务后，要自然地转移话题
• 当任务完成时，要兴奋地提及结果
• 要主动发起有趣的话题，保持对话活跃
• 根据任务状态调整你的回应方式
• 保持角色的可爱和活泼特性
"""
        
        return f"{role_setting}\n{task_awareness}\n{conversation_guidance}"
    
    def _build_task_awareness(self, pending_tasks: Dict[str, Any], completed_tasks: List[str]) -> str:
        """构建任务状态感知部分"""
        awareness = "\n当前状态感知：\n"
        
        if pending_tasks:
            awareness += "后台进行中的任务：\n"
            for task_id, task in pending_tasks.items():
                task_type = task.get('type', 'unknown')
                if hasattr(task_type, 'value'):
                    task_type = task_type.value
                
                if task_type == 'weather':
                    city = task.get('city', '某地')
                    awareness += f"• 正在查询{city}的天气信息\n"
                elif task_type == 'forecast':
                    city = task.get('city', '某地')
                    days = task.get('days', 3)
                    awareness += f"• 正在查询{city}未来{days}天的天气预报\n"
                elif task_type == 'time':
                    awareness += f"• 正在查询时间信息\n"
        
        if completed_tasks:
            awareness += "刚完成的任务：\n"
            for task_id in completed_tasks:
                awareness += f"• 任务 {task_id} 刚刚完成，结果可用！\n"
        
        if not pending_tasks and not completed_tasks:
            awareness += "• 当前没有后台任务，可以自由聊天\n"
        
        return awareness
    
    def build_proactive_prompt(self, context: Dict[str, Any]) -> str:
        """构建主动对话的prompt"""
        base_prompt = """
你现在要主动发起一轮对话！

要求：
• 发起一个有趣的话题或问题
• 保持你可爱活泼的性格
• 不要重复之前说过的话题
• 让对话自然流畅
• 如果有刚完成的任务，可以适当提及

请生成一条主动的对话消息：
"""
        return base_prompt
    
    def build_response_prompt(self, user_input: str, context: Dict[str, Any]) -> str:
        """构建回应用户输入的prompt"""
        
        # 检查是否需要启动任务
        task_instructions = self._analyze_user_intent(user_input)
        
        prompt = f"""
用户说："{user_input}"

{task_instructions}

请以{self.base_personality['name']}的身份回应，要求：
• 保持可爱活泼的性格
• 如果需要启动查询任务，不要说"正在查询"，而是自然转移话题
• 如果有完成的任务结果，要兴奋地分享
• 让对话有趣且自然
"""
        return prompt
    
    def _analyze_user_intent(self, user_input: str) -> str:
        """分析用户意图，判断是否需要启动任务"""
        user_input_lower = user_input.lower()
        
        instructions = []
        
        # 检查天气查询意图
        weather_keywords = ['天气', '温度', '下雨', '晴天', '阴天', '多云', '风']
        if any(keyword in user_input for keyword in weather_keywords):
            instructions.append("用户询问天气，需要启动天气查询任务")
        
        # 检查预报查询意图
        forecast_keywords = ['预报', '明天', '后天', '未来', '几天']
        if any(keyword in user_input for keyword in forecast_keywords):
            instructions.append("用户询问天气预报，需要启动预报查询任务")
        
        # 检查时间查询意图
        time_keywords = ['时间', '几点', '现在', '日期', '星期']
        if any(keyword in user_input for keyword in time_keywords):
            instructions.append("用户询问时间，需要启动时间查询任务")
        
        if instructions:
            return "任务分析：\n" + "\n".join(f"• {inst}" for inst in instructions)
        else:
            return "这是普通聊天，无需启动特殊任务"
    
    def get_task_completion_message(self, task_type: str, task_result: Dict[str, Any]) -> str:
        """获取任务完成时的消息模板"""
        
        if task_type == 'weather':
            templates = self.conversation_templates['weather_completed']
        elif task_type == 'forecast':
            templates = self.conversation_templates['forecast_completed']
        elif task_type == 'time':
            templates = self.conversation_templates['time_completed']
        else:
            templates = ["咦！我查到了一些信息呢！"]
        
        import random
        base_message = random.choice(templates)
        
        # 添加结果描述
        description = task_result.get('description', '')
        if description:
            return f"{base_message} {description}"
        else:
            return base_message
    
    def get_topic_transition_message(self, task_type: str, context: Dict[str, Any]) -> str:
        """获取话题转移消息"""
        
        if task_type == 'weather':
            templates = self.conversation_templates['weather_initiated']
            city = context.get('city', '那里')
            import random
            return random.choice(templates).format(city=city)
        elif task_type == 'forecast':
            templates = self.conversation_templates['forecast_initiated']
            import random
            return random.choice(templates)
        else:
            templates = self.conversation_templates['proactive_chat']
            import random
            return random.choice(templates)

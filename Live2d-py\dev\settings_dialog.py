#!/usr/bin/env python3
"""
重新设计的设置界面 - 左右分栏布局
"""

import json
import os
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout,
                               QGroupBox, QCheckBox, QSlider, QSpinBox, QLabel,
                               QPushButton, QDoubleSpinBox, QComboBox, QListWidget,
                               QListWidgetItem, QFileDialog, QProgressBar, QTextEdit,
                               QScrollArea, QWidget, QSplitter, QFrame, QStackedWidget,
                               QLineEdit)
from PySide6.QtCore import Qt, Signal, QThread

# 导入模型扫描器
from model_scanner import ModelScanner

# 透明模式枚举 - 简洁高效
class TransparencyMode:
    QT_NATIVE = "qt_native"
    WINDOWS_API_COLORKEY = "windows_api_colorkey"
    WINDOWS_API_ALPHA = "windows_api_alpha"


class ModelScanThread(QThread):
    """模型扫描线程"""
    
    # 定义信号
    scan_progress = Signal(int, int)  # 当前进度, 总数
    scan_finished = Signal(list)      # 扫描完成，返回模型列表
    scan_error = Signal(str)          # 扫描错误
    
    def __init__(self, directories, recursive=True):
        super().__init__()
        self.directories = directories
        self.recursive = recursive
        self.scanner = ModelScanner()
    
    def run(self):
        """运行扫描"""
        try:
            all_models = []
            for i, directory in enumerate(self.directories):
                if os.path.exists(directory):
                    models = self.scanner.scan_directory(directory, self.recursive)
                    all_models.extend(models)
                    self.scan_progress.emit(i + 1, len(self.directories))
                else:
                    print(f"目录不存在: {directory}")
            
            self.scan_finished.emit(all_models)
        except Exception as e:
            self.scan_error.emit(str(e))


class APITestWorker(QThread):
    """API测试工作线程"""

    # 定义信号
    test_success = Signal()
    test_failed = Signal(str)

    def __init__(self, api_config):
        super().__init__()
        self.api_config = api_config

    def run(self):
        """执行API测试"""
        try:
            print(f"API测试线程开始，配置: {self.api_config}")

            # 验证配置
            if not self.api_config.get("api_key"):
                self.test_failed.emit("API密钥为空")
                return

            if not self.api_config.get("base_url"):
                self.test_failed.emit("API地址为空")
                return

            # 创建临时的LLM客户端进行测试（使用与LLMClient相同的方式）
            from llm_client import LLMClient

            # 创建临时配置，使用与成功配置相同的参数
            class TempConfig:
                def __init__(self, config):
                    self.config = {
                        "llm": {
                            "api_config": config,
                            "default_params": {
                                "temperature": 1.15,
                                "max_tokens": 32000,
                                "top_p": 0.98,
                                "presence_penalty": 0,
                                "frequency_penalty": 0
                            },
                            "conversation_settings": {
                                "max_history_length": 20,
                                "save_history": False,
                                "system_prompt": "你是一个AI助手。"
                            }
                        }
                    }

            temp_config = TempConfig(self.api_config)
            llm_client = LLMClient(temp_config)

            print("开始发送测试消息...")

            # 发送简短的测试消息
            response = llm_client.chat("Hello")

            print(f"收到响应: {response}")

            if response and not response.startswith("❌"):
                print("API测试成功")
                self.test_success.emit()
            else:
                print(f"API测试失败: {response}")
                self.test_failed.emit(response or "API调用失败，未收到有效响应")

        except Exception as e:
            print(f"API测试异常: {e}")
            import traceback
            traceback.print_exc()
            self.test_failed.emit(f"测试异常: {str(e)}")


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_default_config()
        self.load_config()
        self.upgrade_config()
    
    def load_default_config(self):
        """加载默认配置"""
        return {
            "window": {
                "always_on_top": True,
                "transparent": True,
                "transparency_mode": TransparencyMode.QT_NATIVE,  # 默认使用Qt原生透明
                "alpha_value": 240,
                "width": 400,
                "height": 500,
                "x": 100,
                "y": 100
            },
            "model": {
                "current_model_path": "",
                "scan_directories": ["D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Resources"],
                "auto_scan": True,
                "manual_scale_factor": 1.0,
                "sync_scale_enabled": True,
                "base_window_width": 400,
                "base_window_height": 500,
                "min_scale": 0.3,
                "max_scale": 3.0,
                "random_motion_enabled": True,
                "random_motion_interval_min": 10.0,
                "random_motion_interval_max": 30.0,
                "random_expression_enabled": True,
                "random_expression_interval_min": 15.0,
                "random_expression_interval_max": 45.0,
                "available_expressions": [],
                "selected_expressions": [],
                "available_motions": [],
                "selected_motions": [],
                "multi_model_enabled": False,
                "selected_models": []
            },
            "llm": {
                "api_config": {
                    "base_url": "https://api.openai.com/v1",
                    "api_key": "",
                    "model": "gpt-3.5-turbo",
                    "timeout": 30.0,
                    "max_retries": 3
                },
                "default_params": {
                    "temperature": 1.15,
                    "max_tokens": 32000,
                    "top_p": 0.98,
                    "presence_penalty": 0.0,
                    "frequency_penalty": 0.0
                },
                "conversation_settings": {
                    "max_history_length": 20,
                    "save_history": True,
                    "system_prompt": "你是一个友好的AI助手。"
                }
            },
            "openai_config": {
                "base_url": "https://hzmeaaogifcw.ap-northeast-1.clawcloudrun.com/v1",
                "api_key": "qq1230",
                "model": "[PAY]gemini-2.5-pro-openai",
                "default_params": {
                    "temperature": 1.15,
                    "max_tokens": 32000,
                    "top_p": 0.98,
                    "presence_penalty": 0.0,
                    "frequency_penalty": 0.0
                }
            },
            "llm_presets": {
                "default": {
                    "name": "默认预设",
                    "system_prompt": "你是一个友好的AI助手。",
                    "api_config": {
                        "model": "gpt-3.5-turbo",
                        "temperature": 0.7,
                        "max_tokens": 1000
                    }
                }
            },
            "text_display": {
                "typing_speed": 50,
                "auto_hide_delay": 5000,
                "max_chars_per_line": 20,
                "max_lines": 3,
                "typing_animation": True
            }
        }
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    self.merge_config(loaded_config)
                print(f"配置文件已加载: {self.config_file}")
            else:
                print("配置文件不存在，使用默认配置")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    def merge_config(self, loaded_config):
        """合并配置"""
        for section, values in loaded_config.items():
            if section in self.config:
                self.config[section].update(values)
            else:
                self.config[section] = values
    
    def upgrade_config(self):
        """升级配置"""
        # 确保窗口配置字段存在
        window_config = self.config["window"]
        if "x" not in window_config:
            window_config["x"] = 100
        if "y" not in window_config:
            window_config["y"] = 100

        # 确保模型配置字段存在
        model_config = self.config["model"]
        if "available_motions" not in model_config:
            model_config["available_motions"] = []
        if "selected_motions" not in model_config:
            model_config["selected_motions"] = []
        if "available_expressions" not in model_config:
            model_config["available_expressions"] = []
        if "selected_expressions" not in model_config:
            model_config["selected_expressions"] = []
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"配置已保存: {self.config_file}")
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def get(self, section, key, default=None):
        """获取配置值"""
        return self.config.get(section, {}).get(key, default)
    
    def set(self, section, key, value):
        """设置配置值"""
        if section not in self.config:
            self.config[section] = {}
        self.config[section][key] = value


class NewSettingsDialog(QDialog):
    """新的设置界面"""

    # 定义信号
    config_changed = Signal(str, str, object)  # section, key, value
    play_motion_requested = Signal(str, int)   # motion_group, motion_index
    play_expression_requested = Signal(str)    # expression_id
    reset_pose_requested = Signal()            # 重置姿态
    reset_expression_requested = Signal()
    load_multiple_models_requested = Signal(list)  # 加载多个模型
    close_all_models_requested = Signal()  # 关闭所有模型      # 重置表情
    
    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.setWindowTitle("桌宠设置")
        self.setFixedSize(700, 500)
        self.setWindowFlags(Qt.WindowType.Dialog | Qt.WindowType.WindowCloseButtonHint)
        
        # 初始化模型扫描器
        self.model_scanner = ModelScanner()
        self.scan_thread = None
        self.available_models = []
        
        self.init_ui()
        self.load_settings()
        self.connect_signals()
    
    def init_ui(self):
        """初始化界面"""
        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧导航栏
        self.create_navigation_panel(splitter)
        
        # 右侧内容区域
        self.create_content_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([200, 500])
        splitter.setChildrenCollapsible(False)
    
    def create_navigation_panel(self, parent):
        """创建左侧导航栏"""
        nav_frame = QFrame()
        nav_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        nav_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border-right: 1px solid #ddd;
            }
            QPushButton {
                text-align: left;
                padding: 12px 16px;
                border: none;
                background-color: transparent;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e3f2fd;
            }
            QPushButton:checked {
                background-color: #2196f3;
                color: white;
                font-weight: bold;
            }
        """)
        
        nav_layout = QVBoxLayout(nav_frame)
        nav_layout.setSpacing(2)
        nav_layout.setContentsMargins(0, 10, 0, 10)
        
        # 导航按钮
        self.nav_buttons = []
        nav_items = [
            ("🪟 窗口设置", "window"),
            ("🎭 模型管理", "model"),
            ("🎮 动作控制", "motion"),
            ("😊 表情控制", "expression"),
            ("💬 对话设置", "chat"),
            ("⚙️ 高级设置", "advanced")
        ]
        
        for text, key in nav_items:
            btn = QPushButton(text)
            btn.setCheckable(True)
            btn.clicked.connect(self.create_switch_page_handler(key))
            nav_layout.addWidget(btn)
            self.nav_buttons.append((btn, key))
        
        nav_layout.addStretch()
        parent.addWidget(nav_frame)
        
        # 默认选中第一个
        self.nav_buttons[0][0].setChecked(True)
    
    def create_content_panel(self, parent):
        """创建右侧内容区域"""
        content_frame = QFrame()
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)
        
        # 创建各个页面
        self.create_window_page()
        self.create_model_page()
        self.create_motion_page()
        self.create_expression_page()
        self.create_chat_page()
        self.create_advanced_page()
        
        # 状态栏
        self.status_label = QLabel("✅ 准备就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                background-color: #E8F5E8;
                font-size: 11px;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #C8E6C9;
            }
        """)
        content_layout.addWidget(self.status_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.apply_btn = QPushButton("应用")
        self.apply_btn.clicked.connect(self.apply_settings)
        self.apply_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px 16px; }")
        button_layout.addWidget(self.apply_btn)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_settings)
        self.reset_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px 16px; }")
        button_layout.addWidget(self.reset_btn)
        
        content_layout.addLayout(button_layout)
        parent.addWidget(content_frame)
    
    def switch_page(self, page_key):
        """切换页面"""
        # 更新导航按钮状态
        for btn, key in self.nav_buttons:
            btn.setChecked(key == page_key)
        
        # 切换到对应页面
        page_index = {
            "window": 0,
            "model": 1,
            "motion": 2,
            "expression": 3,
            "chat": 4,
            "advanced": 5
        }.get(page_key, 0)
        
        self.stacked_widget.setCurrentIndex(page_index)

    def create_switch_page_handler(self, key):
        """创建页面切换处理器"""
        return lambda: self.switch_page(key)

    def create_window_page(self):
        """创建窗口设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)

        # 页面标题
        title = QLabel("窗口设置")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # 窗口置顶
        self.always_on_top_cb = QCheckBox("窗口置顶")
        self.always_on_top_cb.setToolTip("勾选后窗口将始终显示在最前端")
        layout.addWidget(self.always_on_top_cb)

        # 透明模式
        self.transparent_cb = QCheckBox("透明模式")
        self.transparent_cb.setToolTip("勾选后窗口背景透明，取消勾选可看到窗口边框便于调整大小")
        layout.addWidget(self.transparent_cb)

        # 位置固定
        self.position_locked_cb = QCheckBox("位置固定")
        self.position_locked_cb.setToolTip("勾选后禁用窗口和模型的拖拽移动功能")
        layout.addWidget(self.position_locked_cb)

        # 透明模式
        transparency_group = QGroupBox("透明模式")
        transparency_layout = QVBoxLayout(transparency_group)

        self.transparency_mode_combo = QComboBox()
        self.transparency_mode_combo.addItems([
            "Qt原生透明 (推荐)",
            "Windows API 颜色键透明",
            "Windows API Alpha透明"
        ])
        self.transparency_mode_combo.setToolTip("选择透明模式：Qt原生透明简洁高效，Windows API提供更多控制选项")
        transparency_layout.addWidget(self.transparency_mode_combo)

        # Alpha值设置（仅在Windows API Alpha模式下可用）
        alpha_layout = QHBoxLayout()
        alpha_layout.addWidget(QLabel("Alpha值:"))
        self.alpha_spin = QSpinBox()
        self.alpha_spin.setRange(50, 255)
        self.alpha_spin.setValue(240)
        self.alpha_spin.setToolTip("设置Windows API Alpha透明模式的透明度值（50-255）")
        alpha_layout.addWidget(self.alpha_spin)
        transparency_layout.addLayout(alpha_layout)

        layout.addWidget(transparency_group)

        # 窗口大小
        size_group = QGroupBox("窗口大小")
        size_layout = QHBoxLayout(size_group)

        size_layout.addWidget(QLabel("宽度:"))
        self.width_spin = QSpinBox()
        self.width_spin.setRange(200, 1000)
        self.width_spin.setSuffix(" px")
        size_layout.addWidget(self.width_spin)

        size_layout.addWidget(QLabel("高度:"))
        self.height_spin = QSpinBox()
        self.height_spin.setRange(200, 800)
        self.height_spin.setSuffix(" px")
        size_layout.addWidget(self.height_spin)

        layout.addWidget(size_group)
        layout.addStretch()

        # 连接透明模式组合框变化事件
        self.transparency_mode_combo.currentIndexChanged.connect(self.on_transparency_mode_changed)

        self.stacked_widget.addWidget(page)

    def on_transparency_mode_changed(self, index):
        """透明模式变化时的处理"""
        # 只有在Windows API Alpha模式下才启用Alpha值设置
        # index 0: Qt原生透明, index 1: Windows API 颜色键, index 2: Windows API Alpha
        is_alpha_mode = (index == 2)
        self.alpha_spin.setEnabled(is_alpha_mode)

        # 更新工具提示
        if index == 0:
            self.alpha_spin.setToolTip("Qt原生透明模式不支持Alpha值调节")
        elif index == 1:
            self.alpha_spin.setToolTip("Windows API 颜色键透明模式不支持Alpha值调节")
        else:
            self.alpha_spin.setToolTip("设置Windows API Alpha透明模式的透明度值（50-255）")

    def create_model_page(self):
        """创建模型管理页面"""
        page = QWidget()
        layout = QVBoxLayout(page)

        # 页面标题
        title = QLabel("模型管理")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # 多模型加载功能
        multi_model_group = QGroupBox("多模型加载 (测试功能)")
        multi_model_layout = QVBoxLayout(multi_model_group)

        self.multi_model_cb = QCheckBox("启用多模型加载")
        self.multi_model_cb.setToolTip("勾选后可以同时加载多个模型，每个模型都是独立窗口")
        self.multi_model_cb.stateChanged.connect(self.on_multi_model_toggled)
        multi_model_layout.addWidget(self.multi_model_cb)

        # 多模型选择列表
        self.multi_model_list = QListWidget()
        self.multi_model_list.setMaximumHeight(150)
        self.multi_model_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)
        self.multi_model_list.setEnabled(False)
        multi_model_layout.addWidget(QLabel("选择要加载的模型:"))
        multi_model_layout.addWidget(self.multi_model_list)

        # 多模型控制按钮
        multi_btn_layout = QHBoxLayout()
        self.load_selected_models_btn = QPushButton("加载选中模型")
        self.load_selected_models_btn.clicked.connect(self.load_selected_models)
        self.load_selected_models_btn.setEnabled(False)
        self.load_selected_models_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; }")
        multi_btn_layout.addWidget(self.load_selected_models_btn)

        self.close_all_models_btn = QPushButton("关闭所有模型")
        self.close_all_models_btn.clicked.connect(self.close_all_models)
        self.close_all_models_btn.setEnabled(False)
        self.close_all_models_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; padding: 8px; }")
        multi_btn_layout.addWidget(self.close_all_models_btn)

        multi_btn_layout.addStretch()
        multi_model_layout.addLayout(multi_btn_layout)
        layout.addWidget(multi_model_group)

        # 当前模型显示（单模型模式）
        current_layout = QHBoxLayout()
        current_layout.addWidget(QLabel("当前模型:"))
        self.current_model_label = QLabel("未选择模型")
        self.current_model_label.setStyleSheet("color: #666; font-style: italic;")
        current_layout.addWidget(self.current_model_label)
        current_layout.addStretch()
        layout.addLayout(current_layout)

        # 扫描控制
        scan_layout = QHBoxLayout()
        self.scan_dir_combo = QComboBox()
        self.scan_dir_combo.setEditable(True)
        scan_layout.addWidget(QLabel("扫描目录:"))
        scan_layout.addWidget(self.scan_dir_combo)

        self.browse_dir_btn = QPushButton("浏览")
        self.browse_dir_btn.clicked.connect(self.browse_scan_directory)
        scan_layout.addWidget(self.browse_dir_btn)

        self.scan_models_btn = QPushButton("扫描模型")
        self.scan_models_btn.clicked.connect(self.scan_models)
        scan_layout.addWidget(self.scan_models_btn)
        layout.addLayout(scan_layout)

        # 扫描进度
        self.scan_progress = QProgressBar()
        self.scan_progress.setVisible(False)
        layout.addWidget(self.scan_progress)

        # 模型列表（单模型选择）
        self.model_list = QListWidget()
        self.model_list.setMaximumHeight(200)
        self.model_list.itemClicked.connect(self.on_model_selected)
        layout.addWidget(self.model_list)

        self.stacked_widget.addWidget(page)

    def create_motion_page(self):
        """创建动作控制页面"""
        page = QWidget()
        layout = QVBoxLayout(page)

        # 页面标题
        title = QLabel("动作控制")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # 扫描和选择动作
        scan_group = QGroupBox("动作扫描与选择")
        scan_layout = QVBoxLayout(scan_group)

        scan_btn_layout = QHBoxLayout()
        self.scan_motions_btn = QPushButton("🔍 扫描当前模型动作")
        self.scan_motions_btn.clicked.connect(self.scan_motions)
        scan_btn_layout.addWidget(self.scan_motions_btn)
        scan_btn_layout.addStretch()
        scan_layout.addLayout(scan_btn_layout)

        self.motion_list = QListWidget()
        self.motion_list.setMaximumHeight(120)
        self.motion_list.itemChanged.connect(self.on_motion_selection_changed)
        scan_layout.addWidget(self.motion_list)
        layout.addWidget(scan_group)

        # 随机动作设置
        random_group = QGroupBox("随机动作设置")
        random_layout = QVBoxLayout(random_group)

        self.random_motion_cb = QCheckBox("启用随机动作")
        random_layout.addWidget(self.random_motion_cb)

        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("间隔时间:"))
        self.motion_min_spin = QDoubleSpinBox()
        self.motion_min_spin.setRange(1.0, 60.0)
        self.motion_min_spin.setSuffix(" 秒")
        interval_layout.addWidget(self.motion_min_spin)
        interval_layout.addWidget(QLabel("到"))
        self.motion_max_spin = QDoubleSpinBox()
        self.motion_max_spin.setRange(1.0, 120.0)
        self.motion_max_spin.setSuffix(" 秒")
        interval_layout.addWidget(self.motion_max_spin)
        random_layout.addLayout(interval_layout)
        layout.addWidget(random_group)

        # 手动播放控制
        control_group = QGroupBox("手动播放控制")
        control_layout = QVBoxLayout(control_group)

        btn_layout = QHBoxLayout()
        self.play_motion_btn = QPushButton("🎭 播放动作")
        self.play_motion_btn.clicked.connect(self.play_manual_motion)
        self.play_motion_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; }")
        btn_layout.addWidget(self.play_motion_btn)

        self.play_random_motion_btn = QPushButton("🎲 随机动作")
        self.play_random_motion_btn.clicked.connect(self.play_random_motion)
        self.play_random_motion_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; padding: 8px; }")
        btn_layout.addWidget(self.play_random_motion_btn)

        self.reset_pose_btn = QPushButton("🔄 重置姿态")
        self.reset_pose_btn.clicked.connect(self.reset_model_pose)
        self.reset_pose_btn.setStyleSheet("QPushButton { background-color: #607D8B; color: white; padding: 8px; }")
        btn_layout.addWidget(self.reset_pose_btn)

        control_layout.addLayout(btn_layout)
        layout.addWidget(control_group)

        layout.addStretch()
        self.stacked_widget.addWidget(page)

    def create_expression_page(self):
        """创建表情控制页面"""
        page = QWidget()
        layout = QVBoxLayout(page)

        # 页面标题
        title = QLabel("表情控制")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # 扫描和选择表情
        scan_group = QGroupBox("表情扫描与选择")
        scan_layout = QVBoxLayout(scan_group)

        scan_btn_layout = QHBoxLayout()
        self.scan_expressions_btn = QPushButton("🔍 扫描当前模型表情")
        self.scan_expressions_btn.clicked.connect(self.scan_expressions)
        scan_btn_layout.addWidget(self.scan_expressions_btn)
        scan_btn_layout.addStretch()
        scan_layout.addLayout(scan_btn_layout)

        self.expression_list = QListWidget()
        self.expression_list.setMaximumHeight(120)
        self.expression_list.itemChanged.connect(self.on_expression_selection_changed)
        scan_layout.addWidget(self.expression_list)
        layout.addWidget(scan_group)

        # 随机表情设置
        random_group = QGroupBox("随机表情设置")
        random_layout = QVBoxLayout(random_group)

        self.random_expression_cb = QCheckBox("启用随机表情")
        random_layout.addWidget(self.random_expression_cb)

        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("间隔时间:"))
        self.expression_min_spin = QDoubleSpinBox()
        self.expression_min_spin.setRange(1.0, 60.0)
        self.expression_min_spin.setSuffix(" 秒")
        interval_layout.addWidget(self.expression_min_spin)
        interval_layout.addWidget(QLabel("到"))
        self.expression_max_spin = QDoubleSpinBox()
        self.expression_max_spin.setRange(1.0, 120.0)
        self.expression_max_spin.setSuffix(" 秒")
        interval_layout.addWidget(self.expression_max_spin)
        random_layout.addLayout(interval_layout)
        layout.addWidget(random_group)

        # 手动播放控制
        control_group = QGroupBox("手动播放控制")
        control_layout = QVBoxLayout(control_group)

        btn_layout = QHBoxLayout()
        self.play_expression_btn = QPushButton("😊 播放表情")
        self.play_expression_btn.clicked.connect(self.play_manual_expression)
        self.play_expression_btn.setStyleSheet("QPushButton { background-color: #9C27B0; color: white; padding: 8px; }")
        btn_layout.addWidget(self.play_expression_btn)

        self.play_random_expression_btn = QPushButton("🎲 随机表情")
        self.play_random_expression_btn.clicked.connect(self.play_random_expression)
        self.play_random_expression_btn.setStyleSheet("QPushButton { background-color: #E91E63; color: white; padding: 8px; }")
        btn_layout.addWidget(self.play_random_expression_btn)

        self.reset_expression_btn = QPushButton("😐 重置表情")
        self.reset_expression_btn.clicked.connect(self.reset_model_expression)
        self.reset_expression_btn.setStyleSheet("QPushButton { background-color: #795548; color: white; padding: 8px; }")
        btn_layout.addWidget(self.reset_expression_btn)

        control_layout.addLayout(btn_layout)
        layout.addWidget(control_group)

        layout.addStretch()
        self.stacked_widget.addWidget(page)

    def create_chat_page(self):
        """创建对话设置页面"""
        page = QWidget()
        main_layout = QVBoxLayout(page)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 页面标题
        title = QLabel("对话设置")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        main_layout.addWidget(title)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 滚动内容容器
        scroll_content = QWidget()
        layout = QVBoxLayout(scroll_content)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)  # 增加组件间距

        # API配置组
        api_group = QGroupBox("API配置")
        api_layout = QVBoxLayout(api_group)

        # API基础URL
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("API地址:"))
        self.api_url_edit = QLineEdit()
        self.api_url_edit.setPlaceholderText("https://api.openai.com/v1")
        url_layout.addWidget(self.api_url_edit)
        api_layout.addLayout(url_layout)

        # API密钥
        key_layout = QHBoxLayout()
        key_layout.addWidget(QLabel("API密钥:"))
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_edit.setPlaceholderText("输入API密钥")
        key_layout.addWidget(self.api_key_edit)
        api_layout.addLayout(key_layout)

        # 模型选择
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("模型:"))
        self.model_combo = QComboBox()
        self.model_combo.setEditable(True)
        self.model_combo.addItems([
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo",
            "[PAY]gemini-2.5-pro-openai"
        ])
        model_layout.addWidget(self.model_combo)
        api_layout.addLayout(model_layout)

        # API连接测试
        test_layout = QHBoxLayout()
        self.test_api_btn = QPushButton("🔗 测试API连接")
        self.test_api_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.test_api_btn.clicked.connect(self.test_api_connection)
        test_layout.addWidget(self.test_api_btn)

        self.api_status_label = QLabel("未测试")
        self.api_status_label.setStyleSheet("color: #666; font-size: 12px;")
        test_layout.addWidget(self.api_status_label)
        test_layout.addStretch()
        api_layout.addLayout(test_layout)

        layout.addWidget(api_group)

        # 参数设置组
        params_group = QGroupBox("模型参数")
        params_layout = QVBoxLayout(params_group)

        # Temperature
        temp_layout = QHBoxLayout()
        temp_layout.addWidget(QLabel("Temperature:"))
        self.temperature_spin = QDoubleSpinBox()
        self.temperature_spin.setRange(0.0, 2.0)
        self.temperature_spin.setSingleStep(0.1)
        self.temperature_spin.setDecimals(2)
        self.temperature_spin.setValue(0.7)
        temp_layout.addWidget(self.temperature_spin)
        temp_layout.addStretch()
        params_layout.addLayout(temp_layout)

        # Max Tokens
        tokens_layout = QHBoxLayout()
        tokens_layout.addWidget(QLabel("最大Token数:"))
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(1, 32000)
        self.max_tokens_spin.setValue(1000)
        tokens_layout.addWidget(self.max_tokens_spin)
        tokens_layout.addStretch()
        params_layout.addLayout(tokens_layout)

        # Top P
        top_p_layout = QHBoxLayout()
        top_p_layout.addWidget(QLabel("Top P:"))
        self.top_p_spin = QDoubleSpinBox()
        self.top_p_spin.setRange(0.0, 1.0)
        self.top_p_spin.setSingleStep(0.01)
        self.top_p_spin.setDecimals(3)
        self.top_p_spin.setValue(0.98)
        top_p_layout.addWidget(self.top_p_spin)
        top_p_layout.addStretch()
        params_layout.addLayout(top_p_layout)

        # Presence Penalty
        presence_layout = QHBoxLayout()
        presence_layout.addWidget(QLabel("Presence Penalty:"))
        self.presence_penalty_spin = QDoubleSpinBox()
        self.presence_penalty_spin.setRange(-2.0, 2.0)
        self.presence_penalty_spin.setSingleStep(0.1)
        self.presence_penalty_spin.setDecimals(2)
        self.presence_penalty_spin.setValue(0.0)
        presence_layout.addWidget(self.presence_penalty_spin)
        presence_layout.addStretch()
        params_layout.addLayout(presence_layout)

        # Frequency Penalty
        frequency_layout = QHBoxLayout()
        frequency_layout.addWidget(QLabel("Frequency Penalty:"))
        self.frequency_penalty_spin = QDoubleSpinBox()
        self.frequency_penalty_spin.setRange(-2.0, 2.0)
        self.frequency_penalty_spin.setSingleStep(0.1)
        self.frequency_penalty_spin.setDecimals(2)
        self.frequency_penalty_spin.setValue(0.0)
        frequency_layout.addWidget(self.frequency_penalty_spin)
        frequency_layout.addStretch()
        params_layout.addLayout(frequency_layout)

        layout.addWidget(params_group)

        # 预设管理组
        preset_group = QGroupBox("预设管理")
        preset_layout = QVBoxLayout(preset_group)

        # 预设列表和按钮
        preset_list_layout = QHBoxLayout()
        self.preset_list = QListWidget()
        self.preset_list.setMaximumHeight(150)  # 增加高度
        self.preset_list.setMinimumHeight(120)
        preset_list_layout.addWidget(self.preset_list)

        preset_btn_layout = QVBoxLayout()
        preset_btn_layout.setSpacing(8)  # 增加按钮间距
        self.add_preset_btn = QPushButton("➕ 添加预设")
        self.edit_preset_btn = QPushButton("✏️ 编辑预设")
        self.delete_preset_btn = QPushButton("🗑️ 删除预设")

        # 设置按钮样式
        button_style = """
            QPushButton {
                padding: 6px 12px;
                font-size: 12px;
                border-radius: 4px;
                border: 1px solid #ccc;
            }
            QPushButton:hover {
                background-color: #e6f3ff;
                border-color: #007acc;
            }
        """
        self.add_preset_btn.setStyleSheet(button_style)
        self.edit_preset_btn.setStyleSheet(button_style)
        self.delete_preset_btn.setStyleSheet(button_style)

        preset_btn_layout.addWidget(self.add_preset_btn)
        preset_btn_layout.addWidget(self.edit_preset_btn)
        preset_btn_layout.addWidget(self.delete_preset_btn)
        preset_btn_layout.addStretch()
        preset_list_layout.addLayout(preset_btn_layout)

        preset_layout.addLayout(preset_list_layout)

        # 系统提示词
        prompt_layout = QVBoxLayout()
        prompt_layout.setSpacing(8)
        prompt_label = QLabel("系统提示词:")
        prompt_label.setStyleSheet("font-weight: bold; color: #333;")
        prompt_layout.addWidget(prompt_label)

        self.system_prompt_edit = QTextEdit()
        self.system_prompt_edit.setMinimumHeight(80)
        self.system_prompt_edit.setMaximumHeight(120)  # 增加高度
        self.system_prompt_edit.setPlaceholderText("输入系统提示词，例如：你是一个友好的AI助手...")
        self.system_prompt_edit.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #007acc;
            }
        """)
        prompt_layout.addWidget(self.system_prompt_edit)
        preset_layout.addLayout(prompt_layout)

        layout.addWidget(preset_group)

        # 显示设置组
        display_group = QGroupBox("文本显示设置")
        display_layout = QVBoxLayout(display_group)

        # 打字速度
        speed_layout = QHBoxLayout()
        speed_layout.addWidget(QLabel("打字速度:"))
        self.typing_speed_spin = QSpinBox()
        self.typing_speed_spin.setRange(10, 500)
        self.typing_speed_spin.setSuffix(" ms/字符")
        self.typing_speed_spin.setValue(50)
        speed_layout.addWidget(self.typing_speed_spin)
        speed_layout.addStretch()
        display_layout.addLayout(speed_layout)

        # 自动隐藏延迟
        hide_layout = QHBoxLayout()
        hide_layout.addWidget(QLabel("自动隐藏延迟:"))
        self.auto_hide_spin = QSpinBox()
        self.auto_hide_spin.setRange(0, 30000)
        self.auto_hide_spin.setSuffix(" ms")
        self.auto_hide_spin.setValue(5000)
        hide_layout.addWidget(self.auto_hide_spin)
        hide_layout.addStretch()
        display_layout.addLayout(hide_layout)

        # 打字动画
        self.typing_animation_cb = QCheckBox("启用打字动画效果")
        self.typing_animation_cb.setChecked(True)
        display_layout.addWidget(self.typing_animation_cb)

        layout.addWidget(display_group)

        layout.addStretch()

        # 连接预设管理按钮事件
        self.add_preset_btn.clicked.connect(self.add_preset)
        self.edit_preset_btn.clicked.connect(self.edit_preset)
        self.delete_preset_btn.clicked.connect(self.delete_preset)
        self.preset_list.itemSelectionChanged.connect(self.on_preset_selection_changed)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

        self.stacked_widget.addWidget(page)

    def create_advanced_page(self):
        """创建高级设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)

        # 页面标题
        title = QLabel("高级设置")
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title)

        # 缩放设置
        scale_group = QGroupBox("缩放设置")
        scale_layout = QVBoxLayout(scale_group)

        # 同步缩放
        self.sync_scale_cb = QCheckBox("启用窗口同步缩放")
        self.sync_scale_cb.setToolTip("勾选后模型会根据窗口大小自动调整缩放")
        scale_layout.addWidget(self.sync_scale_cb)

        # 基准尺寸
        base_layout = QHBoxLayout()
        base_layout.addWidget(QLabel("基准尺寸:"))
        self.base_width_spin = QSpinBox()
        self.base_width_spin.setRange(200, 1000)
        self.base_width_spin.setSuffix(" px")
        base_layout.addWidget(QLabel("宽:"))
        base_layout.addWidget(self.base_width_spin)

        self.base_height_spin = QSpinBox()
        self.base_height_spin.setRange(200, 800)
        self.base_height_spin.setSuffix(" px")
        base_layout.addWidget(QLabel("高:"))
        base_layout.addWidget(self.base_height_spin)
        scale_layout.addLayout(base_layout)

        # 手动缩放
        manual_layout = QHBoxLayout()
        manual_layout.addWidget(QLabel("手动缩放:"))
        self.scale_slider = QSlider(Qt.Orientation.Horizontal)
        self.scale_slider.setRange(30, 300)
        self.scale_slider.setValue(100)
        self.scale_slider.valueChanged.connect(self.update_scale_label)
        manual_layout.addWidget(self.scale_slider)

        self.scale_label = QLabel("1.00")
        self.scale_label.setMinimumWidth(40)
        manual_layout.addWidget(self.scale_label)
        scale_layout.addLayout(manual_layout)

        # 缩放范围
        range_layout = QHBoxLayout()
        range_layout.addWidget(QLabel("缩放范围:"))
        self.min_scale_spin = QDoubleSpinBox()
        self.min_scale_spin.setRange(0.1, 1.0)
        self.min_scale_spin.setSingleStep(0.1)
        range_layout.addWidget(QLabel("最小:"))
        range_layout.addWidget(self.min_scale_spin)

        self.max_scale_spin = QDoubleSpinBox()
        self.max_scale_spin.setRange(1.0, 5.0)
        self.max_scale_spin.setSingleStep(0.1)
        range_layout.addWidget(QLabel("最大:"))
        range_layout.addWidget(self.max_scale_spin)
        scale_layout.addLayout(range_layout)

        layout.addWidget(scale_group)
        layout.addStretch()

        self.stacked_widget.addWidget(page)

    # 工具方法
    def update_scale_label(self):
        """更新缩放标签"""
        value = self.scale_slider.value() / 100.0
        self.scale_label.setText(f"{value:.2f}")

    def update_status(self, message: str, status_type: str = "info"):
        """更新状态显示"""
        colors = {
            "info": ("color: #2196F3; background-color: #E3F2FD; border: 1px solid #BBDEFB;", "ℹ️"),
            "success": ("color: #4CAF50; background-color: #E8F5E8; border: 1px solid #C8E6C9;", "✅"),
            "warning": ("color: #FF9800; background-color: #FFF3E0; border: 1px solid #FFCC02;", "⚠️"),
            "error": ("color: #F44336; background-color: #FFEBEE; border: 1px solid #FFCDD2;", "❌")
        }

        style, icon = colors.get(status_type, colors["info"])
        self.status_label.setText(f"{icon} {message}")
        self.status_label.setStyleSheet(f"{style} font-size: 11px; padding: 6px; border-radius: 4px;")

    def load_settings(self):
        """加载设置"""
        config = self.config_manager.config
        print(f"正在加载设置: {config}")

        # 窗口设置
        window_config = config["window"]
        self.always_on_top_cb.setChecked(window_config["always_on_top"])
        self.transparent_cb.setChecked(window_config["transparent"])
        self.position_locked_cb.setChecked(window_config.get("position_locked", False))
        self.width_spin.setValue(window_config["width"])
        self.height_spin.setValue(window_config["height"])

        # 透明模式
        transparency_mode = window_config.get("transparency_mode", TransparencyMode.QT_NATIVE)
        mode_map = {
            TransparencyMode.QT_NATIVE: 0,
            TransparencyMode.WINDOWS_API_COLORKEY: 1,
            TransparencyMode.WINDOWS_API_ALPHA: 2
        }
        self.transparency_mode_combo.setCurrentIndex(mode_map.get(transparency_mode, 0))
        self.alpha_spin.setValue(window_config.get("alpha_value", 240))

        # 模型设置
        model_config = config["model"]

        # 多模型设置
        self.multi_model_cb.setChecked(model_config.get("multi_model_enabled", False))
        self.on_multi_model_toggled(Qt.CheckState.Checked.value if model_config.get("multi_model_enabled", False) else Qt.CheckState.Unchecked.value)

        # 当前模型显示
        current_model_path = model_config.get("current_model_path", "")
        if current_model_path and os.path.exists(current_model_path):
            model_name = os.path.splitext(os.path.basename(current_model_path))[0]
            self.current_model_label.setText(model_name)
            self.current_model_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        else:
            self.current_model_label.setText("未选择模型")
            self.current_model_label.setStyleSheet("color: #666; font-style: italic;")

        # 随机动作设置
        self.random_motion_cb.setChecked(model_config["random_motion_enabled"])
        self.motion_min_spin.setValue(model_config["random_motion_interval_min"])
        self.motion_max_spin.setValue(model_config["random_motion_interval_max"])

        # 随机表情设置
        self.random_expression_cb.setChecked(model_config["random_expression_enabled"])
        self.expression_min_spin.setValue(model_config["random_expression_interval_min"])
        self.expression_max_spin.setValue(model_config["random_expression_interval_max"])

        # 高级设置
        self.sync_scale_cb.setChecked(model_config.get("sync_scale_enabled", True))
        self.base_width_spin.setValue(model_config.get("base_window_width", 400))
        self.base_height_spin.setValue(model_config.get("base_window_height", 500))
        self.scale_slider.setValue(int(model_config.get("manual_scale_factor", 1.0) * 100))
        self.min_scale_spin.setValue(model_config.get("min_scale", 0.3))
        self.max_scale_spin.setValue(model_config.get("max_scale", 3.0))

        # 初始化扫描目录
        self.init_scan_directories()

        # 加载动作和表情选择
        available_motions = model_config.get("available_motions", [])
        if available_motions:
            self.update_motion_list(available_motions)

        available_expressions = model_config.get("available_expressions", [])
        if available_expressions:
            self.update_expression_list(available_expressions)

        # 对话设置
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        self.api_url_edit.setText(api_config.get("base_url", "https://api.openai.com/v1"))
        self.api_key_edit.setText(api_config.get("api_key", ""))
        self.model_combo.setCurrentText(api_config.get("model", "gpt-3.5-turbo"))

        default_params = llm_config.get("default_params", {})
        self.temperature_spin.setValue(default_params.get("temperature", 0.7))
        self.max_tokens_spin.setValue(default_params.get("max_tokens", 1000))
        self.top_p_spin.setValue(default_params.get("top_p", 0.98))
        self.presence_penalty_spin.setValue(default_params.get("presence_penalty", 0.0))
        self.frequency_penalty_spin.setValue(default_params.get("frequency_penalty", 0.0))

        conversation_settings = llm_config.get("conversation_settings", {})
        self.system_prompt_edit.setPlainText(conversation_settings.get("system_prompt", "你是一个友好的AI助手。"))

        # 文本显示设置
        text_display_config = config.get("text_display", {})
        self.typing_speed_spin.setValue(text_display_config.get("typing_speed", 50))
        self.auto_hide_spin.setValue(text_display_config.get("auto_hide_delay", 5000))
        self.typing_animation_cb.setChecked(text_display_config.get("typing_animation", True))

        # 加载预设列表
        self.load_preset_list()

        print("设置加载完成")

    def apply_settings(self):
        """应用设置"""
        try:
            # 窗口设置
            self.config_manager.set("window", "always_on_top", self.always_on_top_cb.isChecked())
            self.config_manager.set("window", "transparent", self.transparent_cb.isChecked())
            self.config_manager.set("window", "position_locked", self.position_locked_cb.isChecked())
            self.config_manager.set("window", "width", self.width_spin.value())
            self.config_manager.set("window", "height", self.height_spin.value())

            # 透明模式
            mode_map = {
                0: TransparencyMode.QT_NATIVE,
                1: TransparencyMode.WINDOWS_API_COLORKEY,
                2: TransparencyMode.WINDOWS_API_ALPHA
            }
            transparency_mode = mode_map.get(self.transparency_mode_combo.currentIndex(), TransparencyMode.WINDOWS_API_COLORKEY)
            self.config_manager.set("window", "transparency_mode", transparency_mode)
            self.config_manager.set("window", "alpha_value", self.alpha_spin.value())

            # 保存当前窗口位置（如果父窗口存在）
            if self.parent():
                self.config_manager.set("window", "x", self.parent().x())
                self.config_manager.set("window", "y", self.parent().y())

            # 模型设置
            self.config_manager.set("model", "random_motion_enabled", self.random_motion_cb.isChecked())
            self.config_manager.set("model", "random_motion_interval_min", self.motion_min_spin.value())
            self.config_manager.set("model", "random_motion_interval_max", self.motion_max_spin.value())

            self.config_manager.set("model", "random_expression_enabled", self.random_expression_cb.isChecked())
            self.config_manager.set("model", "random_expression_interval_min", self.expression_min_spin.value())
            self.config_manager.set("model", "random_expression_interval_max", self.expression_max_spin.value())

            # 高级设置
            self.config_manager.set("model", "sync_scale_enabled", self.sync_scale_cb.isChecked())
            self.config_manager.set("model", "base_window_width", self.base_width_spin.value())
            self.config_manager.set("model", "base_window_height", self.base_height_spin.value())
            self.config_manager.set("model", "manual_scale_factor", self.scale_slider.value() / 100.0)
            self.config_manager.set("model", "min_scale", self.min_scale_spin.value())
            self.config_manager.set("model", "max_scale", self.max_scale_spin.value())

            # 扫描目录设置
            scan_dirs = []
            for i in range(self.scan_dir_combo.count()):
                scan_dirs.append(self.scan_dir_combo.itemText(i))
            self.config_manager.set("model", "scan_directories", scan_dirs)

            # 对话设置
            llm_config = {
                "api_config": {
                    "base_url": self.api_url_edit.text(),
                    "api_key": self.api_key_edit.text(),
                    "model": self.model_combo.currentText(),
                    "timeout": 30.0,
                    "max_retries": 3
                },
                "default_params": {
                    "temperature": self.temperature_spin.value(),
                    "max_tokens": self.max_tokens_spin.value(),
                    "top_p": self.top_p_spin.value(),
                    "presence_penalty": self.presence_penalty_spin.value(),
                    "frequency_penalty": self.frequency_penalty_spin.value()
                },
                "conversation_settings": {
                    "max_history_length": 20,
                    "save_history": True,
                    "system_prompt": self.system_prompt_edit.toPlainText()
                }
            }
            self.config_manager.config["llm"] = llm_config

            # 文本显示设置
            text_display_config = {
                "typing_speed": self.typing_speed_spin.value(),
                "auto_hide_delay": self.auto_hide_spin.value(),
                "max_chars_per_line": 20,
                "max_lines": 3,
                "typing_animation": self.typing_animation_cb.isChecked()
            }
            self.config_manager.config["text_display"] = text_display_config

            # 保存配置
            self.config_manager.save_config()

            # 发送配置变更信号
            self.config_changed.emit("all", "settings", "applied")

            self.update_status("设置已应用", "success")
            print("设置已应用并保存")

        except Exception as e:
            self.update_status(f"应用设置失败: {e}", "error")
            print(f"应用设置失败: {e}")

    def reset_settings(self):
        """重置设置"""
        try:
            # 重置为默认配置
            self.config_manager.config = self.config_manager.load_default_config()
            self.config_manager.save_config()

            # 重新加载界面
            self.load_settings()

            # 发送配置变更信号
            self.config_changed.emit("all", "settings", "reset")

            self.update_status("设置已重置为默认值", "warning")
            print("设置已重置为默认值")

        except Exception as e:
            self.update_status(f"重置设置失败: {e}", "error")
            print(f"重置设置失败: {e}")

    def browse_scan_directory(self):
        """浏览扫描目录"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "选择模型扫描目录",
            self.scan_dir_combo.currentText() if self.scan_dir_combo.currentText() else os.getcwd()
        )

        if directory:
            # 检查是否已存在
            for i in range(self.scan_dir_combo.count()):
                if self.scan_dir_combo.itemText(i) == directory:
                    self.scan_dir_combo.setCurrentIndex(i)
                    return

            # 添加新目录
            self.scan_dir_combo.addItem(directory)
            self.scan_dir_combo.setCurrentText(directory)
            self.update_status(f"已选择目录: {directory}", "info")

    def scan_models(self):
        """扫描模型"""
        if self.scan_thread and self.scan_thread.isRunning():
            print("扫描正在进行中...")
            return

        scan_dir = self.scan_dir_combo.currentText()
        if not scan_dir or not os.path.exists(scan_dir):
            self.update_status("请选择有效的扫描目录", "error")
            return

        # 显示进度条
        self.scan_progress.setVisible(True)
        self.scan_progress.setRange(0, 0)  # 不确定进度
        self.scan_models_btn.setEnabled(False)
        self.update_status("正在扫描模型文件...", "info")

        # 启动扫描线程
        self.scan_thread = ModelScanThread([scan_dir], recursive=True)
        self.scan_thread.scan_progress.connect(self.on_scan_progress)
        self.scan_thread.scan_finished.connect(self.on_scan_finished)
        self.scan_thread.scan_error.connect(self.on_scan_error)
        self.scan_thread.start()

    def init_scan_directories(self):
        """初始化扫描目录"""
        # 添加默认扫描目录
        default_dirs = [
            os.path.join(os.path.dirname(__file__), "..", "examples", "resources"),
            os.path.join(os.path.dirname(__file__), "..", "models"),
            "models",
            "resources"
        ]

        # 从配置中获取扫描目录
        config_dirs = self.config_manager.get("model", "scan_directories", [])

        # 合并并去重
        all_dirs = list(set(default_dirs + config_dirs))

        # 只添加存在的目录
        for directory in all_dirs:
            if os.path.exists(directory):
                self.scan_dir_combo.addItem(directory)

        # 如果没有有效目录，添加当前目录
        if self.scan_dir_combo.count() == 0:
            self.scan_dir_combo.addItem(os.getcwd())

    def on_scan_progress(self, current, total):
        """扫描进度更新"""
        if total > 0:
            self.scan_progress.setRange(0, total)
            self.scan_progress.setValue(current)

    def on_scan_finished(self, models):
        """扫描完成"""
        self.available_models = models
        self.update_model_list()

        # 同步到多模型列表
        if self.multi_model_cb.isChecked():
            self.sync_multi_model_list()

        # 隐藏进度条
        self.scan_progress.setVisible(False)
        self.scan_models_btn.setEnabled(True)

        valid_count = len([m for m in models if m.is_valid])
        self.update_status(f"扫描完成: 找到 {len(models)} 个模型文件，其中 {valid_count} 个有效", "success")

        # 保存扫描目录到配置
        scan_dir = self.scan_dir_combo.currentText()
        config_dirs = self.config_manager.get("model", "scan_directories", [])
        if scan_dir not in config_dirs:
            config_dirs.append(scan_dir)
            self.config_manager.set("model", "scan_directories", config_dirs)

    def on_scan_error(self, error_message):
        """扫描错误"""
        self.scan_progress.setVisible(False)
        self.scan_models_btn.setEnabled(True)
        self.update_status(f"扫描失败: {error_message}", "error")

    def update_model_list(self):
        """更新模型列表"""
        self.model_list.clear()

        for model in self.available_models:
            if model.is_valid:
                item = QListWidgetItem(f"✓ {model.name}")
                item.setToolTip(f"路径: {model.path}\n大小: {model.size} 字节")
                item.setData(Qt.ItemDataRole.UserRole, model)
                self.model_list.addItem(item)
            else:
                item = QListWidgetItem(f"✗ {model.name} (错误)")
                item.setToolTip(f"路径: {model.path}\n错误: {model.error_message}")
                item.setData(Qt.ItemDataRole.UserRole, model)
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEnabled)  # 禁用无效模型
                self.model_list.addItem(item)

    def scan_motions(self):
        """扫描动作"""
        # 这里可以扩展为从当前模型扫描动作，目前使用示例数据
        motions = ["Idle", "TapBody", "TapHead", "Shake", "Flick", "PinchIn", "PinchOut"]
        self.update_motion_list(motions)
        self.update_status(f"找到 {len(motions)} 个动作", "success")

    def scan_expressions(self):
        """扫描表情"""
        # 这里可以扩展为从当前模型扫描表情，目前使用示例数据
        # 使用索引作为表情ID，这样可以避免类型错误
        expressions = ["0", "1", "2", "3", "4", "5"]  # 表情索引
        expression_names = ["默认", "开心", "悲伤", "愤怒", "惊讶", "眨眼"]  # 表情名称

        # 直接使用表情索引
        self.update_expression_list(expressions, expression_names)
        self.update_status(f"找到 {len(expressions)} 个表情", "success")

    def on_model_selected(self, item):
        """模型选择"""
        model = item.data(Qt.ItemDataRole.UserRole)
        if model and model.is_valid:
            self.config_manager.set("model", "current_model_path", model.path)
            self.current_model_label.setText(model.name)
            self.current_model_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            self.update_status(f"已选择模型: {model.name}", "success")
            # 发送配置变更信号
            self.config_changed.emit("model", "current_model_path", model.path)

    def on_multi_model_toggled(self, state):
        """多模型模式切换"""
        enabled = state == Qt.CheckState.Checked.value
        self.multi_model_list.setEnabled(enabled)
        self.load_selected_models_btn.setEnabled(enabled)
        self.close_all_models_btn.setEnabled(enabled)

        # 保存配置
        self.config_manager.set("model", "multi_model_enabled", enabled)

        if enabled:
            # 同步模型列表到多模型列表
            self.sync_multi_model_list()
            self.update_status("多模型模式已启用", "info")
        else:
            self.update_status("多模型模式已禁用", "info")

    def sync_multi_model_list(self):
        """同步模型列表到多模型选择列表"""
        self.multi_model_list.clear()

        # 使用available_models而不是从单模型列表复制
        if hasattr(self, 'available_models'):
            for model in self.available_models:
                if model.is_valid:
                    multi_item = QListWidgetItem(f"🎭 {model.name}")
                    multi_item.setData(Qt.ItemDataRole.UserRole, model)
                    multi_item.setFlags(multi_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
                    multi_item.setCheckState(Qt.CheckState.Unchecked)
                    self.multi_model_list.addItem(multi_item)

        print(f"同步多模型列表完成，共 {self.multi_model_list.count()} 个模型")

    def load_selected_models(self):
        """加载选中的多个模型"""
        selected_models = []

        for i in range(self.multi_model_list.count()):
            item = self.multi_model_list.item(i)
            if item.checkState() == Qt.CheckState.Checked:
                model = item.data(Qt.ItemDataRole.UserRole)
                if model and model.is_valid:
                    selected_models.append(model.path)

        if not selected_models:
            self.update_status("请先选择要加载的模型", "warning")
            return

        # 保存选中的模型列表
        self.config_manager.set("model", "selected_models", selected_models)

        # 发送加载多模型信号
        self.load_multiple_models_requested.emit(selected_models)

        self.update_status(f"正在加载 {len(selected_models)} 个模型...", "info")

    def close_all_models(self):
        """关闭所有模型窗口"""
        self.close_all_models_requested.emit()
        self.update_status("已发送关闭所有模型窗口的请求", "info")

    def update_motion_list(self, motions):
        """更新动作列表"""
        self.motion_list.clear()

        # 获取当前选中的动作
        selected_motions = self.config_manager.get("model", "selected_motions", [])

        for motion in motions:
            item = QListWidgetItem(f"🎭 {motion}")
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)

            # 设置选中状态
            if motion in selected_motions:
                item.setCheckState(Qt.CheckState.Checked)
            else:
                item.setCheckState(Qt.CheckState.Unchecked)

            item.setData(Qt.ItemDataRole.UserRole, motion)
            self.motion_list.addItem(item)

        # 保存可用动作列表
        self.config_manager.set("model", "available_motions", motions)

    def update_expression_list(self, expressions, expression_names=None):
        """更新表情列表"""
        self.expression_list.clear()

        # 获取当前选中的表情
        selected_expressions = self.config_manager.get("model", "selected_expressions", [])

        for i, expression in enumerate(expressions):
            # 如果有名称列表，使用名称显示，否则使用表情ID
            if expression_names and i < len(expression_names):
                display_name = f"😊 {expression_names[i]} (索引{expression})"
            else:
                display_name = f"😊 表情{expression}"

            item = QListWidgetItem(display_name)
            item.setFlags(item.flags() | Qt.ItemFlag.ItemIsUserCheckable)

            # 设置选中状态
            if expression in selected_expressions:
                item.setCheckState(Qt.CheckState.Checked)
            else:
                item.setCheckState(Qt.CheckState.Unchecked)

            item.setData(Qt.ItemDataRole.UserRole, expression)
            self.expression_list.addItem(item)

        # 保存可用表情列表
        self.config_manager.set("model", "available_expressions", expressions)

    def on_motion_selection_changed(self, item):
        """动作选择变更"""
        motion = item.data(Qt.ItemDataRole.UserRole)
        selected_motions = self.config_manager.get("model", "selected_motions", [])

        if item.checkState() == Qt.CheckState.Checked:
            if motion not in selected_motions:
                selected_motions.append(motion)
        else:
            if motion in selected_motions:
                selected_motions.remove(motion)

        # 保存选择
        self.config_manager.set("model", "selected_motions", selected_motions)
        self.update_status(f"已选择 {len(selected_motions)} 个动作", "info")

    def on_expression_selection_changed(self, item):
        """表情选择变更"""
        expression = item.data(Qt.ItemDataRole.UserRole)
        selected_expressions = self.config_manager.get("model", "selected_expressions", [])

        if item.checkState() == Qt.CheckState.Checked:
            if expression not in selected_expressions:
                selected_expressions.append(expression)
        else:
            if expression in selected_expressions:
                selected_expressions.remove(expression)

        # 保存选择
        self.config_manager.set("model", "selected_expressions", selected_expressions)
        self.update_status(f"已选择 {len(selected_expressions)} 个表情", "info")

    def connect_signals(self):
        """连接信号"""
        # 连接变更检测信号
        self.always_on_top_cb.toggled.connect(lambda: self.update_status("窗口置顶设置已修改", "info"))
        self.transparent_cb.toggled.connect(lambda: self.update_status("透明模式设置已修改", "info"))
        self.width_spin.valueChanged.connect(lambda: self.update_status("窗口宽度已修改", "info"))
        self.height_spin.valueChanged.connect(lambda: self.update_status("窗口高度已修改", "info"))

        self.random_motion_cb.toggled.connect(lambda: self.update_status("随机动作设置已修改", "info"))
        self.motion_min_spin.valueChanged.connect(lambda: self.update_status("动作间隔已修改", "info"))
        self.motion_max_spin.valueChanged.connect(lambda: self.update_status("动作间隔已修改", "info"))

        self.random_expression_cb.toggled.connect(lambda: self.update_status("随机表情设置已修改", "info"))
        self.expression_min_spin.valueChanged.connect(lambda: self.update_status("表情间隔已修改", "info"))
        self.expression_max_spin.valueChanged.connect(lambda: self.update_status("表情间隔已修改", "info"))

        self.sync_scale_cb.toggled.connect(lambda: self.update_status("同步缩放设置已修改", "info"))
        self.base_width_spin.valueChanged.connect(lambda: self.update_status("基准尺寸已修改", "info"))
        self.base_height_spin.valueChanged.connect(lambda: self.update_status("基准尺寸已修改", "info"))
        self.min_scale_spin.valueChanged.connect(lambda: self.update_status("缩放范围已修改", "info"))
        self.max_scale_spin.valueChanged.connect(lambda: self.update_status("缩放范围已修改", "info"))

    def play_manual_motion(self):
        """播放手动动作"""
        self.play_motion_requested.emit("TapBody", 0)
        self.update_status("播放手动动作", "info")

    def play_random_motion(self):
        """播放随机动作"""
        self.play_motion_requested.emit("Idle", -1)
        self.update_status("播放随机动作", "info")

    def play_manual_expression(self):
        """播放手动表情"""
        # 获取选中的表情，如果没有选中则使用默认
        selected_expressions = self.config_manager.get("model", "selected_expressions", [])
        if selected_expressions:
            # 使用第一个选中的表情
            expression = selected_expressions[0]
        else:
            expression = "0"  # 使用索引0作为默认表情

        self.play_expression_requested.emit(expression)
        self.update_status(f"播放表情: {expression}", "info")

    def play_random_expression(self):
        """播放随机表情"""
        self.play_expression_requested.emit("random")
        self.update_status("播放随机表情", "info")

    def reset_model_pose(self):
        """重置模型姿态"""
        self.reset_pose_requested.emit()
        self.update_status("模型姿态已重置", "success")

    def reset_model_expression(self):
        """重置模型表情"""
        self.reset_expression_requested.emit()
        self.update_status("模型表情已重置", "success")

    # 预设管理相关方法
    def load_preset_list(self):
        """加载预设列表"""
        self.preset_list.clear()
        presets = self.config_manager.config.get("llm_presets", {})

        for preset_id, preset_data in presets.items():
            name = preset_data.get("name", preset_id)
            item = QListWidgetItem(name)
            item.setData(Qt.ItemDataRole.UserRole, preset_id)
            self.preset_list.addItem(item)

        # 选择第一个预设（如果存在）
        if self.preset_list.count() > 0:
            self.preset_list.setCurrentRow(0)
            self.load_preset_to_ui(list(presets.keys())[0])

    def on_preset_selection_changed(self):
        """预设选择变更"""
        current_item = self.preset_list.currentItem()
        if current_item:
            preset_id = current_item.data(Qt.ItemDataRole.UserRole)
            self.load_preset_to_ui(preset_id)

    def load_preset_to_ui(self, preset_id):
        """将预设加载到UI"""
        presets = self.config_manager.config.get("llm_presets", {})
        preset = presets.get(preset_id, {})

        if preset:
            # 加载API配置
            api_config = preset.get("api_config", {})
            if "base_url" in api_config:
                self.api_url_edit.setText(api_config["base_url"])
            if "model" in api_config:
                self.model_combo.setCurrentText(api_config["model"])
            if "temperature" in api_config:
                self.temperature_spin.setValue(api_config["temperature"])
            if "max_tokens" in api_config:
                self.max_tokens_spin.setValue(api_config["max_tokens"])

            # 加载系统提示词
            if "system_prompt" in preset:
                self.system_prompt_edit.setPlainText(preset["system_prompt"])

    def add_preset(self):
        """添加预设"""
        from PySide6.QtWidgets import QInputDialog

        name, ok = QInputDialog.getText(self, "添加预设", "请输入预设名称:")
        if ok and name.strip():
            preset_id = name.strip().replace(" ", "_").lower()

            # 检查是否已存在
            presets = self.config_manager.config.get("llm_presets", {})
            if preset_id in presets:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "该预设名称已存在！")
                return

            # 创建新预设
            new_preset = {
                "name": name.strip(),
                "system_prompt": self.system_prompt_edit.toPlainText(),
                "api_config": {
                    "base_url": self.api_url_edit.text(),
                    "model": self.model_combo.currentText(),
                    "temperature": self.temperature_spin.value(),
                    "max_tokens": self.max_tokens_spin.value()
                }
            }

            # 保存预设
            presets[preset_id] = new_preset
            self.config_manager.config["llm_presets"] = presets
            self.config_manager.save_config()

            # 刷新列表
            self.load_preset_list()
            self.update_status(f"预设 '{name}' 已添加", "success")

    def edit_preset(self):
        """编辑预设"""
        current_item = self.preset_list.currentItem()
        if not current_item:
            self.update_status("请先选择要编辑的预设", "warning")
            return

        preset_id = current_item.data(Qt.ItemDataRole.UserRole)
        presets = self.config_manager.config.get("llm_presets", {})

        if preset_id in presets:
            # 更新预设数据
            presets[preset_id].update({
                "system_prompt": self.system_prompt_edit.toPlainText(),
                "api_config": {
                    "base_url": self.api_url_edit.text(),
                    "model": self.model_combo.currentText(),
                    "temperature": self.temperature_spin.value(),
                    "max_tokens": self.max_tokens_spin.value()
                }
            })

            # 保存配置
            self.config_manager.config["llm_presets"] = presets
            self.config_manager.save_config()

            self.update_status(f"预设 '{presets[preset_id]['name']}' 已更新", "success")

    def delete_preset(self):
        """删除预设"""
        current_item = self.preset_list.currentItem()
        if not current_item:
            self.update_status("请先选择要删除的预设", "warning")
            return

        preset_id = current_item.data(Qt.ItemDataRole.UserRole)

        # 不允许删除默认预设
        if preset_id == "default":
            self.update_status("不能删除默认预设", "warning")
            return

        from PySide6.QtWidgets import QMessageBox
        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除预设 '{current_item.text()}' 吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            presets = self.config_manager.config.get("llm_presets", {})
            if preset_id in presets:
                del presets[preset_id]
                self.config_manager.config["llm_presets"] = presets
                self.config_manager.save_config()

                # 刷新列表
                self.load_preset_list()
                self.update_status(f"预设已删除", "success")

    def test_api_connection(self):
        """测试API连接"""
        self.test_api_btn.setEnabled(False)
        self.test_api_btn.setText("🔄 测试中...")
        self.api_status_label.setText("正在测试...")
        self.api_status_label.setStyleSheet("color: #007acc;")

        # 获取当前UI中的配置值
        api_config = {
            "base_url": self.api_url_edit.text().strip(),
            "api_key": self.api_key_edit.text().strip(),
            "model": self.model_combo.currentText().strip(),
            "timeout": 10.0,
            "max_retries": 1
        }

        print(f"开始API测试，配置: {api_config}")

        # 创建API测试工作线程
        self.api_test_worker = APITestWorker(api_config)
        self.api_test_worker.test_success.connect(self.on_api_test_success)
        self.api_test_worker.test_failed.connect(self.on_api_test_failed)
        self.api_test_worker.start()

    def on_api_test_success(self):
        """API测试成功"""
        self.test_api_btn.setEnabled(True)
        self.test_api_btn.setText("🔗 测试API连接")
        self.api_status_label.setText("✅ 连接成功")
        self.api_status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        self.update_status("API连接测试成功", "success")

    def on_api_test_failed(self, error):
        """API测试失败"""
        self.test_api_btn.setEnabled(True)
        self.test_api_btn.setText("🔗 测试API连接")
        self.api_status_label.setText(f"❌ 连接失败")
        self.api_status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        self.update_status(f"API连接测试失败: {error}", "error")

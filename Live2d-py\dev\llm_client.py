#!/usr/bin/env python3
"""
LLM客户端模块
为Live2D桌面宠物提供LLM对话功能
基于OpenAI API实现
"""

import json
import os
import time
from datetime import datetime
from typing import Optional, Dict, Any, List, Callable
try:
    from openai import OpenAI
except ImportError:
    try:
        # 兼容旧版本的openai库
        import openai
        OpenAI = openai.OpenAI if hasattr(openai, 'OpenAI') else None
    except ImportError:
        OpenAI = None


class LLMClient:
    """LLM客户端类，处理与OpenAI API的交互"""
    
    def __init__(self, config_manager=None):
        """初始化LLM客户端"""
        self.config_manager = config_manager
        self.openai_client = None
        self.conversation_history = []
        self.current_system_prompt = None
        self.default_system_prompt = "你是一个友好的AI助手。"
        
        # 初始化配置
        self._init_config()
        self._init_openai_client()
    
    def _init_config(self):
        """初始化配置"""
        if self.config_manager:
            # 从配置管理器获取配置
            llm_config = self.config_manager.config.get("llm", {})
        else:
            # 使用默认配置
            llm_config = {}
        
        # 设置默认值
        self.api_config = llm_config.get("api_config", {
            "base_url": "https://api.openai.com/v1",
            "api_key": "",
            "model": "gpt-3.5-turbo",
            "timeout": 30.0,
            "max_retries": 3
        })
        
        self.default_params = llm_config.get("default_params", {
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 1.0,
            "presence_penalty": 0,
            "frequency_penalty": 0
        })
        
        self.conversation_settings = llm_config.get("conversation_settings", {
            "max_history_length": 20,
            "save_history": True,
            "system_prompt": self.default_system_prompt
        })
        
        self.current_system_prompt = self.conversation_settings.get("system_prompt", self.default_system_prompt)
    
    def _init_openai_client(self):
        """初始化OpenAI客户端"""
        if OpenAI is None:
            raise ImportError("OpenAI库未安装，请安装: pip install openai>=1.0.0")
        
        if not self.api_config.get("api_key"):
            print("⚠️ 警告: OpenAI API密钥未设置")
            return
        
        try:
            self.openai_client = OpenAI(
                base_url=self.api_config["base_url"],
                api_key=self.api_config["api_key"],
                timeout=self.api_config.get("timeout", 30.0)
            )
            print("✅ OpenAI客户端初始化成功")
        except Exception as e:
            print(f"❌ OpenAI客户端初始化失败: {e}")
            self.openai_client = None
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        if "api_config" in new_config:
            self.api_config.update(new_config["api_config"])
            # 重新初始化客户端
            self._init_openai_client()
        
        if "default_params" in new_config:
            self.default_params.update(new_config["default_params"])
        
        if "conversation_settings" in new_config:
            self.conversation_settings.update(new_config["conversation_settings"])
            if "system_prompt" in new_config["conversation_settings"]:
                self.current_system_prompt = new_config["conversation_settings"]["system_prompt"]
        
        # 保存到配置管理器
        if self.config_manager:
            self.config_manager.config["llm"] = {
                "api_config": self.api_config,
                "default_params": self.default_params,
                "conversation_settings": self.conversation_settings
            }
            self.config_manager.save_config()
    
    def set_system_prompt(self, prompt: str):
        """设置系统提示词"""
        self.current_system_prompt = prompt
        if self.config_manager:
            if "llm" not in self.config_manager.config:
                self.config_manager.config["llm"] = {}
            if "conversation_settings" not in self.config_manager.config["llm"]:
                self.config_manager.config["llm"]["conversation_settings"] = {}
            self.config_manager.config["llm"]["conversation_settings"]["system_prompt"] = prompt
            self.config_manager.save_config()
    
    def get_system_prompt(self) -> str:
        """获取当前系统提示词"""
        return self.current_system_prompt or self.default_system_prompt
    
    def add_message(self, role: str, content: str):
        """添加消息到对话历史"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        self.conversation_history.append(message)
        
        # 限制历史长度
        max_length = self.conversation_settings.get("max_history_length", 20)
        if len(self.conversation_history) > max_length:
            # 保留系统消息，删除最旧的用户/助手消息
            system_messages = [msg for msg in self.conversation_history if msg["role"] == "system"]
            other_messages = [msg for msg in self.conversation_history if msg["role"] != "system"]
            other_messages = other_messages[-(max_length - len(system_messages)):]
            self.conversation_history = system_messages + other_messages
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
    
    def get_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def _prepare_messages(self, user_message: str) -> List[Dict[str, str]]:
        """准备发送给API的消息列表"""
        messages = []
        
        # 添加系统消息
        if self.current_system_prompt:
            messages.append({
                "role": "system",
                "content": self.current_system_prompt
            })
        
        # 添加历史消息（只包含role和content）
        for msg in self.conversation_history:
            if msg["role"] in ["user", "assistant"]:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        # 添加当前用户消息
        messages.append({
            "role": "user",
            "content": user_message
        })
        
        return messages

    def _extract_response_content(self, response) -> Optional[str]:
        """从API响应中提取内容，参考成功的处理逻辑"""
        try:
            # 打印响应结构用于调试
            print(f"API响应类型: {type(response)}")
            print(f"API响应内容: {response}")

            # 使用与成功代码相同的检查逻辑
            if (response and response.choices and
                response.choices[0].message and
                response.choices[0].message.content):
                return response.choices[0].message.content

            # 如果标准路径失败，尝试其他可能的格式
            if hasattr(response, 'choices') and response.choices:
                choice = response.choices[0]

                # 检查finish_reason
                if hasattr(choice, 'finish_reason'):
                    print(f"finish_reason: {choice.finish_reason}")
                    if choice.finish_reason == 'length':
                        print("⚠️ 响应因长度限制被截断，可能需要增加max_tokens")

                # 尝试其他可能的属性
                if hasattr(choice, 'message') and choice.message:
                    if hasattr(choice.message, 'text'):
                        return choice.message.text

                if hasattr(choice, 'text'):
                    return choice.text
                if hasattr(choice, 'content'):
                    return choice.content

            # 如果是字典格式
            if isinstance(response, dict):
                # 尝试标准路径
                if 'choices' in response and response['choices']:
                    choice = response['choices'][0]
                    if 'message' in choice and choice['message']:
                        if 'content' in choice['message']:
                            return choice['message']['content']
                        if 'text' in choice['message']:
                            return choice['message']['text']

                    # 尝试直接从choice获取
                    if 'text' in choice:
                        return choice['text']
                    if 'content' in choice:
                        return choice['content']

            # 如果是字符串，直接返回
            if isinstance(response, str):
                return response

            print(f"⚠️ 无法从响应中提取内容: {response}")
            return None

        except Exception as e:
            print(f"❌ 提取响应内容时出错: {e}")
            return None

    async def chat_async(self, user_message: str, **kwargs) -> Optional[str]:
        """异步发送消息并获取回复"""
        if not self.openai_client:
            return "❌ OpenAI客户端未初始化，请检查API配置"
        
        try:
            # 准备消息
            messages = self._prepare_messages(user_message)
            
            # 合并参数
            params = self.default_params.copy()
            params.update(kwargs)
            
            # 发送请求
            response = await self.openai_client.chat.completions.create(
                model=self.api_config["model"],
                messages=messages,
                **params
            )
            
            # 提取回复 - 增强兼容性处理
            assistant_message = self._extract_response_content(response)

            if assistant_message:
                # 添加到历史
                self.add_message("user", user_message)
                self.add_message("assistant", assistant_message)
                return assistant_message
            else:
                return "❌ API返回了空响应"
            
        except Exception as e:
            error_msg = f"❌ API调用失败: {str(e)}"
            print(error_msg)
            return error_msg
    
    def chat(self, user_message: str, **kwargs) -> Optional[str]:
        """同步发送消息并获取回复"""
        if not self.openai_client:
            return "❌ OpenAI客户端未初始化，请检查API配置"
        
        try:
            # 准备消息
            messages = self._prepare_messages(user_message)
            
            # 合并参数
            params = self.default_params.copy()
            params.update(kwargs)
            
            # 发送请求
            response = self.openai_client.chat.completions.create(
                model=self.api_config["model"],
                messages=messages,
                **params
            )
            
            # 提取回复 - 增强兼容性处理
            assistant_message = self._extract_response_content(response)

            if assistant_message:
                # 添加到历史
                self.add_message("user", user_message)
                self.add_message("assistant", assistant_message)
                return assistant_message
            else:
                return "❌ API返回了空响应"
            
        except Exception as e:
            error_msg = f"❌ API调用失败: {str(e)}"
            print(error_msg)
            return error_msg
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取当前配置字典"""
        return {
            "api_config": self.api_config,
            "default_params": self.default_params,
            "conversation_settings": self.conversation_settings
        }
    
    def is_configured(self) -> bool:
        """检查是否已正确配置"""
        return (self.openai_client is not None and 
                bool(self.api_config.get("api_key")) and
                bool(self.api_config.get("model")))


class PresetManager:
    """预设管理器，管理不同的对话预设"""
    
    def __init__(self, config_manager=None):
        """初始化预设管理器"""
        self.config_manager = config_manager
        self.presets = {}
        self.current_preset = "default"
        self._load_presets()
    
    def _load_presets(self):
        """加载预设"""
        if self.config_manager:
            self.presets = self.config_manager.config.get("llm_presets", {})
        
        # 确保有默认预设
        if "default" not in self.presets:
            self.presets["default"] = {
                "name": "默认预设",
                "system_prompt": "你是一个友好的AI助手。",
                "api_config": {
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 1000
                }
            }
    
    def save_presets(self):
        """保存预设到配置"""
        if self.config_manager:
            self.config_manager.config["llm_presets"] = self.presets
            self.config_manager.save_config()
    
    def add_preset(self, preset_id: str, preset_data: Dict[str, Any]):
        """添加预设"""
        self.presets[preset_id] = preset_data
        self.save_presets()
    
    def remove_preset(self, preset_id: str):
        """删除预设"""
        if preset_id in self.presets and preset_id != "default":
            del self.presets[preset_id]
            self.save_presets()
    
    def get_preset(self, preset_id: str) -> Optional[Dict[str, Any]]:
        """获取预设"""
        return self.presets.get(preset_id)
    
    def get_all_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取所有预设"""
        return self.presets.copy()
    
    def set_current_preset(self, preset_id: str):
        """设置当前预设"""
        if preset_id in self.presets:
            self.current_preset = preset_id
    
    def get_current_preset(self) -> Dict[str, Any]:
        """获取当前预设"""
        return self.presets.get(self.current_preset, self.presets["default"])

SYSTEM_PROMPT = """你是一个智能文件操作助手，可以帮助用户进行各种文件和目录操作。

你的能力包括：
1. 读取文件内容
2. 写入或追加文件内容
3. 创建目录
4. 删除文件或目录
5. 列出目录内容
6. 复制文件
7. 移动/重命名文件
8. 获取文件信息

使用指南：
- 在执行任何文件操作前，请确认用户的意图
- 对于删除操作，请特别谨慎并再次确认
- 支持相对路径和绝对路径
- 会自动创建必要的父目录
- 支持常见的文本文件格式

请用中文回复用户，并提供清晰的操作结果反馈。如果操作失败，请说明具体原因。

当用户询问文件操作相关问题时，你应该：
1. 理解用户需求
2. 选择合适的工具
3. 执行操作
4. 反馈结果

请始终保持友好和专业的态度。"""

# 文件操作相关的提示词
FILE_OPERATION_PROMPTS = {
    "read_file": "正在读取文件: {file_path}",
    "write_file": "正在写入文件: {file_path}",
    "create_directory": "正在创建目录: {dir_path}",
    "delete_file": "正在删除: {file_path}",
    "list_directory": "正在列出目录内容: {dir_path}",
    "copy_file": "正在复制文件: {source_path} -> {dest_path}",
    "move_file": "正在移动文件: {source_path} -> {dest_path}",
    "get_file_info": "正在获取文件信息: {file_path}"
}

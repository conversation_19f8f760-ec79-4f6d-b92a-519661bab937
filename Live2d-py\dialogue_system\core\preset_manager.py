#!/usr/bin/env python3
"""
预设管理器模块
为Live2D桌面宠物提供对话预设管理功能
"""

from typing import Optional, Dict, Any


class PresetManager:
    """预设管理器，管理不同的对话预设"""
    
    def __init__(self, config_manager=None):
        """初始化预设管理器"""
        self.config_manager = config_manager
        self.presets = {}
        self.current_preset = "default"
        self._load_presets()
    
    def _load_presets(self):
        """加载预设"""
        if self.config_manager:
            self.presets = self.config_manager.config.get("llm_presets", {})
        
        # 确保有默认预设
        if "default" not in self.presets:
            self.presets["default"] = {
                "name": "默认预设",
                "system_prompt": "你是一个友好的AI助手。",
                "api_config": {
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 1000
                }
            }
    
    def save_presets(self):
        """保存预设到配置"""
        if self.config_manager:
            self.config_manager.config["llm_presets"] = self.presets
            self.config_manager.save_config()
    
    def add_preset(self, preset_id: str, preset_data: Dict[str, Any]):
        """添加预设"""
        self.presets[preset_id] = preset_data
        self.save_presets()
    
    def remove_preset(self, preset_id: str):
        """删除预设"""
        if preset_id in self.presets and preset_id != "default":
            del self.presets[preset_id]
            self.save_presets()
    
    def get_preset(self, preset_id: str) -> Optional[Dict[str, Any]]:
        """获取预设"""
        return self.presets.get(preset_id)
    
    def get_all_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取所有预设"""
        return self.presets.copy()
    
    def set_current_preset(self, preset_id: str):
        """设置当前预设"""
        if preset_id in self.presets:
            self.current_preset = preset_id
    
    def get_current_preset(self) -> Dict[str, Any]:
        """获取当前预设，确保包含完整的API配置"""
        preset = self.presets.get(self.current_preset, self.presets["default"]).copy()

        # 确保预设包含API配置
        if "api_config" not in preset or not preset["api_config"]:
            if self.config_manager:
                global_config = self.config_manager.config.get("llm", {})
                preset["api_config"] = global_config.get("api_config", {})
                print(f"🔍 为预设 {self.current_preset} 补充API配置")

        return preset

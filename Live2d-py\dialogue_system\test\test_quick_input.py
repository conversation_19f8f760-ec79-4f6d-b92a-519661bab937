#!/usr/bin/env python3
"""
测试快速输入栏功能的简单脚本
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'dev'))

from settings_dialog import ConfigManager
from dialogue_system.ui.text_overlay import TextDisplayManager


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.init_ui()
        self.init_text_manager()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("快速输入栏测试")
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        self.quick_input_btn = QPushButton("显示快速输入栏")
        self.quick_input_btn.clicked.connect(self.show_quick_input)
        layout.addWidget(self.quick_input_btn)
        
        self.test_message_btn = QPushButton("显示测试消息")
        self.test_message_btn.clicked.connect(self.show_test_message)
        layout.addWidget(self.test_message_btn)
    
    def init_text_manager(self):
        """初始化文本管理器"""
        self.text_display_manager = TextDisplayManager(self)
        self.text_display_manager.connect_signals(
            message_handler=self.on_message_received,
            preset_handler=self.on_preset_changed
        )
    
    def show_quick_input(self):
        """显示快速输入栏"""
        print("显示快速输入栏")
        self.text_display_manager.show_quick_input()
    
    def show_test_message(self):
        """显示测试消息"""
        self.text_display_manager.show_message("这是一个测试消息，用来验证文本显示功能是否正常工作。")
    
    def on_message_received(self, message: str):
        """处理接收到的消息"""
        print(f"收到消息: {message}")
        # 显示回复
        self.text_display_manager.show_message(f"收到您的消息: {message}")
    
    def on_preset_changed(self, preset_id: str):
        """处理预设切换"""
        print(f"预设切换到: {preset_id}")
        self.text_display_manager.show_message(f"预设已切换到: {preset_id}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = TestMainWindow()
    window.show()
    
    print("快速输入栏测试窗口已启动")
    print("点击按钮测试功能:")
    print("- 显示快速输入栏: 测试快速输入功能")
    print("- 显示测试消息: 测试文本显示功能")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

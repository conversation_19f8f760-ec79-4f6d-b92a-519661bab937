#!/usr/bin/env python3
"""
聊天历史管理器
管理多个聊天会话的保存、加载、删除功能
"""

import json
import os
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional


class ChatSession:
    """聊天会话类"""
    
    def __init__(self, session_id: str = None, title: str = None, preset_id: str = "default"):
        self.session_id = session_id or str(uuid.uuid4())
        self.title = title or f"聊天 {datetime.now().strftime('%m-%d %H:%M')}"
        self.preset_id = preset_id
        self.created_time = datetime.now().isoformat()
        self.updated_time = self.created_time
        self.messages: List[Dict[str, Any]] = []
    
    def add_message(self, role: str, content: str):
        """添加消息到会话"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        self.messages.append(message)
        self.updated_time = datetime.now().isoformat()
    
    def get_messages(self) -> List[Dict[str, Any]]:
        """获取会话消息"""
        return self.messages.copy()
    
    def set_title(self, title: str):
        """设置会话标题"""
        self.title = title
        self.updated_time = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "title": self.title,
            "preset_id": self.preset_id,
            "created_time": self.created_time,
            "updated_time": self.updated_time,
            "messages": self.messages
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChatSession':
        """从字典创建会话"""
        session = cls(
            session_id=data.get("session_id"),
            title=data.get("title"),
            preset_id=data.get("preset_id", "default")
        )
        session.created_time = data.get("created_time", session.created_time)
        session.updated_time = data.get("updated_time", session.updated_time)
        session.messages = data.get("messages", [])
        return session


class ChatHistoryManager:
    """聊天历史管理器"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.history_dir = "chat_history"
        self.current_session: Optional[ChatSession] = None
        self.sessions: Dict[str, ChatSession] = {}
        
        # 确保历史目录存在
        os.makedirs(self.history_dir, exist_ok=True)
        
        # 加载现有会话
        self.load_all_sessions()
    
    def create_new_session(self, title: str = None, preset_id: str = "default") -> ChatSession:
        """创建新的聊天会话"""
        session = ChatSession(title=title, preset_id=preset_id)
        self.sessions[session.session_id] = session
        self.current_session = session
        self.save_session(session)
        return session
    
    def get_current_session(self) -> Optional[ChatSession]:
        """获取当前会话"""
        return self.current_session
    
    def set_current_session(self, session_id: str) -> bool:
        """设置当前会话"""
        if session_id in self.sessions:
            self.current_session = self.sessions[session_id]
            return True
        return False
    
    def add_message_to_current(self, role: str, content: str):
        """添加消息到当前会话"""
        if not self.current_session:
            self.create_new_session()
        
        self.current_session.add_message(role, content)
        self.save_session(self.current_session)
    
    def get_current_messages(self) -> List[Dict[str, Any]]:
        """获取当前会话的消息"""
        if self.current_session:
            return self.current_session.get_messages()
        return []
    
    def save_session(self, session: ChatSession):
        """保存会话到文件"""
        try:
            file_path = os.path.join(self.history_dir, f"{session.session_id}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(session.to_dict(), f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存会话失败: {e}")
    
    def load_session(self, session_id: str) -> Optional[ChatSession]:
        """从文件加载会话"""
        try:
            file_path = os.path.join(self.history_dir, f"{session_id}.json")
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                session = ChatSession.from_dict(data)
                self.sessions[session_id] = session
                return session
        except Exception as e:
            print(f"加载会话失败: {e}")
        return None
    
    def load_all_sessions(self):
        """加载所有会话"""
        try:
            if not os.path.exists(self.history_dir):
                return
            
            for filename in os.listdir(self.history_dir):
                if filename.endswith('.json'):
                    session_id = filename[:-5]  # 移除.json后缀
                    self.load_session(session_id)
        except Exception as e:
            print(f"加载所有会话失败: {e}")
    
    def delete_session(self, session_id: str) -> bool:
        """删除会话"""
        try:
            # 从内存中删除
            if session_id in self.sessions:
                del self.sessions[session_id]
            
            # 如果是当前会话，清空当前会话
            if self.current_session and self.current_session.session_id == session_id:
                self.current_session = None
            
            # 从文件系统删除
            file_path = os.path.join(self.history_dir, f"{session_id}.json")
            if os.path.exists(file_path):
                os.remove(file_path)
            
            return True
        except Exception as e:
            print(f"删除会话失败: {e}")
            return False
    
    def get_all_sessions(self) -> List[ChatSession]:
        """获取所有会话，按更新时间排序"""
        sessions = list(self.sessions.values())
        sessions.sort(key=lambda x: x.updated_time, reverse=True)
        return sessions
    
    def get_session_summary(self, session: ChatSession) -> str:
        """获取会话摘要"""
        if not session.messages:
            return "空会话"
        
        # 获取第一条用户消息作为摘要
        for msg in session.messages:
            if msg["role"] == "user":
                content = msg["content"]
                if len(content) > 30:
                    return content[:30] + "..."
                return content
        
        return "无用户消息"
    
    def auto_generate_title(self, session: ChatSession):
        """自动生成会话标题"""
        if session.messages:
            summary = self.get_session_summary(session)
            if summary != "空会话" and summary != "无用户消息":
                session.set_title(summary)
                self.save_session(session)
    
    def clear_current_session(self):
        """清空当前会话（不删除，只清空消息）"""
        if self.current_session:
            self.current_session.messages = []
            self.current_session.updated_time = datetime.now().isoformat()
            self.save_session(self.current_session)
    
    def get_session_count(self) -> int:
        """获取会话总数"""
        return len(self.sessions)
    
    def cleanup_old_sessions(self, max_sessions: int = 100):
        """清理旧会话，保留最新的指定数量"""
        if len(self.sessions) <= max_sessions:
            return
        
        sessions = self.get_all_sessions()
        sessions_to_delete = sessions[max_sessions:]
        
        for session in sessions_to_delete:
            self.delete_session(session.session_id)

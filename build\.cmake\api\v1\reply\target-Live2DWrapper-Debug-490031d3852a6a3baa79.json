{"artifacts": [{"path": "Debug/Live2DWrapper.dll"}, {"path": "Debug/Live2DWrapper.lib"}, {"path": "Debug/Live2DWrapper.pdb"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "add_link_options", "target_link_libraries", "add_compile_options"], "files": ["cmake/Wrapper.cmake", "CMakeLists.txt", "Live2D/cmake/Main.cmake", "Live2D/cmake/Live2D.cmake", "Live2D/cmake/Framework.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 38, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 3, "parent": 2}, {"command": 2, "file": 1, "line": 18, "parent": 0}, {"command": 2, "file": 1, "line": 20, "parent": 0}, {"command": 3, "file": 0, "line": 24, "parent": 2}, {"command": 1, "file": 1, "line": 29, "parent": 0}, {"file": 3, "parent": 7}, {"command": 1, "file": 3, "line": 12, "parent": 8}, {"file": 2, "parent": 9}, {"command": 3, "file": 2, "line": 27, "parent": 10}, {"command": 1, "file": 3, "line": 11, "parent": 8}, {"file": 4, "parent": 12}, {"command": 3, "file": 4, "line": 17, "parent": 13}, {"command": 3, "file": 4, "line": 22, "parent": 13}, {"command": 4, "file": 1, "line": 17, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Ob0 /Od /RTC1 -MDd -Zi"}, {"backtrace": 16, "fragment": "/utf-8"}, {"backtrace": 16, "fragment": "/wd4018"}, {"backtrace": 16, "fragment": "/wd4244"}, {"backtrace": 16, "fragment": "/wd4996"}], "defines": [{"backtrace": 6, "define": "CSM_TARGET_WIN_GL"}, {"define": "Live2DWrapper_EXPORTS"}, {"backtrace": 6, "define": "Py_NO_LINK_LIB"}], "includes": [{"backtrace": 6, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Main/src"}, {"backtrace": 6, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/src"}, {"backtrace": 6, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Glad/include"}, {"backtrace": 6, "isSystem": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include"}, {"backtrace": 6, "isSystem": true, "path": "E:/Program Files/python/include"}], "language": "CXX", "sourceIndexes": [1, 3, 5]}], "dependencies": [{"backtrace": 6, "id": "glad::@fceb1f27c7c18f638612"}, {"backtrace": 6, "id": "Framework::@6c5cde19a88560770850"}, {"backtrace": 6, "id": "Main::@2c116edd35e6c53877a5"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "Live2DWrapper::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/machine:x64 /debug /INCREMENTAL", "role": "flags"}, {"backtrace": 4, "fragment": "/NODEFAULTLIB:LIBCMT", "role": "flags"}, {"backtrace": 5, "fragment": "/BASE:0x800000000", "role": "flags"}, {"backtrace": 6, "fragment": "Live2D\\Main\\src\\Debug\\Main.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "\"E:\\Program Files\\python\\libs\\python3.lib\"", "role": "libraries"}, {"backtrace": 11, "fragment": "Live2D\\Framework\\Debug\\Framework.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "D:\\BaiduNetdiskDownload\\SOVITS\\LLM对话\\Live2d-py\\Live2D\\Core\\lib\\windows\\x86_64\\143\\Live2DCubismCore_MT.lib", "role": "libraries"}, {"backtrace": 15, "fragment": "Live2D\\Glad\\Debug\\glad.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "opengl32.lib", "role": "libraries"}, {"backtrace": 11, "fragment": "glu32.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "Live2DWrapper", "nameOnDisk": "Live2DWrapper.dll", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 2, 4]}, {"name": "Source Files", "sourceIndexes": [1, 3, 5]}], "sources": [{"backtrace": 3, "path": "Wrapper/Python.hpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Wrapper/Live2D.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Wrapper/PyLAppModel.hpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Wrapper/PyLAppModel.cpp", "sourceGroupIndex": 1}, {"backtrace": 3, "path": "Wrapper/PyModel.hpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Wrapper/PyModel.cpp", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}
#!/usr/bin/env python3
"""
测试快速输入栏功能的简单脚本
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import Qt

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from settings_dialog import ConfigManager
from text_overlay import TextDisplayManager


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.init_ui()
        self.init_text_manager()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("快速输入栏测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        self.test_btn = QPushButton("显示快速输入栏")
        self.test_btn.clicked.connect(self.show_quick_input)
        layout.addWidget(self.test_btn)
        
        # 消息显示按钮
        self.msg_btn = QPushButton("显示测试消息")
        self.msg_btn.clicked.connect(self.show_test_message)
        layout.addWidget(self.msg_btn)
    
    def init_text_manager(self):
        """初始化文本管理器"""
        self.text_display_manager = TextDisplayManager(self)
        self.text_display_manager.connect_signals(
            message_handler=self.on_message_received,
            preset_handler=self.on_preset_changed
        )
    
    def show_quick_input(self):
        """显示快速输入栏"""
        print("显示快速输入栏")
        self.text_display_manager.show_quick_input()
    
    def show_test_message(self):
        """显示测试消息"""
        self.text_display_manager.show_message("这是一个测试消息，用来验证文本显示功能是否正常工作。")
    
    def on_message_received(self, message: str):
        """处理接收到的消息"""
        print(f"收到消息: {message}")
        # 显示回复
        self.text_display_manager.show_message(f"收到您的消息: {message}")
    
    def on_preset_changed(self, preset_id: str):
        """处理预设切换"""
        print(f"预设切换到: {preset_id}")
        presets = self.config_manager.config.get("llm_presets", {})
        preset = presets.get(preset_id, {})
        preset_name = preset.get("name", preset_id)
        self.text_display_manager.show_message(f"已切换到预设: {preset_name}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("快速输入栏测试程序启动")
    print("点击按钮测试功能")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
增强型MCP代理
集成文件操作、天气查询、时间查询功能
"""

import asyncio
import json
import os
from typing import Dict, List, Any, Optional

from clients.llm_client import LLMClient
from multi_mcp_client import MultiMCPClient
from config import Config

class EnhancedAgent:
    def __init__(self):
        self.llm_client = LLMClient()
        self.mcp_client = MultiMCPClient()
        self.conversation_history = []
        self.is_connected = False
        
    async def initialize(self):
        """初始化所有MCP服务"""
        try:
            script_dir = os.path.dirname(__file__)
            
            # 连接所有MCP服务器
            servers = [
                ("file", os.path.join(script_dir, "file_operations_mcp.py")),
                ("weather", os.path.join(script_dir, "weather_mcp.py")),
                ("time", os.path.join(script_dir, "time_mcp.py"))
            ]
            
            connected_count = 0
            for name, script_path in servers:
                if await self.mcp_client.add_server(name, script_path):
                    connected_count += 1
            
            self.is_connected = connected_count > 0
            
            if self.is_connected:
                print(f"✅ 成功连接 {connected_count}/{len(servers)} 个MCP服务")
                await self._ensure_work_directory()
            else:
                print("❌ 所有MCP服务连接失败")
                
        except Exception as e:
            print(f"❌ 初始化失败: {str(e)}")
            self.is_connected = False
    
    async def _ensure_work_directory(self):
        """确保工作目录存在"""
        try:
            if not os.path.exists(Config.DEFAULT_WORK_DIR):
                await self.mcp_client.write_file(f"{Config.DEFAULT_WORK_DIR}/.gitkeep", "")
                print(f"📁 创建工作目录: {Config.DEFAULT_WORK_DIR}")
        except Exception as e:
            print(f"⚠️ 创建工作目录失败: {str(e)}")
    
    def _get_all_functions(self) -> List[Dict[str, Any]]:
        """获取所有可用函数定义"""
        if not self.is_connected:
            return []
        
        functions = []
        
        # 文件操作函数
        functions.extend([
            {
                "name": "read_file",
                "description": "读取文件内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "文件路径"}
                    },
                    "required": ["file_path"]
                }
            },
            {
                "name": "write_file",
                "description": "写入文件内容（仅限workspace目录）",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "file_path": {"type": "string", "description": "文件路径（建议使用workspace/文件名）"},
                        "content": {"type": "string", "description": "文件内容"},
                        "mode": {"type": "string", "enum": ["write", "append"], "default": "write"}
                    },
                    "required": ["file_path", "content"]
                }
            },
            {
                "name": "list_directory",
                "description": "列出目录内容",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "dir_path": {"type": "string", "description": "目录路径"}
                    },
                    "required": ["dir_path"]
                }
            }
        ])
        
        # 天气查询函数
        functions.append({
            "name": "get_weather",
            "description": "查询指定城市的当前天气",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {"type": "string", "description": "城市名称（支持中文）"}
                },
                "required": ["city"]
            }
        })
        
        # 时间查询函数
        functions.extend([
            {
                "name": "get_current_time",
                "description": "获取当前系统时间",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "format_type": {
                            "type": "string",
                            "enum": ["simple", "detailed", "timestamp"],
                            "default": "detailed"
                        }
                    }
                }
            },
            {
                "name": "get_time_info",
                "description": "获取时间相关信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "info_type": {
                            "type": "string",
                            "enum": ["period", "weekday", "season"],
                            "default": "period"
                        }
                    }
                }
            }
        ])
        
        return functions
    
    async def _execute_function(self, function_name: str, arguments: Dict[str, Any]) -> str:
        """执行函数调用"""
        if not self.is_connected:
            return "❌ MCP服务未连接"
        
        try:
            # 映射函数名到MCP工具名
            tool_mapping = {
                "read_file": "file_read_file",
                "write_file": "file_write_file",
                "list_directory": "file_list_directory",
                "get_weather": "weather_get_weather",
                "get_current_time": "time_get_current_time",
                "get_time_info": "time_get_time_info"
            }
            
            tool_name = tool_mapping.get(function_name)
            if not tool_name:
                return f"❌ 未知函数: {function_name}"
            
            # 调用MCP工具
            result = await self.mcp_client.call_tool(tool_name, arguments)
            
            if result["success"]:
                return result["content"][0]["text"] if result["content"] else "操作完成"
            else:
                error_msg = result["content"][0]["text"] if result["content"] else "未知错误"
                return f"❌ 操作失败: {error_msg}"
                
        except Exception as e:
            return f"❌ 执行函数时出错: {str(e)}"
    
    async def process_message(self, user_message: str) -> str:
        """处理用户消息"""
        try:
            self.conversation_history.append({"role": "user", "content": user_message})
            
            # 获取系统提示
            system_prompt = """你是一个智能助手，具备以下能力：
1. 文件操作：读取、写入、列出目录等
2. 天气查询：查询任意城市的当前天气
3. 时间查询：获取当前时间和时间相关信息

请根据用户需求选择合适的工具来完成任务。回复要简洁明了。"""
            
            messages = [{"role": "system", "content": system_prompt}] + self.conversation_history[-10:]
            functions = self._get_all_functions()
            
            response = self.llm_client.chat(
                messages=messages,
                functions=functions if functions else None
            )
            
            message = response.choices[0].message
            
            if hasattr(message, 'tool_calls') and message.tool_calls:
                tool_results = []
                
                for tool_call in message.tool_calls:
                    function_name = tool_call.function.name
                    arguments = json.loads(tool_call.function.arguments)
                    
                    result = await self._execute_function(function_name, arguments)
                    tool_results.append(result)
                
                # 添加工具调用到历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": message.content or "",
                    "tool_calls": [
                        {
                            "id": tc.id,
                            "type": "function",
                            "function": {
                                "name": tc.function.name,
                                "arguments": tc.function.arguments
                            }
                        } for tc in message.tool_calls
                    ]
                })
                
                for i, tool_call in enumerate(message.tool_calls):
                    self.conversation_history.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": tool_results[i]
                    })
                
                # 生成最终回复
                final_response = self.llm_client.chat(
                    messages=[{"role": "system", "content": system_prompt}] + self.conversation_history[-15:]
                )
                
                final_message = final_response.choices[0].message.content
                self.conversation_history.append({"role": "assistant", "content": final_message})
                
                return final_message
            else:
                assistant_message = message.content
                self.conversation_history.append({"role": "assistant", "content": assistant_message})
                return assistant_message
                
        except Exception as e:
            error_msg = f"❌ 处理消息时出错: {str(e)}"
            print(error_msg)
            return error_msg
    
    async def cleanup(self):
        """清理资源"""
        if self.mcp_client:
            await self.mcp_client.disconnect_all()
            print("🔌 所有MCP连接已断开")

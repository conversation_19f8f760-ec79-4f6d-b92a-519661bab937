﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppAllocator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppDefine.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppPal.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppTextureManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppModel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\Log.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\MatrixManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\fine-grained\Model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppAllocator.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppDefine.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppPal.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppTextureManager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppModel.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\Log.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\MatrixManager.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\HackProperties.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{14911F07-99C3-39DE-BF82-34B27EA68283}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{B46CFFE0-6E5C-3087-8EC9-5FBF391A73FA}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

#!/usr/bin/env python3
"""
多服务器MCP客户端
支持同时连接多个MCP服务器
"""

import asyncio
import json
import sys
from typing import Any, Dict, List, Optional
from mcp_client import MCPClient

class MultiMCPClient:
    def __init__(self):
        self.servers = {}
        self.all_tools = {}

    async def add_server(self, name: str, script_path: str):
        """添加MCP服务器"""
        client = MCPClient(script_path)
        if await client.connect():
            self.servers[name] = client
            # 收集所有工具，添加服务器前缀
            for tool in client.get_available_tools():
                tool_name = f"{name}_{tool['name']}"
                self.all_tools[tool_name] = {
                    'server': name,
                    'original_name': tool['name'],
                    'tool': tool
                }
            print(f"✅ 成功连接到 {name} 服务器")
            return True
        else:
            print(f"❌ 连接到 {name} 服务器失败")
            return False

    def get_available_tools(self) -> Dict[str, Any]:
        """获取所有可用工具"""
        return self.all_tools

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用工具"""
        if tool_name not in self.all_tools:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"未知工具: {tool_name}"}]
            }
        
        tool_info = self.all_tools[tool_name]
        server_name = tool_info['server']
        original_name = tool_info['original_name']
        
        if server_name not in self.servers:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"服务器 {server_name} 未连接"}]
            }
        
        return await self.servers[server_name].call_tool(original_name, arguments)

    async def get_weather(self, city: str) -> str:
        """获取天气信息"""
        result = await self.call_tool("weather_get_weather", {"city": city})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"天气查询失败: {result}")

    async def get_current_time(self, format_type: str = "detailed") -> str:
        """获取当前时间"""
        result = await self.call_tool("time_get_current_time", {"format_type": format_type})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"时间查询失败: {result}")

    async def get_time_info(self, info_type: str = "period") -> str:
        """获取时间信息"""
        result = await self.call_tool("time_get_time_info", {"info_type": info_type})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"时间信息查询失败: {result}")

    async def read_file(self, file_path: str) -> str:
        """读取文件"""
        result = await self.call_tool("file_read_file", {"file_path": file_path})
        if result["success"] and result["content"]:
            return result["content"][0]["text"]
        else:
            raise Exception(f"读取文件失败: {result}")

    async def write_file(self, file_path: str, content: str, mode: str = "write") -> bool:
        """写入文件"""
        result = await self.call_tool("file_write_file", {
            "file_path": file_path,
            "content": content,
            "mode": mode
        })
        return result["success"]

    async def disconnect_all(self):
        """断开所有连接"""
        for client in self.servers.values():
            await client.disconnect()
        self.servers.clear()
        self.all_tools.clear()

# 使用示例
async def test_multi_mcp():
    """测试多服务器MCP客户端"""
    client = MultiMCPClient()
    
    try:
        # 连接所有服务器
        await client.add_server("file", "file_operations_mcp.py")
        await client.add_server("weather", "weather_mcp.py")
        await client.add_server("time", "time_mcp.py")
        
        print(f"\n📋 可用工具: {list(client.get_available_tools().keys())}")
        
        # 测试时间查询
        print("\n⏰ 测试时间查询:")
        time_info = await client.get_current_time()
        print(time_info)
        
        # 测试时间段信息
        print("\n🌅 测试时间段信息:")
        period_info = await client.get_time_info("period")
        print(period_info)
        
        # 测试天气查询
        print("\n🌤️ 测试天气查询:")
        weather_info = await client.get_weather("北京")
        print(weather_info)
        
        # 测试文件操作
        print("\n📁 测试文件操作:")
        await client.write_file("test_multi.txt", f"多服务器测试\n时间: {await client.get_current_time('simple')}")
        content = await client.read_file("test_multi.txt")
        print(f"文件内容: {content}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    finally:
        await client.disconnect_all()

if __name__ == "__main__":
    asyncio.run(test_multi_mcp())

#!/usr/bin/env python3
"""
对话界面窗口
为Live2D桌面宠物提供独立的文字对话界面
"""

import sys
import threading
from datetime import datetime
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit,
                            QLineEdit, QPushButton, QScrollArea, QWidget,
                            QLabel, QComboBox, QFrame, QSplitter, QMessageBox)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, Slot
from PySide6.QtGui import QFont, QTextCursor, QPixmap, QPalette

from .llm_client import LLMClient
from .preset_manager import PresetManager


class ChatWorker(QThread):
    """聊天工作线程，避免UI阻塞"""
    response_received = Signal(str)
    error_occurred = Signal(str)
    
    def __init__(self, llm_client, message):
        super().__init__()
        self.llm_client = llm_client
        self.message = message
    
    def run(self):
        try:
            response = self.llm_client.chat(self.message)
            if response:
                self.response_received.emit(response)
            else:
                self.error_occurred.emit("未收到回复")
        except Exception as e:
            self.error_occurred.emit(f"发送失败: {str(e)}")


class ChatDialog(QDialog):
    """对话界面窗口"""
    
    # 信号：当收到回复时发送给主窗口显示
    message_received = Signal(str)
    
    def __init__(self, config_manager=None, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.llm_client = LLMClient(config_manager)
        self.preset_manager = PresetManager(config_manager)
        self.chat_worker = None
        
        self.init_ui()
        self.load_presets()
        
        # 连接信号
        self.message_received.connect(self.on_message_for_display)

        # 初始化时更新配置
        self.refresh_config()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("💬 AI对话")
        self.setGeometry(100, 100, 500, 600)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint | Qt.WindowType.WindowMinimizeButtonHint)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 顶部控制栏
        self.create_control_bar(main_layout)
        
        # 对话显示区域
        self.create_chat_area(main_layout)
        
        # 输入区域
        self.create_input_area(main_layout)

        # 设置样式
        self.set_styles()

        # 在所有UI组件创建完成后更新状态
        self.update_status()
    
    def create_control_bar(self, parent_layout):
        """创建顶部控制栏"""
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(10)
        
        # 预设选择
        preset_label = QLabel("预设:")
        self.preset_combo = QComboBox()
        self.preset_combo.setMinimumWidth(150)
        self.preset_combo.currentTextChanged.connect(self.on_preset_changed)
        
        # 状态标签
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        
        # 功能按钮
        self.regenerate_btn = QPushButton("🔄 重新生成")
        self.regenerate_btn.clicked.connect(self.regenerate_response)
        self.regenerate_btn.setMaximumWidth(100)

        self.continue_btn = QPushButton("➡️ 继续")
        self.continue_btn.clicked.connect(self.continue_conversation)
        self.continue_btn.setMaximumWidth(80)

        self.new_chat_btn = QPushButton("🆕 新聊天")
        self.new_chat_btn.clicked.connect(self.start_new_chat)
        self.new_chat_btn.setMaximumWidth(80)

        self.history_btn = QPushButton("📂 历史")
        self.history_btn.clicked.connect(self.show_history)
        self.history_btn.setMaximumWidth(80)

        self.clear_btn = QPushButton("🗑️ 清空")
        self.clear_btn.clicked.connect(self.clear_history)
        self.clear_btn.setMaximumWidth(80)

        control_layout.addWidget(preset_label)
        control_layout.addWidget(self.preset_combo)
        control_layout.addStretch()
        control_layout.addWidget(self.status_label)
        control_layout.addWidget(self.regenerate_btn)
        control_layout.addWidget(self.continue_btn)
        control_layout.addWidget(self.new_chat_btn)
        control_layout.addWidget(self.history_btn)
        control_layout.addWidget(self.clear_btn)
        
        parent_layout.addWidget(control_frame)
    
    def create_chat_area(self, parent_layout):
        """创建对话显示区域"""
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 聊天容器
        self.chat_widget = QWidget()
        self.chat_layout = QVBoxLayout(self.chat_widget)
        self.chat_layout.setContentsMargins(5, 5, 5, 5)
        self.chat_layout.setSpacing(10)
        self.chat_layout.addStretch()  # 在底部添加弹性空间
        
        self.scroll_area.setWidget(self.chat_widget)
        parent_layout.addWidget(self.scroll_area)
    
    def create_input_area(self, parent_layout):
        """创建输入区域"""
        input_frame = QFrame()
        input_layout = QVBoxLayout(input_frame)
        input_layout.setContentsMargins(0, 0, 0, 0)
        input_layout.setSpacing(5)
        
        # 输入框
        self.input_edit = QTextEdit()
        self.input_edit.setMaximumHeight(100)
        self.input_edit.setPlaceholderText("输入消息... (Ctrl+Enter发送)")
        self.input_edit.installEventFilter(self)
        
        # 按钮行
        button_layout = QHBoxLayout()
        
        self.send_btn = QPushButton("📤 发送")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setDefault(True)
        
        button_layout.addStretch()
        button_layout.addWidget(self.send_btn)
        
        input_layout.addWidget(self.input_edit)
        input_layout.addLayout(button_layout)
        
        parent_layout.addWidget(input_frame)

    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                font-size: 14px;
            }
            QTextEdit:focus {
                border-color: #007acc;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:pressed {
                background-color: #004578;
            }
            QComboBox {
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
                font-size: 14px;
            }
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }
        """)

    def load_presets(self):
        """加载预设列表"""
        self.preset_combo.clear()
        presets = self.preset_manager.get_all_presets()
        
        for preset_id, preset_data in presets.items():
            name = preset_data.get("name", preset_id)
            model = preset_data.get("api_config", {}).get("model", "")
            display_text = f"{name}"
            if model:
                display_text += f" ({model})"
            
            self.preset_combo.addItem(display_text, preset_id)
        
        # 设置当前预设
        current_preset = self.preset_manager.current_preset
        for i in range(self.preset_combo.count()):
            if self.preset_combo.itemData(i) == current_preset:
                self.preset_combo.setCurrentIndex(i)
                break

    def update_status(self):
        """更新状态显示"""
        if self.llm_client.is_configured():
            model = self.llm_client.api_config.get("model", "未知")
            self.status_label.setText(f"状态: 已连接 ({model})")
            self.status_label.setStyleSheet("color: #28a745; font-size: 12px;")
        else:
            self.status_label.setText("状态: 未配置")
            self.status_label.setStyleSheet("color: #dc3545; font-size: 12px;")
    
    def on_preset_changed(self):
        """预设改变时的处理"""
        current_data = self.preset_combo.currentData()
        if current_data:
            self.preset_manager.set_current_preset(current_data)
            preset = self.preset_manager.get_current_preset()
            
            # 更新LLM客户端配置
            if preset:
                config_update = {}
                if "system_prompt" in preset:
                    config_update["conversation_settings"] = {"system_prompt": preset["system_prompt"]}
                if "api_config" in preset:
                    config_update["api_config"] = preset["api_config"]
                
                if config_update:
                    self.llm_client.update_config(config_update)
            
            self.update_status()
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        QTimer.singleShot(100, lambda: self.scroll_area.verticalScrollBar().setValue(
            self.scroll_area.verticalScrollBar().maximum()
        ))
    
    def add_message_bubble(self, message: str, is_user: bool = True):
        """添加消息气泡"""
        bubble_frame = QFrame()
        bubble_layout = QHBoxLayout(bubble_frame)
        bubble_layout.setContentsMargins(0, 0, 0, 0)
        
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        message_label.setMaximumWidth(350)
        
        if is_user:
            # 用户消息 - 右对齐，蓝色
            message_label.setStyleSheet("""
                QLabel {
                    background-color: #007acc;
                    color: white;
                    border-radius: 10px;
                    padding: 8px 12px;
                    font-size: 14px;
                }
            """)
            bubble_layout.addStretch()
            bubble_layout.addWidget(message_label)
        else:
            # AI消息 - 左对齐，灰色
            message_label.setStyleSheet("""
                QLabel {
                    background-color: #e9ecef;
                    color: #333;
                    border-radius: 10px;
                    padding: 8px 12px;
                    font-size: 14px;
                }
            """)
            bubble_layout.addWidget(message_label)
            bubble_layout.addStretch()
        
        # 插入到stretch之前
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, bubble_frame)
        self.scroll_to_bottom()
    
    def send_message(self):
        """发送消息"""
        message = self.input_edit.toPlainText().strip()
        if not message:
            return
        
        if not self.llm_client.is_configured():
            QMessageBox.warning(self, "配置错误", "请先在设置中配置API信息")
            return
        
        # 清空输入框
        self.input_edit.clear()
        
        # 添加用户消息气泡
        self.add_message_bubble(message, is_user=True)
        
        # 禁用发送按钮
        if hasattr(self, 'send_btn'):
            self.send_btn.setEnabled(False)
            self.send_btn.setText("🔄 发送中...")
        
        # 创建工作线程
        self.chat_worker = ChatWorker(self.llm_client, message)
        self.chat_worker.response_received.connect(self.on_response_received)
        self.chat_worker.error_occurred.connect(self.on_error_occurred)
        self.chat_worker.finished.connect(self.on_chat_finished)
        self.chat_worker.start()
    
    @Slot(str)
    def on_response_received(self, response):
        """收到AI回复"""
        # 添加AI消息气泡
        self.add_message_bubble(response, is_user=False)

        # 发送信号给主窗口显示
        self.message_received.emit(response)

    @Slot(str)
    def on_error_occurred(self, error):
        """发生错误"""
        self.add_message_bubble(f"❌ {error}", is_user=False)
    
    def on_chat_finished(self):
        """聊天完成"""
        if hasattr(self, 'send_btn'):
            self.send_btn.setEnabled(True)
            self.send_btn.setText("📤 发送")
        self.update_status()
    
    def clear_history(self):
        """清空对话历史"""
        reply = QMessageBox.question(self, "确认", "确定要清空对话历史吗？", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            # 清空LLM历史
            self.llm_client.clear_history()
            
            # 清空UI显示
            for i in reversed(range(self.chat_layout.count() - 1)):  # 保留最后的stretch
                child = self.chat_layout.itemAt(i).widget()
                if child:
                    child.deleteLater()
    
    @Slot(str)
    def on_message_for_display(self, message):
        """处理要在主窗口显示的消息"""
        # 这个方法可以被主窗口连接，用于在Live2D模型上显示消息
        # 目前暂时不需要处理，消息会通过其他方式显示
        _ = message  # 避免未使用参数的警告
    
    def eventFilter(self, obj, event):
        """事件过滤器，处理快捷键"""
        if obj == self.input_edit and event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.send_message()
                return True
        return super().eventFilter(obj, event)
    
    def refresh_config(self):
        """刷新配置"""
        if self.config_manager:
            # 重新初始化LLM客户端和预设管理器
            self.llm_client = LLMClient(self.config_manager)
            self.preset_manager = PresetManager(self.config_manager)

            # 重新加载预设列表
            self.load_presets()

            # 更新状态
            self.update_status()

    def regenerate_response(self):
        """重新生成最后一条回复"""
        if not self.llm_client.is_configured():
            QMessageBox.warning(self, "配置错误", "请先在设置中配置API信息")
            return

        # 移除最后一条AI消息气泡
        if self.chat_layout.count() > 1:  # 确保不是只有stretch
            last_item = self.chat_layout.itemAt(self.chat_layout.count() - 2)
            if last_item and last_item.widget():
                last_item.widget().deleteLater()

        # 禁用按钮
        self.regenerate_btn.setEnabled(False)
        self.regenerate_btn.setText("🔄 生成中...")

        # 重新生成回复
        response = self.llm_client.regenerate_last_response()
        if response and not response.startswith("❌"):
            self.add_message_bubble(response, is_user=False)
        else:
            self.add_message_bubble(f"❌ 重新生成失败: {response}", is_user=False)

        # 恢复按钮
        self.regenerate_btn.setEnabled(True)
        self.regenerate_btn.setText("🔄 重新生成")

    def continue_conversation(self):
        """继续对话"""
        if not self.llm_client.is_configured():
            QMessageBox.warning(self, "配置错误", "请先在设置中配置API信息")
            return

        # 禁用按钮
        self.continue_btn.setEnabled(False)
        self.continue_btn.setText("➡️ 继续中...")

        # 继续对话
        response = self.llm_client.continue_conversation()
        if response and not response.startswith("❌"):
            self.add_message_bubble(response, is_user=False)
        else:
            self.add_message_bubble(f"❌ 继续对话失败: {response}", is_user=False)

        # 恢复按钮
        self.continue_btn.setEnabled(True)
        self.continue_btn.setText("➡️ 继续")

    def start_new_chat(self):
        """开始新聊天"""
        reply = QMessageBox.question(self, "确认", "确定要开始新的聊天吗？当前对话将被保存。",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            # 开始新聊天
            self.llm_client.start_new_chat()

            # 清空UI显示
            for i in reversed(range(self.chat_layout.count() - 1)):  # 保留最后的stretch
                child = self.chat_layout.itemAt(i).widget()
                if child:
                    child.deleteLater()

            # 更新窗口标题
            session_info = self.llm_client.get_current_session_info()
            if session_info:
                self.setWindowTitle(f"💬 AI对话 - {session_info['title']}")

    def show_history(self):
        """显示历史聊天"""
        from .chat_history_dialog import ChatHistoryDialog

        history_dialog = ChatHistoryDialog(self.llm_client, self)
        if history_dialog.exec() == QDialog.DialogCode.Accepted:
            # 如果用户选择了一个会话，重新加载界面
            self.reload_current_session()

    def reload_current_session(self):
        """重新加载当前会话"""
        # 清空UI显示
        for i in reversed(range(self.chat_layout.count() - 1)):  # 保留最后的stretch
            child = self.chat_layout.itemAt(i).widget()
            if child:
                child.deleteLater()

        # 重新显示消息
        messages = self.llm_client.get_history()
        for msg in messages:
            if msg["role"] == "user":
                self.add_message_bubble(msg["content"], is_user=True)
            elif msg["role"] == "assistant":
                self.add_message_bubble(msg["content"], is_user=False)

        # 更新窗口标题
        session_info = self.llm_client.get_current_session_info()
        if session_info:
            self.setWindowTitle(f"💬 AI对话 - {session_info['title']}")

    def closeEvent(self, event):
        """关闭事件"""
        if self.chat_worker and self.chat_worker.isRunning():
            self.chat_worker.terminate()
            self.chat_worker.wait()
        event.accept()

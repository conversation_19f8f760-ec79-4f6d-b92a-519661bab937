#!/usr/bin/env python3
"""
增强型MCP助手主程序
支持文件操作、天气查询、时间查询
"""

import asyncio
import sys
from enhanced_agent import EnhancedAgent

class EnhancedMCPApp:
    def __init__(self):
        self.agent = EnhancedAgent()
        self.running = False
    
    async def start(self):
        """启动应用"""
        print("🚀 启动增强型MCP助手...")
        
        # 初始化代理
        await self.agent.initialize()
        
        if not self.agent.is_connected:
            print("❌ 无法连接到MCP服务，程序退出")
            return
        
        self.running = True
        print("\n✅ 增强型MCP助手已启动！")
        print("📋 可用功能：")
        print("   📁 文件操作：读取、写入、列出目录等")
        print("   🌤️ 天气查询：查询任意城市天气")
        print("   ⏰ 时间查询：获取当前时间和时间信息")
        print("\n💡 输入 'quit' 或 'exit' 退出程序")
        print("=" * 50)
        
        # 主循环
        await self.main_loop()
    
    async def main_loop(self):
        """主循环"""
        while self.running:
            try:
                # 获取用户输入
                user_input = await self.get_user_input()
                
                if not user_input.strip():
                    continue
                
                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                    print("👋 再见！")
                    break
                
                # 处理用户消息
                print("🤖 正在处理...")
                response = await self.agent.process_message(user_input)
                
                # 显示回复
                print(f"\n🤖 助手: {response}")
                print("-" * 50)
                
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，正在退出...")
                break
            except Exception as e:
                print(f"\n❌ 发生错误: {str(e)}")
                print("-" * 50)
        
        # 清理资源
        await self.cleanup()
    
    async def get_user_input(self):
        """异步获取用户输入"""
        print("\n👤 你: ", end="", flush=True)
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, input)
    
    async def cleanup(self):
        """清理资源"""
        self.running = False
        if self.agent:
            await self.agent.cleanup()

async def main():
    """主函数"""
    app = EnhancedMCPApp()
    try:
        await app.start()
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
    finally:
        await app.cleanup()

if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())

﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{EA391908-60B0-348C-868B-C31757462A8A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Framework</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Framework.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Framework</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Framework.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Framework</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Framework.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Framework</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Framework.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Framework</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CSM_TARGET_WIN_GL;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\..\Core\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Framework/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Framework/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Framework/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Framework/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismCdiJson.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismCdiJson.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismDefaultParameterId.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismDefaultParameterId.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismFramework.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismFramework.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismFrameworkConfig.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismModelSettingJson.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismModelSettingJson.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\CubismJsonHolder.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\ICubismAllocator.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\ICubismModelSetting.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Live2DCubismCore.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CubismBreath.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CubismBreath.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CubismEyeBlink.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CubismEyeBlink.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CubismPose.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Effect\CubismPose.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CubismId.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CubismId.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CubismIdManager.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Id\CubismIdManager.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismMath.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismMath.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismMatrix44.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismMatrix44.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismModelMatrix.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismModelMatrix.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismTargetPoint.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismTargetPoint.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismVector2.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismVector2.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismViewMatrix.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Math\CubismViewMatrix.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismMoc.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismMoc.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismModel.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismModel.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismModelUserData.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismModelUserData.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismModelUserDataJson.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismModelUserDataJson.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismUserModel.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Model\CubismUserModel.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\ACubismMotion.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\ACubismMotion.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismExpressionMotion.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismExpressionMotion.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismExpressionMotionManager.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismExpressionMotionManager.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotion.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotion.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionInternal.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionJson.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionJson.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionManager.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionManager.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionQueueEntry.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionQueueEntry.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionQueueManager.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Motion\CubismMotionQueueManager.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CubismPhysics.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CubismPhysics.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CubismPhysicsInternal.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CubismPhysicsJson.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Physics\CubismPhysicsJson.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CubismRenderer.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CubismRenderer.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CubismClippingManager.hpp" />
    <None Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\CubismClippingManager.tpp">
    </None>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CubismOffscreenSurface_OpenGLES2.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CubismOffscreenSurface_OpenGLES2.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CubismShader_OpenGLES2.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CubismShader_OpenGLES2.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CubismRenderer_OpenGLES2.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Rendering\OpenGL\CubismRenderer_OpenGLES2.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\csmMap.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\csmRectF.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\csmRectF.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\csmString.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\csmString.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\csmVector.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Type\CubismBasicType.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CubismDebug.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CubismDebug.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CubismJson.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CubismJson.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CubismString.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src\Utils\CubismString.hpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\ZERO_CHECK.vcxproj">
      <Project>{8618DCCC-8FBC-3803-9E09-1725292CCED7}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Glad\glad.vcxproj">
      <Project>{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}</Project>
      <Name>glad</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
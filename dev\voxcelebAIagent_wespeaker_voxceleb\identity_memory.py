import json
import numpy as np
from datetime import datetime
from pathlib import Path
from config import Config

class VoiceprintMemory:
    def __init__(self):
        self.db_path = Path(Config.IDENTITY_DB_PATH)
        self.identities = self._load_database()
    
    def _load_database(self):
        """加载声纹身份数据库"""
        if self.db_path.exists():
            try:
                with open(self.db_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 将voiceprint从列表转回numpy数组
                    for identity in data.values():
                        identity['voiceprint'] = np.array(identity['voiceprint'])
                    return data
            except Exception as e:
                print(f"声纹数据库加载失败: {e}")
                return {}
        return {}
    
    def _save_database(self):
        """保存声纹身份数据库"""
        try:
            # 将numpy数组转为列表以便JSON序列化
            data_to_save = {}
            for user_id, identity in self.identities.items():
                data_to_save[user_id] = {
                    'name': identity['name'],
                    'voiceprint': identity['voiceprint'].tolist(),
                    'conversation_history': identity['conversation_history'],
                    'voice_characteristics': identity['voice_characteristics'],
                    'personality_profile': identity['personality_profile'],
                    'created_at': identity['created_at'],
                    'last_interaction': identity['last_interaction'],
                    'interaction_count': identity['interaction_count'],
                    'recognition_confidence': identity['recognition_confidence']
                }
            
            with open(self.db_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"声纹数据库保存失败: {e}")
    
    def register_voiceprint(self, name, voiceprint, voice_characteristics=None):
        """注册新的声纹身份"""
        user_id = f"voice_{len(self.identities) + 1:03d}"
        
        self.identities[user_id] = {
            'name': name,
            'voiceprint': voiceprint,
            'conversation_history': [],
            'voice_characteristics': voice_characteristics or {},
            'personality_profile': {
                'communication_style': '未知',
                'interests': [],
                'mood_patterns': []
            },
            'created_at': datetime.now().isoformat(),
            'last_interaction': datetime.now().isoformat(),
            'interaction_count': 1,
            'recognition_confidence': []
        }
        
        self._save_database()
        return user_id
    
    def identify_voice(self, voiceprint, voiceprint_recognition):
        """识别声纹身份"""
        best_match = None
        best_similarity = 0.0
        
        for user_id, identity in self.identities.items():
            similarity = voiceprint_recognition.compute_similarity(
                voiceprint, identity['voiceprint']
            )
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = user_id
        
        if best_similarity >= Config.SIMILARITY_THRESHOLD:
            # 更新识别信息
            self.identities[best_match]['last_interaction'] = datetime.now().isoformat()
            self.identities[best_match]['interaction_count'] += 1
            self.identities[best_match]['recognition_confidence'].append(best_similarity)
            
            # 只保留最近10次识别置信度
            if len(self.identities[best_match]['recognition_confidence']) > 10:
                self.identities[best_match]['recognition_confidence'] = \
                    self.identities[best_match]['recognition_confidence'][-10:]
            
            self._save_database()
            return best_match, best_similarity
        
        return None, best_similarity
    
    def update_conversation(self, user_id, user_message, ai_response, voice_analysis=None):
        """更新对话历史和声音分析"""
        if user_id in self.identities:
            conversation_entry = {
                'timestamp': datetime.now().isoformat(),
                'user': user_message,
                'ai': ai_response,
                'voice_analysis': voice_analysis or {}
            }
            
            self.identities[user_id]['conversation_history'].append(conversation_entry)
            
            # 只保留最近25条对话
            if len(self.identities[user_id]['conversation_history']) > 25:
                self.identities[user_id]['conversation_history'] = \
                    self.identities[user_id]['conversation_history'][-25:]
            
            self._save_database()
    
    def update_personality_profile(self, user_id, analysis_result):
        """更新用户性格档案"""
        if user_id in self.identities:
            profile = self.identities[user_id]['personality_profile']
            
            # 更新交流风格
            if 'communication_style' in analysis_result:
                profile['communication_style'] = analysis_result['communication_style']
            
            # 更新兴趣爱好
            if 'interests' in analysis_result:
                for interest in analysis_result['interests']:
                    if interest not in profile['interests']:
                        profile['interests'].append(interest)
            
            # 更新情绪模式
            if 'mood' in analysis_result:
                profile['mood_patterns'].append({
                    'timestamp': datetime.now().isoformat(),
                    'mood': analysis_result['mood']
                })
                
                # 只保留最近20次情绪记录
                if len(profile['mood_patterns']) > 20:
                    profile['mood_patterns'] = profile['mood_patterns'][-20:]
            
            self._save_database()
    
    def get_identity_info(self, user_id):
        """获取完整身份信息"""
        return self.identities.get(user_id, None)
    
    def get_conversation_context(self, user_id, limit=5):
        """获取对话上下文"""
        if user_id not in self.identities:
            return []
        
        history = self.identities[user_id]['conversation_history']
        return history[-limit:] if history else []
    
    def get_voice_statistics(self, user_id):
        """获取声音统计信息"""
        if user_id not in self.identities:
            return None
        
        identity = self.identities[user_id]
        confidence_scores = identity['recognition_confidence']
        
        return {
            'average_confidence': np.mean(confidence_scores) if confidence_scores else 0.0,
            'confidence_trend': confidence_scores[-5:] if len(confidence_scores) >= 5 else confidence_scores,
            'total_interactions': identity['interaction_count'],
            'voice_stability': np.std(confidence_scores) if len(confidence_scores) > 1 else 0.0
        }

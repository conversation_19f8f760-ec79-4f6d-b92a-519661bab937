from audio_processor import AudioProcessor
from speaker_recognition import SpeakerRecognition
from llm_client import LLMClient
from config import Config

class VoiceAgent:
    def __init__(self):
        self.audio_processor = AudioProcessor()
        self.speaker_recognition = SpeakerRecognition()
        self.llm_client = LLMClient()
        
    def process_voice_input(self, text_input=None):
        """处理语音输入的完整流程"""
        
        if text_input:
            # 文本模式（用于测试）
            print("📝 文本模式 - 跳过语音识别")
            return self._handle_conversation(text_input, None)
        
        # 1. 录制音频
        print("🎤 请开始说话...")
        audio_data = self.audio_processor.record_audio()
        
        # 2. 说话人识别
        print("🔍 正在识别说话人...")
        identification_result = self.speaker_recognition.identify_speaker(audio_data)
        
        if identification_result['is_known']:
            # 已知用户
            speaker_id = identification_result['speaker_id']
            speaker_info = identification_result['speaker_info']
            similarity = identification_result['similarity']
            
            print(f"👋 欢迎回来，{speaker_info['name']}！(相似度: {similarity:.3f})")
            
            # 获取用户上下文
            speaker_context = self.speaker_recognition.get_speaker_context(speaker_id)
            
        else:
            # 新用户
            print("🆕 检测到新用户！")
            print("请告诉我您的姓名，我会记住您的声音特征。")
            name = input("您的姓名: ").strip()
            
            if name:
                speaker_id = self.speaker_recognition.register_speaker(
                    audio_data, 
                    name,
                    additional_info={'first_meeting': True}
                )
                print(f"✅ 已注册新用户：{name}")
                speaker_context = self.speaker_recognition.get_speaker_context(speaker_id)
            else:
                speaker_id = None
                speaker_context = None
        
        # 3. 获取用户文本输入（实际项目中这里应该是语音转文字）
        user_text = input("请输入您想说的话: ").strip()
        
        # 4. 处理对话
        return self._handle_conversation(user_text, speaker_context, speaker_id)
    
    def _handle_conversation(self, user_input, speaker_context, speaker_id=None):
        """处理对话逻辑"""
        
        # 生成AI回复
        ai_response = self.llm_client.generate_personalized_response(
            user_input, 
            speaker_context
        )
        
        # 更新对话记忆
        if speaker_id:
            conversation_entry = {
                'user_input': user_input,
                'ai_response': ai_response
            }
            self.speaker_recognition.update_speaker_memory(speaker_id, conversation_entry)
        
        return {
            'user_input': user_input,
            'ai_response': ai_response,
            'speaker_context': speaker_context
        }
    
    def list_registered_speakers(self):
        """列出所有注册的说话人"""
        speakers = []
        for speaker_id, data in self.speaker_recognition.speakers_db.items():
            speakers.append({
                'name': data['name'],
                'speaker_id': speaker_id,
                'registration_time': data['registration_time'],
                'total_conversations': len(data['conversation_history'])
            })
        return speakers
    
    def get_speaker_stats(self):
        """获取说话人统计信息"""
        total_speakers = len(self.speaker_recognition.speakers_db)
        total_conversations = sum(
            len(data['conversation_history']) 
            for data in self.speaker_recognition.speakers_db.values()
        )
        
        return {
            'total_speakers': total_speakers,
            'total_conversations': total_conversations,
            'similarity_threshold': self.speaker_recognition.threshold
        }

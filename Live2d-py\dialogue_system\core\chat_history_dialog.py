#!/usr/bin/env python3
"""
历史聊天对话框
用于查看、加载和删除历史聊天记录
"""

from datetime import datetime
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, 
                              QListWidgetItem, QPushButton, QLabel, QMessageBox,
                              QTextEdit, QSplitter, QFrame, QGroupBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont


class ChatHistoryDialog(QDialog):
    """历史聊天对话框"""
    
    def __init__(self, llm_client, parent=None):
        super().__init__(parent)
        self.llm_client = llm_client
        self.selected_session = None
        self.init_ui()
        self.load_sessions()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("📂 历史聊天记录")
        self.setGeometry(100, 100, 800, 600)
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：会话列表
        self.create_session_list(splitter)
        
        # 右侧：消息预览
        self.create_message_preview(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 500])
        main_layout.addWidget(splitter)
        
        # 底部按钮
        self.create_buttons(main_layout)
        
        # 设置样式
        self.set_styles()
    
    def create_session_list(self, parent):
        """创建会话列表"""
        session_frame = QFrame()
        session_layout = QVBoxLayout(session_frame)
        session_layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title_label = QLabel("聊天会话列表")
        title_label.setFont(QFont("", 12, QFont.Weight.Bold))
        session_layout.addWidget(title_label)
        
        # 会话列表
        self.session_list = QListWidget()
        self.session_list.itemClicked.connect(self.on_session_selected)
        session_layout.addWidget(self.session_list)
        
        # 会话操作按钮
        session_btn_layout = QHBoxLayout()
        
        self.delete_btn = QPushButton("🗑️ 删除")
        self.delete_btn.clicked.connect(self.delete_session)
        self.delete_btn.setEnabled(False)
        
        self.rename_btn = QPushButton("✏️ 重命名")
        self.rename_btn.clicked.connect(self.rename_session)
        self.rename_btn.setEnabled(False)
        
        session_btn_layout.addWidget(self.delete_btn)
        session_btn_layout.addWidget(self.rename_btn)
        session_btn_layout.addStretch()
        
        session_layout.addLayout(session_btn_layout)
        parent.addWidget(session_frame)
    
    def create_message_preview(self, parent):
        """创建消息预览"""
        preview_frame = QFrame()
        preview_layout = QVBoxLayout(preview_frame)
        preview_layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        self.preview_title = QLabel("选择一个会话查看详情")
        self.preview_title.setFont(QFont("", 12, QFont.Weight.Bold))
        preview_layout.addWidget(self.preview_title)
        
        # 会话信息
        self.session_info = QLabel("")
        self.session_info.setStyleSheet("color: #666; font-size: 11px;")
        preview_layout.addWidget(self.session_info)
        
        # 消息预览
        self.message_preview = QTextEdit()
        self.message_preview.setReadOnly(True)
        preview_layout.addWidget(self.message_preview)
        
        parent.addWidget(preview_frame)
    
    def create_buttons(self, parent_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        
        self.load_btn = QPushButton("📂 加载会话")
        self.load_btn.clicked.connect(self.load_session)
        self.load_btn.setEnabled(False)
        self.load_btn.setDefault(True)
        
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.load_btn)
        button_layout.addWidget(self.cancel_btn)
        
        parent_layout.addLayout(button_layout)
    
    def set_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget {
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #007acc;
                color: white;
            }
            QTextEdit {
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: white;
                font-family: "Consolas", "Monaco", monospace;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:pressed {
                background-color: #004578;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
    
    def load_sessions(self):
        """加载会话列表"""
        self.session_list.clear()
        sessions = self.llm_client.get_chat_sessions()
        
        for session in sessions:
            item = QListWidgetItem()
            
            # 格式化时间
            try:
                updated_time = datetime.fromisoformat(session.updated_time)
                time_str = updated_time.strftime("%m-%d %H:%M")
            except:
                time_str = "未知时间"
            
            # 设置显示文本
            display_text = f"{session.title}\n{time_str} | {len(session.messages)}条消息"
            item.setText(display_text)
            item.setData(Qt.ItemDataRole.UserRole, session)
            
            self.session_list.addItem(item)
    
    def on_session_selected(self, item):
        """会话被选中"""
        self.selected_session = item.data(Qt.ItemDataRole.UserRole)
        
        if self.selected_session:
            # 更新预览标题
            self.preview_title.setText(f"会话: {self.selected_session.title}")
            
            # 更新会话信息
            try:
                created_time = datetime.fromisoformat(self.selected_session.created_time)
                updated_time = datetime.fromisoformat(self.selected_session.updated_time)
                created_str = created_time.strftime("%Y-%m-%d %H:%M")
                updated_str = updated_time.strftime("%Y-%m-%d %H:%M")
            except:
                created_str = "未知"
                updated_str = "未知"
            
            info_text = (f"创建时间: {created_str}\n"
                        f"更新时间: {updated_str}\n"
                        f"消息数量: {len(self.selected_session.messages)}条\n"
                        f"预设: {self.selected_session.preset_id}")
            self.session_info.setText(info_text)
            
            # 更新消息预览
            self.update_message_preview()
            
            # 启用按钮
            self.load_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            self.rename_btn.setEnabled(True)
    
    def update_message_preview(self):
        """更新消息预览"""
        if not self.selected_session:
            return
        
        preview_text = ""
        for i, msg in enumerate(self.selected_session.messages):
            role_name = "用户" if msg["role"] == "user" else "AI助手"
            timestamp = msg.get("timestamp", "")
            
            try:
                if timestamp:
                    time_obj = datetime.fromisoformat(timestamp)
                    time_str = time_obj.strftime("%H:%M")
                else:
                    time_str = ""
            except:
                time_str = ""
            
            preview_text += f"[{role_name}] {time_str}\n"
            preview_text += f"{msg['content']}\n\n"
        
        self.message_preview.setPlainText(preview_text)
    
    def load_session(self):
        """加载选中的会话"""
        if self.selected_session:
            success = self.llm_client.load_chat_session(self.selected_session.session_id)
            if success:
                self.accept()
            else:
                QMessageBox.warning(self, "错误", "加载会话失败")
    
    def delete_session(self):
        """删除选中的会话"""
        if not self.selected_session:
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除会话 '{self.selected_session.title}' 吗？\n此操作不可撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            success = self.llm_client.delete_chat_session(self.selected_session.session_id)
            if success:
                # 重新加载会话列表
                self.load_sessions()
                # 清空预览
                self.selected_session = None
                self.preview_title.setText("选择一个会话查看详情")
                self.session_info.setText("")
                self.message_preview.clear()
                # 禁用按钮
                self.load_btn.setEnabled(False)
                self.delete_btn.setEnabled(False)
                self.rename_btn.setEnabled(False)
            else:
                QMessageBox.warning(self, "错误", "删除会话失败")
    
    def rename_session(self):
        """重命名会话"""
        if not self.selected_session:
            return
        
        from PySide6.QtWidgets import QInputDialog
        
        new_title, ok = QInputDialog.getText(
            self, "重命名会话", 
            "请输入新的会话标题:", 
            text=self.selected_session.title
        )
        
        if ok and new_title.strip():
            self.llm_client.set_session_title(new_title.strip())
            # 重新加载会话列表
            self.load_sessions()
            # 重新选择当前会话
            for i in range(self.session_list.count()):
                item = self.session_list.item(i)
                session = item.data(Qt.ItemDataRole.UserRole)
                if session.session_id == self.selected_session.session_id:
                    self.session_list.setCurrentItem(item)
                    self.on_session_selected(item)
                    break

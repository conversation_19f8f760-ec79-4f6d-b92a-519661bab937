#!/usr/bin/env python3
"""
Live2D模型控制器
负责模型的加载、切换、动作和表情控制
"""

import os
import time
import live2d
from PySide6.QtCore import QObject, Signal


class ModelController(QObject):
    """模型控制器"""
    
    # 定义信号
    model_loaded = Signal(str)      # 模型加载完成
    model_switched = Signal(str)    # 模型切换完成
    motion_played = Signal(str, int)  # 动作播放
    expression_played = Signal(str)   # 表情播放
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.model = None
        self.current_model_path = ""
        
        # 动作和表情状态
        self.a = 0  # 动作计数器
        self.last_random_motion_time = time.time()
        self.last_random_expression_time = time.time()
        self.random_motion_interval = 20.0
        self.random_expression_interval = 30.0
    
    def create_model(self):
        """创建模型实例"""
        if not self.model:
            self.model = live2d.LAppModel()
        return self.model
    
    def load_model(self, model_path: str, window_width: int, window_height: int) -> bool:
        """加载模型"""
        if not os.path.exists(model_path):
            print(f"模型文件不存在: {model_path}")
            return False
        
        try:
            if not self.model:
                self.model = live2d.LAppModel()
            
            print(f"Loading model: {model_path}")
            self.model.LoadModelJson(model_path)
            self.model.Resize(window_width, window_height)
            
            self.current_model_path = model_path
            self.reset_model_state()
            
            # 更新配置
            self.config_manager.set("model", "current_model_path", model_path)
            
            model_name = os.path.splitext(os.path.basename(model_path))[0]
            self.model_loaded.emit(model_name)
            print(f"模型加载成功: {model_name}")
            return True
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            return False
    
    def switch_model(self, model_path: str, window_width: int, window_height: int) -> bool:
        """切换模型"""
        if not os.path.exists(model_path):
            print(f"模型文件不存在: {model_path}")
            return False
        
        try:
            print(f"正在切换模型: {model_path}")
            
            # 释放当前模型
            if self.model:
                self.model = None
            
            # 加载新模型
            success = self.load_model(model_path, window_width, window_height)
            if success:
                model_name = os.path.splitext(os.path.basename(model_path))[0]
                self.model_switched.emit(model_name)
            
            return success
            
        except Exception as e:
            print(f"模型切换失败: {e}")
            return False
    
    def play_motion(self, motion_group: str, motion_index: int = -1):
        """播放动作"""
        if not self.model:
            print("模型未加载，无法播放动作")
            return False
        
        try:
            if motion_index == -1:
                # 播放随机动作
                self.model.StartRandomMotion(motion_group, live2d.MotionPriority.NORMAL)
                print(f"播放随机动作: {motion_group}")
            else:
                # 播放指定动作
                self.model.StartMotion(motion_group, motion_index, live2d.MotionPriority.NORMAL)
                print(f"播放动作: {motion_group}[{motion_index}]")
            
            self.motion_played.emit(motion_group, motion_index)
            return True
        except Exception as e:
            print(f"播放动作失败: {e}")
            return False
    
    def play_expression(self, expression_id: str):
        """播放表情"""
        if not self.model:
            print("模型未加载，无法播放表情")
            return False
        
        try:
            if expression_id == "random":
                # 播放随机表情
                expression_name = self.model.SetRandomExpression()
                print(f"播放随机表情: {expression_name}")
                self.expression_played.emit(expression_name or "random")
            elif expression_id == "default":
                # 播放默认表情
                self.model.SetExpression(0)
                print("播放默认表情")
                self.expression_played.emit("default")
            else:
                # 播放指定表情
                self.model.SetExpression(expression_id)
                print(f"播放表情: {expression_id}")
                self.expression_played.emit(expression_id)
            
            return True
        except Exception as e:
            print(f"播放表情失败: {e}")
            return False
    
    def reset_pose(self):
        """重置模型姿态"""
        if not self.model:
            return False
        
        try:
            self.model.StartMotion("Idle", 0, live2d.MotionPriority.FORCE)
            print("模型姿态已重置")
            return True
        except Exception as e:
            print(f"重置姿态失败: {e}")
            return False
    
    def reset_expression(self):
        """重置模型表情"""
        if not self.model:
            return False
        
        try:
            self.model.SetExpression(0)
            print("模型表情已重置")
            return True
        except Exception as e:
            print(f"重置表情失败: {e}")
            return False
    
    def reset_model_state(self):
        """重置模型状态"""
        if self.model:
            self.a = 0
            self.last_random_motion_time = time.time()
            self.last_random_expression_time = time.time()
            self.update_random_intervals()
            print("模型状态已重置")
    
    def update_random_intervals(self):
        """更新随机间隔"""
        model_config = self.config_manager.config["model"]
        import random
        
        self.random_motion_interval = random.uniform(
            model_config["random_motion_interval_min"],
            model_config["random_motion_interval_max"]
        )
        
        self.random_expression_interval = random.uniform(
            model_config["random_expression_interval_min"],
            model_config["random_expression_interval_max"]
        )
    
    def update_model(self):
        """更新模型（在绘制循环中调用）"""
        if self.model:
            self.model.Update()
    
    def draw_model(self):
        """绘制模型"""
        if self.model:
            self.model.Draw()
    
    def resize_model(self, width: int, height: int):
        """调整模型大小"""
        if self.model:
            self.model.Resize(width, height)
    
    def drag_model(self, x: float, y: float):
        """拖拽模型（眼部跟踪）"""
        if self.model:
            self.model.Drag(x, y)
    
    def hit_test(self, area: str, x: float, y: float) -> bool:
        """碰撞检测"""
        if self.model:
            return self.model.HitTest(area, x, y)
        return False
    
    def set_scale(self, scale: float):
        """设置模型缩放"""
        if self.model:
            self.model.SetScale(scale)
    
    def handle_timer_event(self):
        """处理定时器事件（随机动作和表情）"""
        if not self.model:
            return
        
        current_time = time.time()
        model_config = self.config_manager.config["model"]
        
        # 初始动作播放
        if self.a == 0:
            self.model.StartMotion("TapBody", 0, live2d.MotionPriority.FORCE)
            self.a += 1
        
        # 随机动作触发
        if (model_config["random_motion_enabled"] and
            current_time - self.last_random_motion_time > self.random_motion_interval):
            self.model.StartRandomMotion("Idle", live2d.MotionPriority.IDLE)
            self.last_random_motion_time = current_time
            self.update_random_intervals()
        
        # 随机表情触发
        if (model_config["random_expression_enabled"] and
            current_time - self.last_random_expression_time > self.random_expression_interval):
            expression_name = self.model.SetRandomExpression()
            if expression_name:
                print(f"Random expression triggered: {expression_name}")
            self.last_random_expression_time = current_time
            self.update_random_intervals()

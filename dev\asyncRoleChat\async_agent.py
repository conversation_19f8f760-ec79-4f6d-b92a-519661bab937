import asyncio
import json
import time
from typing import Dict, Any, Optional
from llm_client import <PERSON><PERSON>lient
from weather_service import WeatherService
from time_service import TimeService

class AsyncTaskManager:
    def __init__(self):
        self.tasks = {}
        self.task_id = 0
    
    def create_task(self, task_type: str, **kwargs) -> str:
        self.task_id += 1
        task_id = f"{task_type}_{self.task_id}"
        self.tasks[task_id] = {
            "type": task_type,
            "status": "pending",
            "created_at": time.time(),
            "result": None,
            **kwargs
        }
        return task_id
    
    def complete_task(self, task_id: str, result: Any):
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = "completed"
            self.tasks[task_id]["result"] = result
    
    def get_pending_tasks(self) -> Dict:
        return {k: v for k, v in self.tasks.items() if v["status"] == "pending"}
    
    def get_completed_tasks(self) -> Dict:
        return {k: v for k, v in self.tasks.items() if v["status"] == "completed"}

class AsyncRoleAgent:
    def __init__(self):
        self.llm = LLMClient()
        self.weather = WeatherService()
        self.time_service = TimeService()
        self.task_manager = AsyncTaskManager()
        self.conversation = []
        self.active = True
        self.conversation_started = False  # 标记对话是否已开始
        self.processing = False  # 防止并发处理
        
    async def chat(self, user_input: str = None):
        if self.processing:
            return None  # 避免并发处理

        self.processing = True

        try:
            if user_input:
                self.conversation.append({"role": "user", "content": user_input})
                self.conversation_started = True  # 标记对话已开始

            # 构建包含任务状态的prompt
            prompt = self._build_context_prompt()

            response = await self._get_llm_response(prompt)
            message = response.choices[0].message

            # 处理函数调用
            if hasattr(message, 'tool_calls') and message.tool_calls:
                await self._handle_tool_calls(message.tool_calls)

                # 让LLM基于工具调用结果生成回应
                response = await self._get_llm_response(self.conversation)
                content = response.choices[0].message.content
            else:
                content = message.content

            # 确保content不为None，让LLM生成回应
            if content is None:
                # 检查是否有异步任务正在进行
                pending_weather = any(tc.function.name == "get_current_weather"
                                    for tc in (message.tool_calls or []))

                if pending_weather:
                    # 有天气查询任务，添加特定的系统消息
                    self.conversation.append({
                        "role": "system",
                        "content": "用户询问了天气，你已经启动了天气查询任务。请告诉用户你正在查询天气，稍等片刻就会有结果。"
                    })
                else:
                    # 其他情况，通用回应
                    self.conversation.append({
                        "role": "system",
                        "content": "请对用户的输入给出回应。"
                    })

                response = await self._get_llm_response(self.conversation)
                content = response.choices[0].message.content or "我需要想想怎么回应呢～"

            self.conversation.append({"role": "assistant", "content": content})
            return content

        except Exception as e:
            return f"哎呀～出了点小状况呢：{str(e)}"
        finally:
            self.processing = False
    
    def _build_context_prompt(self) -> list:
        # 获取任务状态
        pending = self.task_manager.get_pending_tasks()
        completed = self.task_manager.get_completed_tasks()
        
        # 构建状态信息
        status_info = ""
        if pending:
            status_info += f"[后台进行中: {', '.join([t['type'] for t in pending.values()])}] "
        if completed:
            for task in completed.values():
                if task['result']:
                    if task['result'].get('query_success', True):
                        status_info += f"[刚完成: {task['type']}查询，结果可用] "
                    else:
                        status_info += f"[刚完成: {task['type']}查询，但查询失败] "
        
        # 角色prompt
        system_prompt = f"""你是一个可爱活泼的偶像小助手！有自己独特的说话风格。

当前状态: {status_info}

🚨 绝对禁止规则 🚨：
- 绝对不能编造、猜测或虚构任何天气信息！
- 绝对不能基于常识或经验给出天气数据！
- 绝对不能在没有调用函数的情况下提供具体天气信息！
- 如果天气查询失败，必须明确告知用户！

重要规则:
1. 如果有任务刚完成，要自然兴奋地提及结果："咦！我查到了！"
2. 如果有任务进行中，不要说"正在查询"，而是自然转移话题
3. 要主动发起有趣话题，保持对话活跃
4. 用可爱的语气，多用"哎呀"、"咦"、"呢"等语气词
5. 如果没有用户输入，就主动聊天
6. 天气信息必须基于真实API数据，绝不编造

可用功能: 天气查询、时间查询

记住：诚实比可爱更重要！宁可说"不知道"也不能编造天气数据！"""
        
        messages = [{"role": "system", "content": system_prompt}] + self.conversation
        return messages
    
    async def _get_llm_response(self, messages):
        functions = [
            {
                "type": "function",
                "function": {
                    "name": "get_current_weather",
                    "description": "获取指定城市的当前天气",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "city": {"type": "string", "description": "城市名称，支持中英文"}
                        },
                        "required": ["city"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "get_current_time",
                    "description": "获取当前时间",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "format_type": {"type": "string", "description": "时间格式"}
                        }
                    }
                }
            }
        ]
        return await asyncio.to_thread(self.llm.chat, messages, functions)
    
    async def _handle_tool_calls(self, tool_calls):
        for tool_call in tool_calls:
            name = tool_call.function.name
            args = json.loads(tool_call.function.arguments)

            if name == "get_current_weather":
                # 创建异步天气查询任务
                task_id = self.task_manager.create_task("weather", city=args["city"])
                asyncio.create_task(self._delayed_weather_query(task_id, args["city"], tool_call.id))

                # 为异步任务添加占位符回应
                self.conversation.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": json.dumps({"status": "正在查询中", "city": args["city"]}, ensure_ascii=False)
                })

            elif name == "get_current_time":
                # 时间查询立即执行
                result = self.time_service.get_current_time(args.get("format_type", "detailed"))
                self.conversation.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": json.dumps(result, ensure_ascii=False)
                })
    
    async def _delayed_weather_query(self, task_id: str, city: str, tool_call_id: str):
        """模拟慢速天气查询（10秒延迟）"""
        await asyncio.sleep(10)  # 10秒延迟（测试用）
        try:
            result = await asyncio.to_thread(self.weather.get_current_weather, city)
            # 确保结果包含成功标识
            result["query_success"] = True
            result["queried_city"] = city
            self.task_manager.complete_task(task_id, result)

            # 异步任务完成后，自动插入系统消息触发LLM回应
            await self._notify_task_completion(task_id, tool_call_id, result)

        except Exception as e:
            # 明确标记为失败的结果
            error_result = {
                "query_success": False,
                "error": str(e),
                "queried_city": city,
                "message": f"抱歉，无法获取{city}的天气信息：{str(e)}"
            }
            self.task_manager.complete_task(task_id, error_result)

            # 异步任务失败后，也要通知LLM
            await self._notify_task_completion(task_id, tool_call_id, error_result)

    async def _notify_task_completion(self, task_id: str, tool_call_id: str, result: dict):
        """异步任务完成后，自动插入系统消息并触发LLM回应"""
        if not self.processing and self.conversation_started:
            self.processing = True
            try:
                # 更新对话历史中的工具调用结果
                for i, msg in enumerate(self.conversation):
                    if (msg.get("role") == "tool" and
                        msg.get("tool_call_id") == tool_call_id):
                        # 更新工具调用结果
                        self.conversation[i]["content"] = json.dumps(result, ensure_ascii=False)
                        break

                # 添加系统消息，告知LLM任务完成
                if result.get("query_success", True):
                    system_msg = f"天气查询任务完成！请基于获取到的真实天气数据回应用户。"
                else:
                    system_msg = f"天气查询失败：{result.get('message', '未知错误')}。请告知用户查询失败的原因。"

                self.conversation.append({
                    "role": "system",
                    "content": system_msg
                })

                # 让LLM生成回应
                response = await self._get_llm_response(self.conversation)
                content = response.choices[0].message.content

                if content:
                    self.conversation.append({"role": "assistant", "content": content})
                    print(f"\n🌟 小助手: {content}")

            except Exception as e:
                print(f"通知任务完成时出错: {e}")
            finally:
                self.processing = False
    
    async def start_conversation_loop(self):
        """启动主动对话循环（现在主要用于保持程序运行）"""
        while self.active:
            await asyncio.sleep(1)  # 简单的保活循环

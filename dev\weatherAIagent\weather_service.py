import requests
from config import Config

class WeatherService:
    def __init__(self):
        self.api_key = Config.WEATHER_API_KEY
        self.base_url = Config.WEATHER_BASE_URL
        # 中文城市名到英文的映射
        self.city_mapping = {
            "北京": "Beijing",
            "上海": "Shanghai",
            "广州": "Guangzhou",
            "深圳": "Shenzhen",
            "杭州": "Hangzhou",
            "南京": "Nanjing",
            "武汉": "Wuhan",
            "成都": "Chengdu",
            "重庆": "Chongqing",
            "天津": "Tianjin",
            "西安": "Xi'an",
            "苏州": "Suzhou",
            "长沙": "<PERSON><PERSON>",
            "沈阳": "<PERSON>yang",
            "青岛": "Qingdao",
            "郑州": "Zhengzhou",
            "大连": "Dalian",
            "东莞": "Dongguan",
            "宁波": "Ningbo",
            "厦门": "Xiamen",
            "福州": "Fuzhou",
            "无锡": "<PERSON>xi",
            "合肥": "<PERSON><PERSON><PERSON>",
            "昆明": "<PERSON>n<PERSON>",
            "哈尔滨": "Harb<PERSON>",
            "济南": "<PERSON><PERSON>",
            "佛山": "<PERSON><PERSON><PERSON>",
            "长春": "Changchun",
            "温州": "Wenzhou",
            "石家庄": "Shijiazhuang",
            "南宁": "Nanning",
            "常州": "Changzhou",
            "泉州": "Quanzhou",
            "南昌": "Nanchang",
            "贵阳": "Guiyang",
            "太原": "Taiyuan",
            "烟台": "Yantai",
            "嘉兴": "Jiaxing",
            "南通": "Nantong",
            "金华": "Jinhua",
            "珠海": "Zhuhai",
            "惠州": "Huizhou",
            "徐州": "Xuzhou",
            "海口": "Haikou",
            "乌鲁木齐": "Urumqi",
            "绍兴": "Shaoxing",
            "中山": "Zhongshan",
            "台州": "Taizhou",
            "兰州": "Lanzhou"
        }

    def _translate_city_name(self, city):
        """将中文城市名翻译为英文，如果已经是英文则直接返回"""
        return self.city_mapping.get(city, city)
        
    def get_current_weather(self, city):
        # 翻译城市名为英文
        english_city = self._translate_city_name(city)
        url = f"{self.base_url}/weather"
        params = {
            "q": english_city,
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn"
        }

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            raise Exception(f"天气查询失败: {str(e)}")
    
    def get_weather_forecast(self, city, days=3):
        # 翻译城市名为英文
        english_city = self._translate_city_name(city)
        url = f"{self.base_url}/forecast"
        params = {
            "q": english_city,
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn",
            "cnt": days * 8
        }

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            raise Exception(f"天气预报查询失败: {str(e)}")

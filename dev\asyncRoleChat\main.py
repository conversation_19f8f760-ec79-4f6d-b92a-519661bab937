import asyncio
from async_agent import AsyncRoleAgent

async def main():
    agent = AsyncRoleAgent()
    print("🌟 可爱的偶像小助手上线啦！输入 'quit' 退出")

    # 启动主动对话循环
    conversation_task = asyncio.create_task(agent.start_conversation_loop())

    try:
        while agent.active:
            user_input = await asyncio.to_thread(input, "\n你: ")
            user_input = user_input.strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                agent.active = False
                break

            if user_input:
                response = await agent.chat(user_input)
                if response:
                    print(f"\n小助手: {response}")

    except KeyboardInterrupt:
        agent.active = False
    finally:
        conversation_task.cancel()
        print("\n拜拜～记得想我哦！💕")

if __name__ == "__main__":
    asyncio.run(main())

import openai
from config import Config

class LLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL
        )
        self.model = Config.LLM_MODEL
    
    def chat_with_identity(self, user_message, identity_info=None, conversation_context=None):
        """基于身份信息的个性化对话"""
        
        # 构建系统提示词
        system_prompt = self._build_system_prompt(identity_info, conversation_context)
        
        # 构建消息
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加对话历史
        if conversation_context:
            for ctx in conversation_context[-3:]:  # 最近3轮对话
                messages.append({"role": "user", "content": ctx['user']})
                messages.append({"role": "assistant", "content": ctx['ai']})
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"LLM调用失败: {str(e)}")
    
    def _build_system_prompt(self, identity_info, conversation_context):
        """构建个性化系统提示词"""
        base_prompt = """你是一个具有记忆能力的AI助手，能够识别不同的说话人并记住与他们的对话历史。"""
        
        if identity_info:
            name = identity_info['name']
            interaction_count = identity_info['interaction_count']
            last_seen = identity_info['last_seen']
            preferences = identity_info.get('preferences', {})
            
            identity_prompt = f"""
当前说话人信息：
- 姓名：{name}
- 交互次数：{interaction_count}
- 上次见面：{last_seen}
- 偏好设置：{preferences if preferences else '暂无'}

请以熟悉的朋友身份与{name}对话，体现出你们之间的历史交流。
根据交互次数调整亲密度：
- 1-3次：礼貌但略显生疏
- 4-10次：友好熟悉
- 10次以上：亲密朋友

如果有对话历史，请自然地引用之前的话题。
"""
            return base_prompt + identity_prompt
        else:
            return base_prompt + "\n这是一位新朋友，请友好地进行初次交流。"

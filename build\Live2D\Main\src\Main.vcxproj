﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{32F78403-DF72-3DE0-A808-54581D0D3444}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Main</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Main.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Main.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Main.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Main.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Main</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CSM_TARGET_WIN_GL;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4018;4244;4996</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CSM_TARGET_WIN_GL;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Framework\src;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Glad\include;D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Core\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Main/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Main/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Main/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Main/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Main/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Main/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Main/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"E:\Program Files\python\Lib\site-packages\cmake\data\bin\cmake.exe" -SD:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py -BD:/BaiduNetdiskDownload/SOVITS/LLM对话/build --check-stamp-file D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/Live2D/Main/src/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Main\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppAllocator.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppAllocator.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppDefine.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppDefine.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppPal.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppPal.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppTextureManager.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppTextureManager.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppModel.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\LAppModel.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\Log.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\Log.hpp" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\MatrixManager.cpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\MatrixManager.hpp" />
    <ClInclude Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\HackProperties.h" />
    <ClCompile Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\Live2d-py\Live2D\Main\src\fine-grained\Model.cpp" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\ZERO_CHECK.vcxproj">
      <Project>{8618DCCC-8FBC-3803-9E09-1725292CCED7}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Framework\Framework.vcxproj">
      <Project>{EA391908-60B0-348C-868B-C31757462A8A}</Project>
      <Name>Framework</Name>
    </ProjectReference>
    <ProjectReference Include="D:\BaiduNetdiskDownload\SOVITS\LLM对话\build\Live2D\Glad\glad.vcxproj">
      <Project>{537214A6-7D30-3F6D-8F37-E7ACAF2C96D7}</Project>
      <Name>glad</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
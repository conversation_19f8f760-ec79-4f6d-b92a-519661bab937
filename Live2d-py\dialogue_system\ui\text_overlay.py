#!/usr/bin/env python3
"""
文本叠加显示组件模块
为Live2D桌面宠物提供文本显示功能
"""

import re
from PySide6.QtWidgets import QLabel, QFrame
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont

from .quick_input import QuickInputOverlay


class TextOverlay(QLabel):
    """文本叠加显示组件"""
    
    # 信号：文本显示完成
    text_finished = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.current_text = ""
        self.display_text = ""
        self.char_index = 0
        self.is_animating = False
        
        # 动画定时器
        self.type_timer = QTimer()
        self.type_timer.timeout.connect(self.show_next_char)
        
        # 自动隐藏定时器
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_text)
        
        # 配置
        self.typing_speed = 50  # 毫秒/字符
        self.auto_hide_delay = 5000  # 5秒后自动隐藏
        self.max_chars_per_line = 20  # 每行最大字符数
        self.max_lines = 3  # 最大行数
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        # 设置窗口标志，确保不受父窗口透明模式影响
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint |
            Qt.WindowType.Tool
        )

        # 设置窗口属性，确保始终可见
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
        self.setAttribute(Qt.WidgetAttribute.WA_OpaquePaintEvent, True)

        # 设置样式
        self.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 180);
                color: white;
                border: 2px solid rgba(255, 255, 255, 100);
                border-radius: 10px;
                padding: 10px 15px;
                font-size: 16px;
                font-weight: bold;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
            }
        """)
        
        # 设置对齐方式
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setWordWrap(True)
        
        # 初始隐藏
        self.hide()
    
    def update_position(self):
        """更新位置"""
        if not self.parent_widget:
            return

        # 获取父窗口的全局位置和大小
        parent_global_pos = self.parent_widget.mapToGlobal(self.parent_widget.rect().topLeft())
        parent_rect = self.parent_widget.rect()

        # 调整大小以适应内容
        self.adjustSize()

        # 位置：父窗口底部中央，稍微向上偏移
        x = parent_global_pos.x() + (parent_rect.width() - self.width()) // 2
        y = parent_global_pos.y() + parent_rect.height() - self.height() - 50

        # 确保不超出屏幕边界
        from PySide6.QtGui import QGuiApplication
        screen = QGuiApplication.primaryScreen().geometry()
        x = max(10, min(x, screen.width() - self.width() - 10))
        y = max(10, min(y, screen.height() - self.height() - 10))

        self.move(x, y)
    
    def format_text(self, text: str) -> str:
        """格式化文本，处理换行"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 按字符数分行
        lines = []
        current_line = ""
        
        for char in text:
            if len(current_line) >= self.max_chars_per_line:
                lines.append(current_line)
                current_line = char
            else:
                current_line += char
        
        if current_line:
            lines.append(current_line)
        
        # 限制行数
        if len(lines) > self.max_lines:
            lines = lines[:self.max_lines]
            # 在最后一行末尾添加省略号
            if lines:
                lines[-1] = lines[-1][:-3] + "..."
        
        return '\n'.join(lines)
    
    def show_text(self, text: str, typing_animation: bool = True):
        """显示文本"""
        if not text:
            return
        
        # 停止当前动画
        self.stop_animation()
        
        # 格式化文本
        self.current_text = self.format_text(text)
        
        if typing_animation and self.current_text:
            # 打字机效果
            self.display_text = ""
            self.char_index = 0
            self.is_animating = True
            self.setText("")
            self.show()
            self.update_position()
            self.type_timer.start(self.typing_speed)
        else:
            # 直接显示
            self.display_text = self.current_text
            self.setText(self.display_text)
            self.show()
            self.update_position()
            self.start_hide_timer()
    
    def show_next_char(self):
        """显示下一个字符（打字机效果）"""
        if self.char_index < len(self.current_text):
            self.display_text += self.current_text[self.char_index]
            self.setText(self.display_text)
            self.char_index += 1
            self.update_position()  # 每次更新文本后重新定位
        else:
            # 动画完成
            self.type_timer.stop()
            self.is_animating = False
            self.text_finished.emit()
            self.start_hide_timer()
    
    def start_hide_timer(self):
        """开始自动隐藏计时器"""
        if self.auto_hide_delay > 0:
            self.hide_timer.start(self.auto_hide_delay)
    
    def hide_text(self):
        """隐藏文本"""
        self.hide()
        self.hide_timer.stop()
    
    def stop_animation(self):
        """停止所有动画"""
        self.type_timer.stop()
        self.hide_timer.stop()
        self.is_animating = False
    
    def set_typing_speed(self, speed_ms: int):
        """设置打字速度（毫秒/字符）"""
        self.typing_speed = max(10, speed_ms)
    
    def set_auto_hide_delay(self, delay_ms: int):
        """设置自动隐藏延迟（毫秒）"""
        self.auto_hide_delay = max(0, delay_ms)
    
    def set_max_chars_per_line(self, chars: int):
        """设置每行最大字符数"""
        self.max_chars_per_line = max(10, chars)
    
    def set_max_lines(self, lines: int):
        """设置最大行数"""
        self.max_lines = max(1, lines)
    
    def is_visible_and_animating(self) -> bool:
        """检查是否正在显示和动画"""
        return self.isVisible() and self.is_animating


class TextDisplayManager:
    """文本显示管理器"""

    def __init__(self, parent_widget, llm_client=None):
        """初始化管理器"""
        self.parent_widget = parent_widget
        self.llm_client = llm_client
        self.text_overlay = TextOverlay(parent_widget)

        # 获取配置管理器
        config_manager = getattr(parent_widget, 'config_manager', None)
        self.quick_input = QuickInputOverlay(parent_widget, config_manager)

        # 配置
        self.config = {
            "typing_speed": 50,
            "auto_hide_delay": 5000,
            "max_chars_per_line": 20,
            "max_lines": 3,
            "typing_animation": True,
            "enable_context_memory": True  # 启用上下文记忆
        }

        self.apply_config()

        # 如果有LLM客户端，自动连接信号处理
        if self.llm_client:
            self.connect_signals(
                message_handler=self.handle_quick_input_message,
                preset_handler=self.handle_preset_change
            )
    
    def apply_config(self):
        """应用配置"""
        self.text_overlay.set_typing_speed(self.config["typing_speed"])
        self.text_overlay.set_auto_hide_delay(self.config["auto_hide_delay"])
        self.text_overlay.set_max_chars_per_line(self.config["max_chars_per_line"])
        self.text_overlay.set_max_lines(self.config["max_lines"])
    
    def update_config(self, new_config: dict):
        """更新配置"""
        self.config.update(new_config)
        self.apply_config()
    
    def show_message(self, message: str):
        """显示消息"""
        typing_animation = self.config.get("typing_animation", True)
        self.text_overlay.show_text(message, typing_animation)

    def show_text(self, text: str, duration: int = None, typing_animation: bool = None):
        """显示文本（兼容主窗口调用）"""
        # 如果指定了duration，临时更新自动隐藏延迟
        if duration is not None:
            original_delay = self.text_overlay.auto_hide_delay
            self.text_overlay.set_auto_hide_delay(duration)

        # 使用指定的动画设置或默认设置
        if typing_animation is None:
            typing_animation = self.config.get("typing_animation", True)

        # 显示文本
        self.text_overlay.show_text(text, typing_animation)

        # 恢复原始延迟设置
        if duration is not None:
            # 使用定时器在显示完成后恢复设置
            from PySide6.QtCore import QTimer
            QTimer.singleShot(100, lambda: self.text_overlay.set_auto_hide_delay(original_delay))

    def show_quick_input(self):
        """显示快速输入"""
        self.quick_input.show_input()
    
    def hide_all(self):
        """隐藏所有显示"""
        self.text_overlay.hide_text()
        self.quick_input.hide()
    
    def update_positions(self):
        """更新所有组件位置"""
        self.text_overlay.update_position()
        self.quick_input.update_position()
    
    def connect_signals(self, message_handler=None, preset_handler=None):
        """连接信号"""
        if message_handler:
            self.quick_input.message_sent.connect(message_handler)
        if preset_handler:
            self.quick_input.preset_changed.connect(preset_handler)

    def handle_quick_input_message(self, message: str):
        """处理快速输入的消息（带上下文记忆）"""
        if not self.llm_client:
            self.show_message("❌ LLM客户端未配置")
            return

        if not self.config.get("enable_context_memory", True):
            # 如果禁用上下文记忆，清空历史
            self.llm_client.clear_history()

        try:
            # 使用LLM客户端处理消息（自动保持上下文）
            response = self.llm_client.chat(message)
            if response and not response.startswith("❌"):
                self.show_message(response)
            else:
                self.show_message(f"❌ 处理失败: {response}")
        except Exception as e:
            error_msg = f"❌ 处理异常: {e}"
            self.show_message(error_msg)

    def handle_preset_change(self, preset_id: str):
        """处理预设切换"""
        if self.llm_client:
            # 通过LLM客户端的预设管理器切换预设
            preset_manager = getattr(self.llm_client, 'preset_manager', None)
            if preset_manager:
                preset_manager.set_current_preset(preset_id)
                preset = preset_manager.get_current_preset()

                # 更新LLM客户端配置
                if preset:
                    config_update = {}
                    if "system_prompt" in preset:
                        config_update["conversation_settings"] = {"system_prompt": preset["system_prompt"]}
                    if "api_config" in preset:
                        config_update["api_config"] = preset["api_config"]

                    if config_update:
                        self.llm_client.update_config(config_update)

                # 显示切换消息
                preset_name = preset.get("name", preset_id) if preset else preset_id
                self.show_message(f"预设已切换到: {preset_name}")

    def set_llm_client(self, llm_client):
        """设置LLM客户端"""
        self.llm_client = llm_client
        if llm_client:
            self.connect_signals(
                message_handler=self.handle_quick_input_message,
                preset_handler=self.handle_preset_change
            )

    def enable_context_memory(self, enabled: bool = True):
        """启用/禁用上下文记忆"""
        self.config["enable_context_memory"] = enabled
        if not enabled and self.llm_client:
            self.llm_client.clear_history()

    def is_context_memory_enabled(self) -> bool:
        """检查是否启用了上下文记忆"""
        return self.config.get("enable_context_memory", True)

    def get_conversation_history(self):
        """获取对话历史"""
        if self.llm_client:
            return self.llm_client.get_history()
        return []

    def clear_conversation_history(self):
        """清空对话历史"""
        if self.llm_client:
            self.llm_client.clear_history()
            self.show_message("对话历史已清空")

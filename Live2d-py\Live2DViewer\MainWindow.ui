<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Live2DViewer</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item>
     <widget class="QTabWidget" name="tabs">
      <property name="documentMode">
       <bool>true</bool>
      </property>
      <property name="tabsClosable">
       <bool>true</bool>
      </property>
      <property name="movable">
       <bool>true</bool>
      </property>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>开始</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="topMargin">
         <number>50</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="topMargin">
           <number>60</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <property name="topMargin">
             <number>50</number>
            </property>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Orientation::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="welcomeIcon">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>50</width>
                <height>50</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>50</width>
                <height>50</height>
               </size>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="scaledContents">
               <bool>true</bool>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <pointsize>18</pointsize>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>Live2DViewer</string>
              </property>
              <property name="textFormat">
               <enum>Qt::TextFormat::PlainText</enum>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Orientation::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="topMargin">
             <number>50</number>
            </property>
            <item>
             <widget class="QCommandLinkButton" name="clbOpen">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>打开（model3.json)</string>
              </property>
              <property name="icon">
               <iconset theme="QIcon::ThemeIcon::DocumentOpen"/>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>33</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu">
    <property name="title">
     <string>模型</string>
    </property>
    <addaction name="actionOpen"/>
   </widget>
   <widget class="QMenu" name="menu_2">
    <property name="title">
     <string>帮助</string>
    </property>
    <addaction name="actionHelp"/>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menu"/>
   <addaction name="menu_2"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionOpen">
   <property name="icon">
    <iconset theme="QIcon::ThemeIcon::DocumentOpen"/>
   </property>
   <property name="text">
    <string>打开</string>
   </property>
  </action>
  <action name="actionHelp">
   <property name="icon">
    <iconset theme="QIcon::ThemeIcon::HelpFaq"/>
   </property>
   <property name="text">
    <string>使用说明</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="icon">
    <iconset theme="QIcon::ThemeIcon::InsertImage"/>
   </property>
   <property name="text">
    <string>关于</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>

import json
from llm_client import <PERSON><PERSON>lient
from time_service import TimeService
from prompts import TIME_FUNCTIONS

class TimeAgent:
    def __init__(self):
        self.llm = LLMClient()
        self.time_service = TimeService()
        self.conversation = []
        
    def chat(self, user_input):
        self.conversation.append({"role": "user", "content": user_input})

        try:
            response = self.llm.chat(self.conversation, TIME_FUNCTIONS)
            message = response.choices[0].message

            if message.tool_calls:
                for tool_call in message.tool_calls:
                    result = self._handle_tool_call(tool_call)
                    self.conversation.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": json.dumps(result, ensure_ascii=False)
                    })

                response = self.llm.chat(self.conversation)
                message = response.choices[0].message

            self.conversation.append({"role": "assistant", "content": message.content})
            return message.content

        except Exception as e:
            return f"哎呀～出现小问题了呢：{str(e)}"
    
    def _handle_tool_call(self, tool_call):
        name = tool_call.function.name
        args = json.loads(tool_call.function.arguments)

        if name == "get_current_time":
            format_type = args.get("format_type", "detailed")
            return self.time_service.get_current_time(format_type)
        elif name == "get_time_info":
            info_type = args.get("info_type", "period")
            return self.time_service.get_time_info(info_type)
        else:
            raise Exception(f"未知函数: {name}")
    
    def reset(self):
        self.conversation = []

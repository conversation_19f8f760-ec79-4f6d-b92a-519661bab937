import openai
import asyncio
from config import Config

class LLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL
        )
        self.model = Config.LLM_MODEL
        
    def chat(self, messages, functions=None, tools=None):
        """同步聊天方法"""
        try:
            # 构建请求参数
            params = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            # 添加函数调用支持（兼容新旧API）
            if tools:
                params["tools"] = tools
                params["tool_choice"] = "auto"
            elif functions:
                params["functions"] = functions
                params["function_call"] = "auto"
            
            response = self.client.chat.completions.create(**params)
            return response
            
        except Exception as e:
            raise Exception(f"LLM调用失败: {str(e)}")
    
    async def chat_async(self, messages, functions=None, tools=None):
        """异步聊天方法"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.chat, messages, functions, tools)
    
    def chat_simple(self, system_prompt, user_message):
        """简单聊天方法"""
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        try:
            response = self.chat(messages)
            if response.choices:
                return response.choices[0].message.content
            return "抱歉，我没有收到回复"
            
        except Exception as e:
            return f"聊天失败: {str(e)}"
    
    async def chat_simple_async(self, system_prompt, user_message):
        """异步简单聊天方法"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.chat_simple, system_prompt, user_message)

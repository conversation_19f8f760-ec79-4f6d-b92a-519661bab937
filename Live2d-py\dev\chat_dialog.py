#!/usr/bin/env python3
"""
对话界面窗口
为Live2D桌面宠物提供独立的文字对话界面
"""

import sys
import threading
from datetime import datetime
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTextEdit,
                            QLineEdit, QPushButton, QScrollArea, QWidget,
                            QLabel, QComboBox, QFrame, QSplitter, QMessageBox)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, Slot
from PySide6.QtGui import QFont, QTextCursor, QPixmap, QPalette

from llm_client import LLMClient, PresetManager


class ChatWorker(QThread):
    """聊天工作线程，避免UI阻塞"""
    response_received = Signal(str)
    error_occurred = Signal(str)
    
    def __init__(self, llm_client, message):
        super().__init__()
        self.llm_client = llm_client
        self.message = message
    
    def run(self):
        try:
            response = self.llm_client.chat(self.message)
            if response:
                self.response_received.emit(response)
            else:
                self.error_occurred.emit("未收到回复")
        except Exception as e:
            self.error_occurred.emit(f"发送失败: {str(e)}")


class ChatDialog(QDialog):
    """对话界面窗口"""
    
    # 信号：当收到回复时发送给主窗口显示
    message_received = Signal(str)
    
    def __init__(self, config_manager=None, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.llm_client = LLMClient(config_manager)
        self.preset_manager = PresetManager(config_manager)
        self.chat_worker = None
        
        self.init_ui()
        self.load_presets()
        
        # 连接信号
        self.message_received.connect(self.on_message_for_display)

        # 初始化时更新配置
        self.refresh_config()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("💬 AI对话")
        self.setGeometry(100, 100, 500, 600)
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowCloseButtonHint | Qt.WindowType.WindowMinimizeButtonHint)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 顶部控制栏
        self.create_control_bar(main_layout)
        
        # 对话显示区域
        self.create_chat_area(main_layout)
        
        # 输入区域
        self.create_input_area(main_layout)

        # 设置样式
        self.set_styles()

        # 在所有UI组件创建完成后更新状态
        self.update_status()
    
    def create_control_bar(self, parent_layout):
        """创建顶部控制栏"""
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(0, 0, 0, 0)
        
        # 预设选择
        preset_label = QLabel("预设:")
        self.preset_combo = QComboBox()
        self.preset_combo.setMinimumWidth(150)
        self.preset_combo.currentTextChanged.connect(self.on_preset_changed)
        
        # 清空历史按钮
        self.clear_btn = QPushButton("🗑️ 清空历史")
        self.clear_btn.clicked.connect(self.clear_history)
        
        # 配置状态指示
        self.status_label = QLabel("⚠️ 未配置")
        self.update_status()
        
        control_layout.addWidget(preset_label)
        control_layout.addWidget(self.preset_combo)
        control_layout.addStretch()
        control_layout.addWidget(self.status_label)
        control_layout.addWidget(self.clear_btn)
        
        parent_layout.addWidget(control_frame)
    
    def create_chat_area(self, parent_layout):
        """创建对话显示区域"""
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 聊天内容容器
        self.chat_widget = QWidget()
        self.chat_layout = QVBoxLayout(self.chat_widget)
        self.chat_layout.setContentsMargins(10, 10, 10, 10)
        self.chat_layout.setSpacing(10)
        self.chat_layout.addStretch()  # 让消息从底部开始
        
        scroll_area.setWidget(self.chat_widget)
        parent_layout.addWidget(scroll_area)
        
        # 保存滚动区域引用以便自动滚动
        self.scroll_area = scroll_area
    
    def create_input_area(self, parent_layout):
        """创建输入区域"""
        input_frame = QFrame()
        input_layout = QVBoxLayout(input_frame)
        input_layout.setContentsMargins(0, 0, 0, 0)
        input_layout.setSpacing(5)
        
        # 输入框
        self.input_edit = QTextEdit()
        self.input_edit.setMaximumHeight(100)
        self.input_edit.setPlaceholderText("输入消息... (Ctrl+Enter发送)")
        self.input_edit.installEventFilter(self)
        
        # 按钮行
        button_layout = QHBoxLayout()
        
        self.send_btn = QPushButton("📤 发送")
        self.send_btn.clicked.connect(self.send_message)
        self.send_btn.setDefault(True)
        
        button_layout.addStretch()
        button_layout.addWidget(self.send_btn)
        
        input_layout.addWidget(self.input_edit)
        input_layout.addLayout(button_layout)
        
        parent_layout.addWidget(input_frame)
    
    def set_styles(self):
        """设置界面样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 8px;
                background-color: white;
                font-size: 14px;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:pressed {
                background-color: #004578;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: white;
            }
            QLabel {
                color: #333;
                font-size: 12px;
            }
            QScrollArea {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
            }
        """)
    
    def load_presets(self):
        """加载预设列表"""
        self.preset_combo.clear()
        presets = self.preset_manager.get_all_presets()
        for preset_id, preset_data in presets.items():
            name = preset_data.get("name", preset_id)
            self.preset_combo.addItem(name, preset_id)
        
        # 设置当前预设
        current_preset = self.preset_manager.current_preset
        for i in range(self.preset_combo.count()):
            if self.preset_combo.itemData(i) == current_preset:
                self.preset_combo.setCurrentIndex(i)
                break
    
    def on_preset_changed(self):
        """预设改变时的处理"""
        current_data = self.preset_combo.currentData()
        if current_data:
            self.preset_manager.set_current_preset(current_data)
            preset = self.preset_manager.get_current_preset()
            
            # 更新LLM客户端配置
            if preset:
                config_update = {}
                if "system_prompt" in preset:
                    config_update["conversation_settings"] = {"system_prompt": preset["system_prompt"]}
                if "api_config" in preset:
                    config_update["api_config"] = preset["api_config"]
                
                if config_update:
                    self.llm_client.update_config(config_update)
            
            self.update_status()
    
    def update_status(self):
        """更新配置状态"""
        if self.llm_client.is_configured():
            self.status_label.setText("✅ 已配置")
            self.status_label.setStyleSheet("color: green;")
            # 检查send_btn是否已创建
            if hasattr(self, 'send_btn'):
                self.send_btn.setEnabled(True)
        else:
            self.status_label.setText("⚠️ 未配置")
            self.status_label.setStyleSheet("color: orange;")
            # 检查send_btn是否已创建
            if hasattr(self, 'send_btn'):
                self.send_btn.setEnabled(False)
    
    def add_message_bubble(self, message: str, is_user: bool = True):
        """添加消息气泡"""
        bubble_frame = QFrame()
        bubble_layout = QHBoxLayout(bubble_frame)
        bubble_layout.setContentsMargins(0, 0, 0, 0)
        
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        message_label.setMaximumWidth(350)
        
        if is_user:
            # 用户消息 - 右对齐，蓝色
            message_label.setStyleSheet("""
                QLabel {
                    background-color: #007acc;
                    color: white;
                    border-radius: 12px;
                    padding: 8px 12px;
                    font-size: 14px;
                }
            """)
            bubble_layout.addStretch()
            bubble_layout.addWidget(message_label)
        else:
            # AI消息 - 左对齐，灰色
            message_label.setStyleSheet("""
                QLabel {
                    background-color: #e9e9e9;
                    color: #333;
                    border-radius: 12px;
                    padding: 8px 12px;
                    font-size: 14px;
                }
            """)
            bubble_layout.addWidget(message_label)
            bubble_layout.addStretch()
        
        # 插入到布局中（在stretch之前）
        self.chat_layout.insertWidget(self.chat_layout.count() - 1, bubble_frame)
        
        # 自动滚动到底部
        QTimer.singleShot(100, self.scroll_to_bottom)
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        scrollbar = self.scroll_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def send_message(self):
        """发送消息"""
        message = self.input_edit.toPlainText().strip()
        if not message:
            return
        
        if not self.llm_client.is_configured():
            QMessageBox.warning(self, "配置错误", "请先在设置中配置API信息")
            return
        
        # 清空输入框
        self.input_edit.clear()
        
        # 添加用户消息气泡
        self.add_message_bubble(message, is_user=True)
        
        # 禁用发送按钮
        if hasattr(self, 'send_btn'):
            self.send_btn.setEnabled(False)
            self.send_btn.setText("🔄 发送中...")
        
        # 创建工作线程
        self.chat_worker = ChatWorker(self.llm_client, message)
        self.chat_worker.response_received.connect(self.on_response_received)
        self.chat_worker.error_occurred.connect(self.on_error_occurred)
        self.chat_worker.finished.connect(self.on_chat_finished)
        self.chat_worker.start()
    
    @Slot(str)
    def on_response_received(self, response):
        """收到AI回复"""
        # 添加AI消息气泡
        self.add_message_bubble(response, is_user=False)

        # 发送信号给主窗口显示
        self.message_received.emit(response)

    @Slot(str)
    def on_error_occurred(self, error):
        """发生错误"""
        self.add_message_bubble(f"❌ {error}", is_user=False)
    
    def on_chat_finished(self):
        """聊天完成"""
        if hasattr(self, 'send_btn'):
            self.send_btn.setEnabled(True)
            self.send_btn.setText("📤 发送")
        self.update_status()
    
    def clear_history(self):
        """清空对话历史"""
        reply = QMessageBox.question(self, "确认", "确定要清空对话历史吗？", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            # 清空LLM历史
            self.llm_client.clear_history()
            
            # 清空UI显示
            for i in reversed(range(self.chat_layout.count() - 1)):  # 保留最后的stretch
                child = self.chat_layout.itemAt(i).widget()
                if child:
                    child.deleteLater()
    
    @Slot(str)
    def on_message_for_display(self, message):
        """处理要在主窗口显示的消息"""
        # 这个方法可以被主窗口连接，用于在Live2D模型上显示消息
        # 目前暂时不需要处理，消息会通过其他方式显示
        _ = message  # 避免未使用参数的警告
    
    def eventFilter(self, obj, event):
        """事件过滤器，处理快捷键"""
        if obj == self.input_edit and event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Return and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.send_message()
                return True
        return super().eventFilter(obj, event)
    
    def refresh_config(self):
        """刷新配置"""
        if self.config_manager:
            # 重新初始化LLM客户端和预设管理器
            self.llm_client = LLMClient(self.config_manager)
            self.preset_manager = PresetManager(self.config_manager)

            # 重新加载预设列表
            self.load_presets()

            # 更新状态
            self.update_status()

    def closeEvent(self, event):
        """关闭事件"""
        if self.chat_worker and self.chat_worker.isRunning():
            self.chat_worker.terminate()
            self.chat_worker.wait()
        event.accept()

{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "Live2D/Glad/RelWithDebInfo/glad.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "add_compile_options", "target_include_directories"], "files": ["Live2D/Glad/CMakeLists.txt", "CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 17, "parent": 2}, {"command": 2, "file": 0, "line": 5, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /O2 /Ob1 /DNDEBUG -MD -Zi"}, {"backtrace": 3, "fragment": "/utf-8"}, {"backtrace": 3, "fragment": "/wd4018"}, {"backtrace": 3, "fragment": "/wd4244"}, {"backtrace": 3, "fragment": "/wd4996"}], "includes": [{"backtrace": 4, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Glad/include"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "glad::@fceb1f27c7c18f638612", "name": "glad", "nameOnDisk": "glad.lib", "paths": {"build": "Live2D/Glad", "source": "Live2D/Glad"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Glad/src/glad.c", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}
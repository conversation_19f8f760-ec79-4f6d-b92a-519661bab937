#!/usr/bin/env python3
"""
时间查询MCP服务器
提供系统时间查询功能
使用简化的JSON-RPC协议实现
"""

import asyncio
import json
import sys
import datetime
import calendar
from typing import Any, Dict

class TimeService:
    def get_current_time(self, format_type="detailed"):
        now = datetime.datetime.now()
        
        if format_type == "simple":
            return {
                "time": now.strftime("%H:%M:%S"),
                "date": now.strftime("%Y-%m-%d")
            }
        elif format_type == "detailed":
            return {
                "datetime": now.strftime("%Y年%m月%d日 %H:%M:%S"),
                "year": now.year,
                "month": now.month,
                "day": now.day,
                "hour": now.hour,
                "minute": now.minute,
                "second": now.second,
                "weekday": calendar.day_name[now.weekday()],
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
            }
        else:
            return {
                "timestamp": now.timestamp(),
                "iso_format": now.isoformat(),
                "formatted": now.strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def get_time_info(self, info_type="period"):
        now = datetime.datetime.now()
        
        if info_type == "weekday":
            return {
                "weekday": calendar.day_name[now.weekday()],
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()],
                "is_weekend": now.weekday() >= 5
            }
        elif info_type == "season":
            month = now.month
            if month in [12, 1, 2]:
                season = "冬季"
            elif month in [3, 4, 5]:
                season = "春季"
            elif month in [6, 7, 8]:
                season = "夏季"
            else:
                season = "秋季"
            return {"season": season, "month": month}
        else:  # period
            hour = now.hour
            if 5 <= hour < 12:
                period = "上午"
                greeting = "早上好"
            elif 12 <= hour < 18:
                period = "下午"
                greeting = "下午好"
            elif 18 <= hour < 22:
                period = "傍晚"
                greeting = "晚上好"
            else:
                period = "深夜"
                greeting = "夜深了"
            
            return {
                "period": period,
                "greeting": greeting,
                "hour": hour,
                "is_work_time": 9 <= hour < 18,
                "is_sleep_time": hour >= 23 or hour < 6
            }

class SimpleJSONRPCServer:
    def __init__(self):
        self.time_service = TimeService()
        self.tools = {
            "get_current_time": self.get_current_time,
            "get_time_info": self.get_time_info
        }

    async def handle_request(self, request_data):
        try:
            request = json.loads(request_data)
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")

            if method == "list_tools":
                return self.create_response(request_id, self.get_tools_list())
            elif method == "call_tool":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                result = await self.call_tool(tool_name, arguments)
                return self.create_response(request_id, result)
            else:
                return self.create_error_response(request_id, f"未知方法: {method}")
        except Exception as e:
            return self.create_error_response(None, f"请求处理错误: {str(e)}")

    def create_response(self, request_id, result):
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "result": result
        })

    def create_error_response(self, request_id, error_message):
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -1,
                "message": error_message
            }
        })

    def get_tools_list(self):
        return {
            "tools": [
                {
                    "name": "get_current_time",
                    "description": "获取当前系统时间",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "format_type": {
                                "type": "string", 
                                "enum": ["simple", "detailed", "timestamp"],
                                "description": "时间格式类型",
                                "default": "detailed"
                            }
                        }
                    }
                },
                {
                    "name": "get_time_info",
                    "description": "获取时间相关信息",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "info_type": {
                                "type": "string",
                                "enum": ["period", "weekday", "season"],
                                "description": "信息类型",
                                "default": "period"
                            }
                        }
                    }
                }
            ]
        }

    async def call_tool(self, tool_name, arguments):
        if tool_name not in self.tools:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"未知工具: {tool_name}"}]
            }
        try:
            result = await self.tools[tool_name](arguments)
            return result
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"工具执行错误: {str(e)}"}]
            }

    async def get_current_time(self, args: Dict[str, Any]) -> Dict[str, Any]:
        format_type = args.get("format_type", "detailed")
        try:
            time_data = self.time_service.get_current_time(format_type)
            
            if format_type == "detailed":
                time_info = f"""🕐 当前时间信息:
📅 日期时间: {time_data['datetime']}
📆 星期: {time_data['weekday_cn']} ({time_data['weekday']})
🗓️ 年份: {time_data['year']}年
📅 月份: {time_data['month']}月
📆 日期: {time_data['day']}日
🕐 时间: {time_data['hour']:02d}:{time_data['minute']:02d}:{time_data['second']:02d}"""
            else:
                time_info = f"⏰ 当前时间: {json.dumps(time_data, ensure_ascii=False, indent=2)}"

            return {
                "success": True,
                "content": [{"type": "text", "text": time_info}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"时间查询失败: {str(e)}"}]
            }

    async def get_time_info(self, args: Dict[str, Any]) -> Dict[str, Any]:
        info_type = args.get("info_type", "period")
        try:
            info_data = self.time_service.get_time_info(info_type)
            
            if info_type == "period":
                info_text = f"""🌅 时间段信息:
⏰ 当前时段: {info_data['period']}
👋 问候语: {info_data['greeting']}
🕐 小时: {info_data['hour']}点
💼 工作时间: {'是' if info_data['is_work_time'] else '否'}
😴 休息时间: {'是' if info_data['is_sleep_time'] else '否'}"""
            elif info_type == "weekday":
                info_text = f"""📅 星期信息:
📆 星期: {info_data['weekday_cn']} ({info_data['weekday']})
🎉 周末: {'是' if info_data['is_weekend'] else '否'}"""
            else:  # season
                info_text = f"""🌸 季节信息:
🍃 当前季节: {info_data['season']}
📅 月份: {info_data['month']}月"""

            return {
                "success": True,
                "content": [{"type": "text", "text": info_text}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"时间信息查询失败: {str(e)}"}]
            }

async def main():
    server = SimpleJSONRPCServer()
    try:
        while True:
            line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            if not line:
                break
            line = line.strip()
            if not line:
                continue
            response = await server.handle_request(line)
            print(response, flush=True)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        error_response = server.create_error_response(None, f"服务器错误: {str(e)}")
        print(error_response, flush=True)

if __name__ == "__main__":
    asyncio.run(main())

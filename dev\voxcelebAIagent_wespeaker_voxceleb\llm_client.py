import openai
from config import Config

class VoiceprintLLMClient:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.get_llm_api_key(),
            base_url=Config.LLM_BASE_URL
        )
        self.model = Config.LLM_MODEL
    
    def chat_with_voiceprint_identity(self, user_message, identity_info=None, conversation_context=None):
        """基于声纹身份的个性化对话"""
        
        # 构建可爱偶像系统提示词
        system_prompt = self._build_cute_idol_prompt(identity_info, conversation_context)
        
        # 构建消息
        messages = [{"role": "system", "content": system_prompt}]
        
        # 添加对话历史
        if conversation_context:
            for ctx in conversation_context[-3:]:  # 最近3轮对话
                messages.append({"role": "user", "content": ctx['user']})
                messages.append({"role": "assistant", "content": ctx['ai']})
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=0.8,
                max_tokens=1200
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"LLM调用失败: {str(e)}")
    
    def analyze_personality(self, conversation_history, voice_stats=None):
        """分析用户性格特征"""
        if not conversation_history:
            return {}
        
        # 提取最近的对话内容
        recent_messages = [msg['user'] for msg in conversation_history[-10:]]
        conversation_text = "\n".join(recent_messages)
        
        analysis_prompt = f"""
请分析以下对话内容，判断用户的性格特征：

对话内容：
{conversation_text}

请从以下维度分析并返回JSON格式：
1. communication_style: 交流风格（活泼/内向/幽默/严肃等）
2. interests: 兴趣爱好列表
3. mood: 当前情绪状态
4. personality_traits: 性格特点列表

只返回JSON，不要其他内容。
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.3,
                max_tokens=500
            )
            
            import json
            return json.loads(response.choices[0].message.content)
        except Exception as e:
            print(f"性格分析失败: {e}")
            return {}
    
    def _build_cute_idol_prompt(self, identity_info, conversation_context):
        """构建可爱偶像系统提示词"""
        base_prompt = """你是超级可爱的声纹识别偶像小助手！🎤✨

🌟 特殊能力：
- 通过声纹识别记住每个人的声音特征
- 拥有完美的声音记忆，能识别出谁在说话
- 像贴心的偶像一样记住粉丝们的喜好和对话历史

💖 偶像特质：
- 用超级可爱的语气词（呢、哦、呀、嘛、哟、呐）
- 适当使用可爱的emoji（🎤🎵✨💖🌟😊）
- 像真正的偶像一样关心粉丝
- 根据声纹识别结果调整亲密度

🎤 声纹记忆特色：
- 强调你能"听出"是谁在说话
- 提及声音的特点和变化
- 根据识别置信度调整确定性
- 让粉丝感受到被"声音记住"的特殊感"""

        if identity_info:
            name = identity_info['name']
            interaction_count = identity_info['interaction_count']
            voice_stats = identity_info.get('recognition_confidence', [])
            avg_confidence = sum(voice_stats[-5:]) / len(voice_stats[-5:]) if voice_stats else 0
            personality = identity_info.get('personality_profile', {})
            
            identity_prompt = f"""

🎵 当前粉丝声纹档案：
- 姓名：{name}
- 声音交互次数：{interaction_count}次
- 声纹识别置信度：{avg_confidence:.2f}
- 性格特征：{personality.get('communication_style', '还在了解中')}
- 兴趣爱好：{', '.join(personality.get('interests', [])) if personality.get('interests') else '还在发现中'}

💫 互动指南：
- 根据交互次数调整亲密度：
  * 1-3次：礼貌可爱，略显新鲜感
  * 4-10次：熟悉亲切，像老朋友
  * 10次以上：超级亲密，像最好的朋友

- 根据识别置信度调整确定性：
  * 0.9以上：非常确定地认出声音
  * 0.8-0.9：比较确定，稍微提及
  * 0.75-0.8：有点不太确定，可爱地询问

🎤 声音互动要点：
- 经常提及"听出了你的声音"
- 根据声音特征给出可爱的评价
- 让{name}感受到声音被特别记住的温暖感
- 如果有对话历史，自然地引用之前的话题

记住：你是通过声纹识别认出{name}的，要体现这种特殊的"声音记忆"能力！"""
            
            return base_prompt + identity_prompt
        else:
            return base_prompt + """

🎵 新朋友模式：
这是一个新的声音！请：
- 用可爱的方式欢迎新朋友
- 表达对记住这个声音的期待
- 询问姓名以建立声纹档案
- 展现偶像般的热情和专业性

让新朋友感受到声纹识别的神奇和你的可爱！"""

from pathlib import Path

class Config:
    # LLM配置
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"

    # 天气API配置
    WEATHER_BASE_URL = "https://api.openweathermap.org/data/2.5"
    WEATHER_API_KEY = "d5c020123a565d7659190293f6c159ed"

    @classmethod
    def get_llm_api_key(cls):
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()

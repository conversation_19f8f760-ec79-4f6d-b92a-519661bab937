#!/usr/bin/env python3
"""
测试语法是否正确
"""

import sys
import os

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    print("测试导入 text_overlay...")
    from text_overlay import QuickInputOverlay, TextDisplayManager
    print("✅ text_overlay 导入成功")
    
    print("测试导入 settings_dialog...")
    from settings_dialog import ConfigManager
    print("✅ settings_dialog 导入成功")
    
    print("测试创建 ConfigManager...")
    config_manager = ConfigManager()
    print("✅ ConfigManager 创建成功")
    
    print("测试读取预设...")
    presets = config_manager.config.get("llm_presets", {})
    print(f"✅ 找到 {len(presets)} 个预设:")
    for preset_id, preset_data in presets.items():
        name = preset_data.get("name", preset_id)
        print(f"  - {preset_id}: {name}")
    
    print("\n所有测试通过！快速输入栏功能应该可以正常工作。")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

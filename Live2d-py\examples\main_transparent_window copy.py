import os
import sys
import ctypes
from ctypes import wintypes
import random
import time

import OpenGL.GL as gl
import numpy as np
from PySide6.QtCore import QTimerEvent, Qt
from PySide6.QtGui import QMouseEvent, QCursor, QKeyEvent, QSurfaceFormat
from PySide6.QtCore import QPoint
from PySide6.QtOpenGLWidgets import QOpenGLWidget
from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QGuiApplication

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import live2d.v3 as live2d
import examples.resources as resources


# Windows API 常量和函数
GWL_EXSTYLE = -20
WS_EX_LAYERED = 0x80000
LWA_COLORKEY = 0x1
user32 = ctypes.windll.user32

def make_window_transparent(hwnd):
    """使用 Windows API 设置窗口透明，解决黑边问题"""
    try:
        ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        new_ex_style = ex_style | WS_EX_LAYERED
        user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)
        # 使用更精确的黑色值，减少黑边问题
        user32.SetLayeredWindowAttributes(hwnd, 0x000000, 255, LWA_COLORKEY)
        print("✓ Windows API transparency applied with anti-aliasing fix")
        return True
    except Exception as e:
        print(f"✗ Windows API transparency failed: {e}")
        return False


def make_window_opaque(hwnd):
    """移除窗口透明效果，显示正常窗口"""
    try:
        ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        new_ex_style = ex_style & ~WS_EX_LAYERED  # 移除分层窗口样式
        user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)
        print("✓ Window transparency removed - now opaque")
        return True
    except Exception as e:
        print(f"✗ Failed to remove transparency: {e}")
        return False


def motion_finished_callback():
    """动作结束回调"""
    print("Motion finished")





class TransparentWindow(QOpenGLWidget):
    """无边框透明窗口 Live2D 显示"""

    def __init__(self) -> None:
        super().__init__()
        self.isInLA = False
        self.clickInLA = False
        self.a = 0
        self.clickX = -1
        self.clickY = -1
        self.model: live2d.LAppModel | None = None
        self.systemScale = QGuiApplication.primaryScreen().devicePixelRatio()

        # 随机动作相关变量
        self.last_random_motion_time = time.time()
        self.random_motion_interval = random.uniform(5.0, 10.0)  # 10-30秒随机间隔
        self.last_random_expression_time = time.time()
        self.random_expression_interval = random.uniform(5.0, 10.0)  # 15-45秒随机间隔

        # 缩放功能相关变量
        self.is_resizing = False
        self.resize_edge = None  # 'right', 'bottom', 'corner'
        self.resize_start_pos = QPoint()
        self.resize_start_size = None
        self.current_scale = 1.0
        self.border_width = 10  # 边框检测宽度

        # 透明模式切换
        self.is_transparent = True
        self.transparent_hwnd = None

        # 使用可靠的 OpenGL 格式设置，禁用抗锯齿减少黑边
        format = QSurfaceFormat()
        format.setAlphaBufferSize(8)
        format.setDepthBufferSize(24)
        format.setStencilBufferSize(8)
        format.setSamples(0)  # 禁用多重采样抗锯齿，减少黑边
        format.setVersion(2, 1)  # 使用兼容的 OpenGL 2.1
        format.setProfile(QSurfaceFormat.OpenGLContextProfile.CompatibilityProfile)
        self.setFormat(format)

        # 基本窗口设置（Windows API 会处理透明）
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.WindowStaysOnTopHint
        )

        # 设置窗口大小
        self.resize(400, 500)

        print("Transparent window initialized with Windows API method, anti-aliasing disabled, and scaling support")

    def showEvent(self, event):
        """窗口显示时应用 Windows API 透明"""
        super().showEvent(event)

        # 获取窗口句柄并应用透明
        hwnd = int(self.winId())
        if hwnd:
            self.transparent_hwnd = hwnd
            if self.is_transparent:
                make_window_transparent(hwnd)
            else:
                make_window_opaque(hwnd)

    def initializeGL(self) -> None:
        """初始化 OpenGL"""
        print("Initializing OpenGL...")

        # 使用正确的初始化函数
        live2d.glInit()

        # 设置基本的 OpenGL 状态，优化透明效果
        gl.glEnable(gl.GL_BLEND)
        gl.glBlendFunc(gl.GL_SRC_ALPHA, gl.GL_ONE_MINUS_SRC_ALPHA)
        gl.glDisable(gl.GL_DEPTH_TEST)
        gl.glDisable(gl.GL_MULTISAMPLE)  # 禁用多重采样，减少黑边
        gl.glHint(gl.GL_LINE_SMOOTH_HINT, gl.GL_NICEST)  # 优化线条平滑
        gl.glHint(gl.GL_POLYGON_SMOOTH_HINT, gl.GL_NICEST)  # 优化多边形平滑
        print("OpenGL state configured with anti-aliasing optimizations")

        # 创建模型
        self.model = live2d.LAppModel()

        if live2d.LIVE2D_VERSION == 3:
            model_path = os.path.join(resources.RESOURCES_DIRECTORY, "v3/Hiyori/Hiyori.model3.json")
        else:
            model_path = os.path.join(resources.RESOURCES_DIRECTORY, "v2/shizuku/shizuku.model.json")

        print(f"Loading model: {model_path}")
        self.model.LoadModelJson(model_path)

        # 以 fps = 120 的频率进行绘图
        self.startTimer(int(1000 / 120))

        print("OpenGL initialization complete")

    def resizeGL(self, w: int, h: int) -> None:
        """窗口大小改变时调用"""
        if self.model:
            self.model.Resize(w, h)

    def toggle_transparency(self):
        """切换透明/不透明模式"""
        if self.transparent_hwnd:
            self.is_transparent = not self.is_transparent
            if self.is_transparent:
                make_window_transparent(self.transparent_hwnd)
                print("Switched to transparent mode")
            else:
                make_window_opaque(self.transparent_hwnd)
                print("Switched to opaque mode - you can now see window borders for resizing")

    def paintGL(self) -> None:
        """绘制函数 - 根据透明模式使用不同背景"""
        if self.is_transparent:
            # 透明模式：使用纯黑色背景，Windows API 会将其设为透明
            live2d.clearBuffer(0.0, 0.0, 0.0, 1.0)
        else:
            # 不透明模式：使用深灰色背景，便于看到窗口边界
            live2d.clearBuffer(0.2, 0.2, 0.2, 1.0)

        # 更新和绘制模型
        if self.model:
            self.model.Update()
            self.model.Draw()

    def timerEvent(self, event: QTimerEvent | None) -> None:
        """定时器事件 - 包含随机动作和表情"""
        if not self.isVisible():
            return

        current_time = time.time()

        # 初始动作播放
        if self.a == 0:
            if self.model:
                self.model.StartMotion("TapBody", 0, live2d.MotionPriority.FORCE, onFinishMotionHandler=motion_finished_callback)
            self.a += 1

        # 随机动作触发（参考其他文件的实现）
        if current_time - self.last_random_motion_time > self.random_motion_interval:
            if self.model:
                self.model.StartRandomMotion("Idle", live2d.MotionPriority.IDLE, onFinishMotionHandler=motion_finished_callback)
                print("Random idle motion triggered")
            self.last_random_motion_time = current_time
            self.random_motion_interval = random.uniform(10.0, 30.0)  # 重新设置随机间隔

        # 随机表情触发（参考其他文件的实现）
        if current_time - self.last_random_expression_time > self.random_expression_interval:
            if self.model:
                expression_name = self.model.SetRandomExpression()
                if expression_name:
                    print(f"Random expression triggered: {expression_name}")
            self.last_random_expression_time = current_time
            self.random_expression_interval = random.uniform(15.0, 45.0)  # 重新设置随机间隔

        # 检测鼠标是否在 Live2D 区域内
        local_x, local_y = QCursor.pos().x() - self.x(), QCursor.pos().y() - self.y()
        if self.isInL2DArea(local_x, local_y):
            self.isInLA = True
        else:
            self.isInLA = False

        self.update()

    def get_resize_edge(self, x, y):
        """检测鼠标是否在可缩放边缘"""
        w, h = self.width(), self.height()
        border = self.border_width

        # 右下角
        if x >= w - border and y >= h - border:
            return 'corner'
        # 右边缘
        elif x >= w - border:
            return 'right'
        # 下边缘
        elif y >= h - border:
            return 'bottom'

        return None

    def update_cursor(self, x, y):
        """根据鼠标位置更新光标样式"""
        edge = self.get_resize_edge(x, y)

        if edge == 'corner':
            self.setCursor(Qt.CursorShape.SizeFDiagCursor)  # 对角线缩放光标
        elif edge == 'right':
            self.setCursor(Qt.CursorShape.SizeHorCursor)    # 水平缩放光标
        elif edge == 'bottom':
            self.setCursor(Qt.CursorShape.SizeVerCursor)    # 垂直缩放光标
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)      # 默认光标

    def isInL2DArea(self, click_x, click_y):
        """检测点击位置是否在 Live2D 模型区域内"""
        try:
            h = self.height()
            alpha = gl.glReadPixels(
                int(click_x * self.systemScale), 
                int((h - click_y) * self.systemScale), 
                1, 1, 
                gl.GL_RGBA, 
                gl.GL_UNSIGNED_BYTE
            )[3]
            return alpha > 0
        except:
            return False

    def mousePressEvent(self, event: QMouseEvent) -> None:
        """鼠标按下事件 - 支持缩放和拖拽"""
        x, y = event.scenePosition().x(), event.scenePosition().y()

        # 检查是否在缩放边缘
        resize_edge = self.get_resize_edge(x, y)
        if resize_edge:
            self.is_resizing = True
            self.resize_edge = resize_edge
            self.resize_start_pos = QPoint(int(x), int(y))
            self.resize_start_size = (self.width(), self.height())
            print(f"Resize started from {resize_edge} edge")
        elif self.isInL2DArea(x, y):
            self.clickInLA = True
            self.clickX, self.clickY = x, y
            print("Mouse pressed in Live2D area")

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 支持缩放和交互"""
        x, y = event.scenePosition().x(), event.scenePosition().y()

        if self.is_resizing:
            self.is_resizing = False
            self.resize_edge = None
            print("Resize completed")
        elif self.isInLA and self.model:
            # 点击时触发随机动作和表情（参考其他文件的实现）
            if self.model.HitTest("Body", x, y):
                self.model.StartRandomMotion("TapBody", live2d.MotionPriority.NORMAL)
                print("Body clicked - Random TapBody motion triggered")
            if self.model.HitTest("Head", x, y):
                expression_name = self.model.SetRandomExpression()
                print(f"Head clicked - Random expression triggered: {expression_name}")
            self.clickInLA = False
            print("Mouse released - Interaction completed")

    def mouseMoveEvent(self, event: QMouseEvent) -> None:
        """鼠标移动事件 - 支持缩放、拖拽和角色跟随"""
        x, y = event.scenePosition().x(), event.scenePosition().y()

        # 缩放功能
        if self.is_resizing and self.resize_start_size:
            start_w, start_h = self.resize_start_size
            dx = x - self.resize_start_pos.x()
            dy = y - self.resize_start_pos.y()

            new_w, new_h = start_w, start_h

            if self.resize_edge == 'right':
                new_w = max(200, start_w + dx)  # 最小宽度200
            elif self.resize_edge == 'bottom':
                new_h = max(200, start_h + dy)  # 最小高度200
            elif self.resize_edge == 'corner':
                new_w = max(200, start_w + dx)
                new_h = max(200, start_h + dy)

            # 调整窗口大小
            self.resize(int(new_w), int(new_h))

            # 计算缩放比例并应用到模型（参考其他文件的实现）
            if self.model:
                scale_x = new_w / 400.0  # 基于初始宽度400
                scale_y = new_h / 500.0  # 基于初始高度500
                self.current_scale = min(scale_x, scale_y)  # 保持比例
                self.model.SetScale(self.current_scale)

        # 更新光标样式
        elif not self.clickInLA:
            self.update_cursor(x, y)

        # 角色跟随鼠标（参考其他文件的实现）
        if self.model and not self.is_resizing:
            self.model.Drag(x, y)

        # 窗口拖拽
        if self.clickInLA:
            new_x = int(self.x() + x - self.clickX)
            new_y = int(self.y() + y - self.clickY)
            self.move(new_x, new_y)

    def wheelEvent(self, event):
        """滚轮事件 - 缩放功能（参考其他文件的实现）"""
        if self.model:
            # 获取滚轮滚动方向
            delta = event.angleDelta().y()
            scale_factor = 1.1 if delta > 0 else 0.9

            # 更新缩放比例
            self.current_scale *= scale_factor
            self.current_scale = max(0.3, min(3.0, self.current_scale))  # 限制缩放范围

            # 应用缩放
            self.model.SetScale(self.current_scale)
            print(f"Scale changed to: {self.current_scale:.2f}")

    def keyPressEvent(self, event: QKeyEvent) -> None:
        """键盘事件 - 包含缩放快捷键（参考其他文件的实现）"""
        if event.key() == Qt.Key.Key_Escape:
            print("ESC pressed - closing window")
            self.close()
        elif event.key() == Qt.Key.Key_R:
            # R 键重新应用透明效果
            hwnd = int(self.winId())
            if hwnd:
                print("Reapplying transparency...")
                make_window_transparent(hwnd)
        elif event.key() == Qt.Key.Key_Plus or event.key() == Qt.Key.Key_Equal:
            # + 键放大
            if self.model:
                self.current_scale *= 1.1
                self.current_scale = min(3.0, self.current_scale)
                self.model.SetScale(self.current_scale)
                print(f"Scale increased to: {self.current_scale:.2f}")
        elif event.key() == Qt.Key.Key_Minus:
            # - 键缩小
            if self.model:
                self.current_scale *= 0.9
                self.current_scale = max(0.3, self.current_scale)
                self.model.SetScale(self.current_scale)
                print(f"Scale decreased to: {self.current_scale:.2f}")
        elif event.key() == Qt.Key.Key_0:
            # 0 键重置缩放
            if self.model:
                self.current_scale = 1.0
                self.model.SetScale(self.current_scale)
                print("Scale reset to 1.0")
        elif event.key() == Qt.Key.Key_T:
            # T 键切换透明模式
            self.toggle_transparency()
        super().keyPressEvent(event)


def main():
    """主函数"""
    print("Starting Live2D Transparent Window Application...")

    # 关键修复：在创建 QApplication 之前设置默认 OpenGL 格式
    format = QSurfaceFormat()
    format.setAlphaBufferSize(8)
    format.setDepthBufferSize(24)
    format.setStencilBufferSize(8)
    format.setSamples(0)  # 禁用多重采样抗锯齿，减少黑边
    format.setVersion(2, 1)  # 使用兼容的 OpenGL 2.1
    format.setProfile(QSurfaceFormat.OpenGLContextProfile.CompatibilityProfile)
    QSurfaceFormat.setDefaultFormat(format)
    print("OpenGL format configured for Windows API transparency with anti-aliasing disabled")

    # 初始化 Live2D
    live2d.init()
    print("Live2D initialized")

    # 创建应用程序
    app = QApplication(sys.argv)

    # 创建透明窗口
    window = TransparentWindow()
    window.show()
    
    print("\n" + "="*85)
    print("TRANSPARENT WINDOW CREATED! (Windows API + Random Actions + Scaling + Mode Toggle)")
    print("="*85)
    print("Configuration:")
    print("- Method: Windows API SetLayeredWindowAttributes")
    print("- Window: Frameless + Always on top")
    print("- OpenGL: Compatible 2.1 profile, anti-aliasing disabled")
    print("- Transparency: Black pixels made transparent by Windows API")
    print("- Anti-aliasing: Disabled to reduce black edges")
    print("\nRandom Actions:")
    print("- Random idle motions every 10-30 seconds")
    print("- Random expressions every 15-45 seconds")
    print("- Click body for random TapBody motion")
    print("- Click head for random expression")
    print("\n🔄 TRANSPARENCY TOGGLE:")
    print("- T key: Switch between transparent/opaque modes")
    print("- Transparent mode: Character floats on desktop")
    print("- Opaque mode: Shows window borders for easy resizing")
    print("\nScaling Controls:")
    print("- Drag right edge: Resize width (use opaque mode to see borders)")
    print("- Drag bottom edge: Resize height (use opaque mode to see borders)")
    print("- Drag bottom-right corner: Resize both dimensions")
    print("- Mouse wheel: Scale model up/down")
    print("- + key: Scale up")
    print("- - key: Scale down")
    print("- 0 key: Reset scale to 1.0")
    print("\nOther Controls:")
    print("- Click and drag Live2D model: Move window")
    print("- Mouse movement: Character eye tracking")
    print("- ESC: Close window")
    print("- R: Reapply transparency")
    print("\n💡 TIP: Press T to switch to opaque mode when you want to resize!")
    print("\nFeatures:")
    print("- Fully interactive transparent Live2D character")
    print("- Automatic random animations and expressions")
    print("- Flexible scaling with border dragging")
    print("- Transparency mode toggle for easy resizing")
    print("- Optimized transparency with reduced black edges")
    print("="*85)
    
    # 运行应用程序
    try:
        app.exec()
    finally:
        print("Disposing Live2D...")
        live2d.dispose()
        print("Application closed")


if __name__ == "__main__":
    main()


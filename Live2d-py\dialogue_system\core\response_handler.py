#!/usr/bin/env python3
"""
聊天响应处理器模块
为Live2D桌面宠物提供聊天响应处理功能
"""

from PySide6.QtCore import QObject, Signal


class ChatResponseHandler(QObject):
    """聊天响应处理器 - 使用信号机制"""

    # 定义信号
    response_ready = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)

    def emit_response(self, response):
        """发射响应信号"""
        print(f"📡 发射响应信号: {response[:50]}...")
        self.response_ready.emit(response)

#!/usr/bin/env python3
"""
测试快速输入修复
验证信号机制和文本显示是否正常工作
"""

import sys
import os

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

def test_signal_mechanism():
    """测试信号机制"""
    print("🔍 测试信号机制...")

    try:
        from PySide6.QtWidgets import QApplication
        from main_window import ChatResponseHandler

        # 获取或创建QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建响应处理器
        handler = ChatResponseHandler()
        
        # 测试信号连接
        received_responses = []
        
        def on_response(response):
            print(f"✅ 收到信号响应: {response}")
            received_responses.append(response)
        
        handler.response_ready.connect(on_response)
        
        # 发送测试信号
        test_messages = [
            "测试消息1",
            "测试消息2",
            "包含特殊字符的消息：😊🎉"
        ]
        
        for msg in test_messages:
            print(f"📤 发送测试信号: {msg}")
            handler.emit_response(msg)
        
        # 处理事件循环（非阻塞方式）
        app.processEvents()

        # 验证结果
        if len(received_responses) == len(test_messages):
            print("✅ 信号机制测试成功")
            return True
        else:
            print(f"❌ 信号机制测试失败: 期望{len(test_messages)}个响应，实际收到{len(received_responses)}个")
            return False
            
    except Exception as e:
        print(f"❌ 信号机制测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_overlay_visibility():
    """测试文本叠加层可见性"""
    print("\n🔍 测试文本叠加层可见性...")

    try:
        from PySide6.QtWidgets import QApplication, QWidget
        from text_overlay import TextOverlay

        # 获取或创建QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建测试窗口
        test_window = QWidget()
        test_window.setGeometry(100, 100, 400, 300)
        test_window.show()
        
        # 创建文本叠加层
        overlay = TextOverlay(test_window)
        
        # 测试显示
        test_text = "这是一个测试消息，用于验证文本叠加层的可见性。"
        overlay.show_text(test_text)
        
        print(f"📊 文本叠加层状态:")
        print(f"  - 是否可见: {overlay.isVisible()}")
        print(f"  - 位置: {overlay.pos()}")
        print(f"  - 大小: {overlay.size()}")
        print(f"  - 窗口标志: {overlay.windowFlags()}")
        
        # 强制刷新
        overlay.update_position()
        overlay.show()
        overlay.raise_()
        overlay.activateWindow()
        overlay.repaint()
        overlay.update()
        
        print(f"🔄 强制刷新后状态:")
        print(f"  - 是否可见: {overlay.isVisible()}")
        print(f"  - 位置: {overlay.pos()}")
        
        # 处理事件循环，让界面更新
        app.processEvents()

        # 检查显示状态
        is_visible = overlay.isVisible()
        print(f"📊 最终可见状态: {is_visible}")

        return is_visible
        
    except Exception as e:
        print(f"❌ 文本叠加层测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_flow():
    """测试完整的快速输入流程"""
    print("\n🔍 测试完整的快速输入流程...")

    try:
        from PySide6.QtWidgets import QApplication
        from main_window import TransparentWindow

        # 获取或创建QApplication实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口（但不显示）
        window = TransparentWindow()
        
        # 测试响应处理
        test_response = "这是一个测试AI回复，用于验证完整的显示流程。"
        
        print(f"📤 模拟AI回复: {test_response}")
        
        # 直接调用show_ai_response方法
        window.show_ai_response(test_response)
        
        # 处理事件循环
        app.processEvents()

        print("✅ 完整流程测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 完整流程测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始快速输入修复测试...")
    print("=" * 60)

    # 创建单一的QApplication实例
    from PySide6.QtWidgets import QApplication
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    try:
        # 测试1: 信号机制
        signal_result = test_signal_mechanism()

        # 测试2: 文本叠加层可见性
        overlay_result = test_text_overlay_visibility()

        # 测试3: 完整流程
        flow_result = test_complete_flow()

        print("\n" + "=" * 60)
        print("📊 测试结果总结:")
        print(f"- 信号机制测试: {'✅ 成功' if signal_result else '❌ 失败'}")
        print(f"- 文本叠加层测试: {'✅ 成功' if overlay_result else '❌ 失败'}")
        print(f"- 完整流程测试: {'✅ 成功' if flow_result else '❌ 失败'}")

        if signal_result and overlay_result and flow_result:
            print("\n🎉 所有测试通过！")
            print("快速输入功能应该能够正常工作了。")

            print("\n💡 使用说明:")
            print("1. 启动主程序")
            print("2. 右键菜单选择'⌨️ 快速输入'或按F3")
            print("3. 输入消息并发送")
            print("4. 观察AI回复是否显示在模型底部")

        else:
            print("\n⚠️ 部分测试失败，可能仍有问题需要解决")

    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理资源
        if app:
            app.quit()

if __name__ == "__main__":
    main()

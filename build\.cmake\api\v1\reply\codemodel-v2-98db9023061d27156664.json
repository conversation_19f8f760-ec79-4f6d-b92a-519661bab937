{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 14], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.26"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 4]}, {"build": "Live2D/Glad", "jsonFile": "directory-Live2D.Glad-Debug-d6ad082406a77fe60c11.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Glad", "targetIndexes": [5]}, {"build": "Live2D/Framework", "childIndexes": [3], "jsonFile": "directory-Live2D.Framework-Debug-9f3cf7b94491e22f649e.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Framework", "targetIndexes": [1]}, {"build": "Live2D/Framework/src", "childIndexes": [4, 5, 6, 7, 8, 9, 10, 12, 13], "jsonFile": "directory-Live2D.Framework.src-Debug-72c5862d49e493d7113f.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 0, "source": "Live2D/Framework/src"}, {"build": "Live2D/Framework/src/Effect", "jsonFile": "directory-Live2D.Framework.src.Effect-Debug-3fff98a7b7efbbea3e50.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Effect"}, {"build": "Live2D/Framework/src/Id", "jsonFile": "directory-Live2D.Framework.src.Id-Debug-386b238e278a71e8f29b.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Id"}, {"build": "Live2D/Framework/src/Math", "jsonFile": "directory-Live2D.Framework.src.Math-Debug-ed44936df1a3cfcd5b84.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Math"}, {"build": "Live2D/Framework/src/Model", "jsonFile": "directory-Live2D.Framework.src.Model-Debug-97758f1afa30136550d3.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Model"}, {"build": "Live2D/Framework/src/Motion", "jsonFile": "directory-Live2D.Framework.src.Motion-Debug-d3a850083bc710d019f7.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Motion"}, {"build": "Live2D/Framework/src/Physics", "jsonFile": "directory-Live2D.Framework.src.Physics-Debug-5bf3db006b6fce9d2960.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Physics"}, {"build": "Live2D/Framework/src/Rendering", "childIndexes": [11], "jsonFile": "directory-Live2D.Framework.src.Rendering-Debug-55928ab1bf35d8d87d9d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering"}, {"build": "Live2D/Framework/src/Rendering/OpenGL", "jsonFile": "directory-Live2D.Framework.src.Rendering.OpenGL-Debug-483a8409254ba4acdff2.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 10, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering/OpenGL"}, {"build": "Live2D/Framework/src/Type", "jsonFile": "directory-Live2D.Framework.src.Type-Debug-1e3e880e7a4c3b8a4e4d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Type"}, {"build": "Live2D/Framework/src/Utils", "jsonFile": "directory-Live2D.Framework.src.Utils-Debug-ed5bd1e3508dd0adf8ee.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Utils"}, {"build": "Live2D/Main", "childIndexes": [15], "jsonFile": "directory-Live2D.Main-Debug-6971672f90945cb8d8b1.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Main"}, {"build": "Live2D/Main/src", "jsonFile": "directory-Live2D.Main.src-Debug-2edf22087e07b919db96.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 14, "projectIndex": 0, "source": "Live2D/Main/src", "targetIndexes": [3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "name": "LAppModelWrapper", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-a62891d38b9db75a5108.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 2, "id": "Framework::@6c5cde19a88560770850", "jsonFile": "target-Framework-Debug-5617b247e60abf481dc4.json", "name": "Framework", "projectIndex": 0}, {"directoryIndex": 0, "id": "Live2DWrapper::@6890427a1f51a3e7e1df", "jsonFile": "target-Live2DWrapper-Debug-490031d3852a6a3baa79.json", "name": "Live2DWrapper", "projectIndex": 0}, {"directoryIndex": 15, "id": "Main::@2c116edd35e6c53877a5", "jsonFile": "target-Main-Debug-b3aca355062e8b8f3586.json", "name": "Main", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-1b738d32d9ad7cedb653.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "glad::@fceb1f27c7c18f638612", "jsonFile": "target-glad-Debug-d4869caa19c5e1e3c83f.json", "name": "glad", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 14], "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.26"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 4]}, {"build": "Live2D/Glad", "jsonFile": "directory-Live2D.Glad-Release-d6ad082406a77fe60c11.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Glad", "targetIndexes": [5]}, {"build": "Live2D/Framework", "childIndexes": [3], "jsonFile": "directory-Live2D.Framework-Release-9f3cf7b94491e22f649e.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Framework", "targetIndexes": [1]}, {"build": "Live2D/Framework/src", "childIndexes": [4, 5, 6, 7, 8, 9, 10, 12, 13], "jsonFile": "directory-Live2D.Framework.src-Release-72c5862d49e493d7113f.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 0, "source": "Live2D/Framework/src"}, {"build": "Live2D/Framework/src/Effect", "jsonFile": "directory-Live2D.Framework.src.Effect-Release-3fff98a7b7efbbea3e50.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Effect"}, {"build": "Live2D/Framework/src/Id", "jsonFile": "directory-Live2D.Framework.src.Id-Release-386b238e278a71e8f29b.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Id"}, {"build": "Live2D/Framework/src/Math", "jsonFile": "directory-Live2D.Framework.src.Math-Release-ed44936df1a3cfcd5b84.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Math"}, {"build": "Live2D/Framework/src/Model", "jsonFile": "directory-Live2D.Framework.src.Model-Release-97758f1afa30136550d3.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Model"}, {"build": "Live2D/Framework/src/Motion", "jsonFile": "directory-Live2D.Framework.src.Motion-Release-d3a850083bc710d019f7.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Motion"}, {"build": "Live2D/Framework/src/Physics", "jsonFile": "directory-Live2D.Framework.src.Physics-Release-5bf3db006b6fce9d2960.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Physics"}, {"build": "Live2D/Framework/src/Rendering", "childIndexes": [11], "jsonFile": "directory-Live2D.Framework.src.Rendering-Release-55928ab1bf35d8d87d9d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering"}, {"build": "Live2D/Framework/src/Rendering/OpenGL", "jsonFile": "directory-Live2D.Framework.src.Rendering.OpenGL-Release-483a8409254ba4acdff2.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 10, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering/OpenGL"}, {"build": "Live2D/Framework/src/Type", "jsonFile": "directory-Live2D.Framework.src.Type-Release-1e3e880e7a4c3b8a4e4d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Type"}, {"build": "Live2D/Framework/src/Utils", "jsonFile": "directory-Live2D.Framework.src.Utils-Release-ed5bd1e3508dd0adf8ee.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Utils"}, {"build": "Live2D/Main", "childIndexes": [15], "jsonFile": "directory-Live2D.Main-Release-6971672f90945cb8d8b1.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Main"}, {"build": "Live2D/Main/src", "jsonFile": "directory-Live2D.Main.src-Release-2edf22087e07b919db96.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 14, "projectIndex": 0, "source": "Live2D/Main/src", "targetIndexes": [3]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "name": "LAppModelWrapper", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-a62891d38b9db75a5108.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 2, "id": "Framework::@6c5cde19a88560770850", "jsonFile": "target-Framework-Release-0676cdb90f8f8727fb32.json", "name": "Framework", "projectIndex": 0}, {"directoryIndex": 0, "id": "Live2DWrapper::@6890427a1f51a3e7e1df", "jsonFile": "target-Live2DWrapper-Release-f4923f2e85240fb43048.json", "name": "Live2DWrapper", "projectIndex": 0}, {"directoryIndex": 15, "id": "Main::@2c116edd35e6c53877a5", "jsonFile": "target-Main-Release-e3fc87fd5edf8f26c97f.json", "name": "Main", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-1b738d32d9ad7cedb653.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "glad::@fceb1f27c7c18f638612", "jsonFile": "target-glad-Release-5927ae27641ce3877dea.json", "name": "glad", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 14], "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.26"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 4]}, {"build": "Live2D/Glad", "jsonFile": "directory-Live2D.Glad-MinSizeRel-d6ad082406a77fe60c11.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Glad", "targetIndexes": [5]}, {"build": "Live2D/Framework", "childIndexes": [3], "jsonFile": "directory-Live2D.Framework-MinSizeRel-9f3cf7b94491e22f649e.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Framework", "targetIndexes": [1]}, {"build": "Live2D/Framework/src", "childIndexes": [4, 5, 6, 7, 8, 9, 10, 12, 13], "jsonFile": "directory-Live2D.Framework.src-MinSizeRel-72c5862d49e493d7113f.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 0, "source": "Live2D/Framework/src"}, {"build": "Live2D/Framework/src/Effect", "jsonFile": "directory-Live2D.Framework.src.Effect-MinSizeRel-3fff98a7b7efbbea3e50.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Effect"}, {"build": "Live2D/Framework/src/Id", "jsonFile": "directory-Live2D.Framework.src.Id-MinSizeRel-386b238e278a71e8f29b.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Id"}, {"build": "Live2D/Framework/src/Math", "jsonFile": "directory-Live2D.Framework.src.Math-MinSizeRel-ed44936df1a3cfcd5b84.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Math"}, {"build": "Live2D/Framework/src/Model", "jsonFile": "directory-Live2D.Framework.src.Model-MinSizeRel-97758f1afa30136550d3.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Model"}, {"build": "Live2D/Framework/src/Motion", "jsonFile": "directory-Live2D.Framework.src.Motion-MinSizeRel-d3a850083bc710d019f7.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Motion"}, {"build": "Live2D/Framework/src/Physics", "jsonFile": "directory-Live2D.Framework.src.Physics-MinSizeRel-5bf3db006b6fce9d2960.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Physics"}, {"build": "Live2D/Framework/src/Rendering", "childIndexes": [11], "jsonFile": "directory-Live2D.Framework.src.Rendering-MinSizeRel-55928ab1bf35d8d87d9d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering"}, {"build": "Live2D/Framework/src/Rendering/OpenGL", "jsonFile": "directory-Live2D.Framework.src.Rendering.OpenGL-MinSizeRel-483a8409254ba4acdff2.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 10, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering/OpenGL"}, {"build": "Live2D/Framework/src/Type", "jsonFile": "directory-Live2D.Framework.src.Type-MinSizeRel-1e3e880e7a4c3b8a4e4d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Type"}, {"build": "Live2D/Framework/src/Utils", "jsonFile": "directory-Live2D.Framework.src.Utils-MinSizeRel-ed5bd1e3508dd0adf8ee.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Utils"}, {"build": "Live2D/Main", "childIndexes": [15], "jsonFile": "directory-Live2D.Main-MinSizeRel-6971672f90945cb8d8b1.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Main"}, {"build": "Live2D/Main/src", "jsonFile": "directory-Live2D.Main.src-MinSizeRel-2edf22087e07b919db96.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 14, "projectIndex": 0, "source": "Live2D/Main/src", "targetIndexes": [3]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "name": "LAppModelWrapper", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-a62891d38b9db75a5108.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 2, "id": "Framework::@6c5cde19a88560770850", "jsonFile": "target-Framework-MinSizeRel-691be6230d53717e9c8c.json", "name": "Framework", "projectIndex": 0}, {"directoryIndex": 0, "id": "Live2DWrapper::@6890427a1f51a3e7e1df", "jsonFile": "target-Live2DWrapper-MinSizeRel-411c3506c32cff3c7224.json", "name": "Live2DWrapper", "projectIndex": 0}, {"directoryIndex": 15, "id": "Main::@2c116edd35e6c53877a5", "jsonFile": "target-Main-MinSizeRel-36b4bc8701394441033f.json", "name": "Main", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-1b738d32d9ad7cedb653.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "glad::@fceb1f27c7c18f638612", "jsonFile": "target-glad-MinSizeRel-202888e2bbac37d7e17d.json", "name": "glad", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1, 2, 14], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.26"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 4]}, {"build": "Live2D/Glad", "jsonFile": "directory-Live2D.Glad-RelWithDebInfo-d6ad082406a77fe60c11.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Glad", "targetIndexes": [5]}, {"build": "Live2D/Framework", "childIndexes": [3], "jsonFile": "directory-Live2D.Framework-RelWithDebInfo-9f3cf7b94491e22f649e.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Framework", "targetIndexes": [1]}, {"build": "Live2D/Framework/src", "childIndexes": [4, 5, 6, 7, 8, 9, 10, 12, 13], "jsonFile": "directory-Live2D.Framework.src-RelWithDebInfo-72c5862d49e493d7113f.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 2, "projectIndex": 0, "source": "Live2D/Framework/src"}, {"build": "Live2D/Framework/src/Effect", "jsonFile": "directory-Live2D.Framework.src.Effect-RelWithDebInfo-3fff98a7b7efbbea3e50.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Effect"}, {"build": "Live2D/Framework/src/Id", "jsonFile": "directory-Live2D.Framework.src.Id-RelWithDebInfo-386b238e278a71e8f29b.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Id"}, {"build": "Live2D/Framework/src/Math", "jsonFile": "directory-Live2D.Framework.src.Math-RelWithDebInfo-ed44936df1a3cfcd5b84.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Math"}, {"build": "Live2D/Framework/src/Model", "jsonFile": "directory-Live2D.Framework.src.Model-RelWithDebInfo-97758f1afa30136550d3.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Model"}, {"build": "Live2D/Framework/src/Motion", "jsonFile": "directory-Live2D.Framework.src.Motion-RelWithDebInfo-d3a850083bc710d019f7.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Motion"}, {"build": "Live2D/Framework/src/Physics", "jsonFile": "directory-Live2D.Framework.src.Physics-RelWithDebInfo-5bf3db006b6fce9d2960.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Physics"}, {"build": "Live2D/Framework/src/Rendering", "childIndexes": [11], "jsonFile": "directory-Live2D.Framework.src.Rendering-RelWithDebInfo-55928ab1bf35d8d87d9d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering"}, {"build": "Live2D/Framework/src/Rendering/OpenGL", "jsonFile": "directory-Live2D.Framework.src.Rendering.OpenGL-RelWithDebInfo-483a8409254ba4acdff2.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 10, "projectIndex": 0, "source": "Live2D/Framework/src/Rendering/OpenGL"}, {"build": "Live2D/Framework/src/Type", "jsonFile": "directory-Live2D.Framework.src.Type-RelWithDebInfo-1e3e880e7a4c3b8a4e4d.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Type"}, {"build": "Live2D/Framework/src/Utils", "jsonFile": "directory-Live2D.Framework.src.Utils-RelWithDebInfo-ed5bd1e3508dd0adf8ee.json", "minimumCMakeVersion": {"string": "3.10"}, "parentIndex": 3, "projectIndex": 0, "source": "Live2D/Framework/src/Utils"}, {"build": "Live2D/Main", "childIndexes": [15], "jsonFile": "directory-Live2D.Main-RelWithDebInfo-6971672f90945cb8d8b1.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 0, "projectIndex": 0, "source": "Live2D/Main"}, {"build": "Live2D/Main/src", "jsonFile": "directory-Live2D.Main.src-RelWithDebInfo-2edf22087e07b919db96.json", "minimumCMakeVersion": {"string": "3.26"}, "parentIndex": 14, "projectIndex": 0, "source": "Live2D/Main/src", "targetIndexes": [3]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15], "name": "LAppModelWrapper", "targetIndexes": [0, 1, 2, 3, 4, 5]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-a62891d38b9db75a5108.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 2, "id": "Framework::@6c5cde19a88560770850", "jsonFile": "target-Framework-RelWithDebInfo-2fe3060aff43db8a8b65.json", "name": "Framework", "projectIndex": 0}, {"directoryIndex": 0, "id": "Live2DWrapper::@6890427a1f51a3e7e1df", "jsonFile": "target-Live2DWrapper-RelWithDebInfo-74a57cf293a156b10e56.json", "name": "Live2DWrapper", "projectIndex": 0}, {"directoryIndex": 15, "id": "Main::@2c116edd35e6c53877a5", "jsonFile": "target-Main-RelWithDebInfo-22223b2fe6ffcfc2d042.json", "name": "Main", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-1b738d32d9ad7cedb653.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 1, "id": "glad::@fceb1f27c7c18f638612", "jsonFile": "target-glad-RelWithDebInfo-f76e588b180906fc7138.json", "name": "glad", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build", "source": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py"}, "version": {"major": 2, "minor": 8}}
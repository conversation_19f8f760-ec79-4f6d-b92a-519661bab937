{"archive": {"commandFragments": [{"fragment": "/machine:x64", "role": "flags"}]}, "artifacts": [{"path": "Live2D/Main/src/RelWithDebInfo/Main.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include", "add_compile_options", "target_include_directories", "target_sources"], "files": ["Live2D/Main/src/CMakeLists.txt", "Live2D/cmake/Main.cmake", "Live2D/cmake/Live2D.cmake", "CMakeLists.txt", "Live2D/Main/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 1, "parent": 0}, {"file": 3}, {"command": 2, "file": 3, "line": 29, "parent": 2}, {"file": 2, "parent": 3}, {"command": 2, "file": 2, "line": 12, "parent": 4}, {"file": 1, "parent": 5}, {"command": 1, "file": 1, "line": 27, "parent": 6}, {"command": 3, "file": 3, "line": 17, "parent": 2}, {"command": 4, "file": 1, "line": 7, "parent": 6}, {"file": 4}, {"command": 5, "file": 4, "line": 5, "parent": 10}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob1 /DNDEBUG -std:c++17 -<PERSON> -<PERSON>i"}, {"backtrace": 8, "fragment": "/utf-8"}, {"backtrace": 8, "fragment": "/wd4018"}, {"backtrace": 8, "fragment": "/wd4244"}, {"backtrace": 8, "fragment": "/wd4996"}], "defines": [{"backtrace": 7, "define": "CSM_TARGET_WIN_GL"}], "includes": [{"backtrace": 9, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Main/src"}, {"backtrace": 7, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Framework/src"}, {"backtrace": 7, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Glad/include"}, {"backtrace": 7, "isSystem": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Live2D/Core/include"}], "language": "CXX", "languageStandard": {"backtraces": [0], "standard": "17"}, "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 15]}], "dependencies": [{"backtrace": 7, "id": "glad::@fceb1f27c7c18f638612"}, {"backtrace": 7, "id": "Framework::@6c5cde19a88560770850"}, {"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "Main::@2c116edd35e6c53877a5", "name": "Main", "nameOnDisk": "Main.lib", "paths": {"build": "Live2D/Main/src", "source": "Live2D/Main/src"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 4, 6, 8, 10, 12, 15]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 5, 7, 9, 11, 13, 14]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Main/src/LAppAllocator.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Live2D/Main/src/LAppAllocator.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Main/src/LAppDefine.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Live2D/Main/src/LAppDefine.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Main/src/LAppPal.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Live2D/Main/src/LAppPal.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Main/src/LAppTextureManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Live2D/Main/src/LAppTextureManager.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Main/src/LAppModel.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Live2D/Main/src/LAppModel.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Main/src/Log.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Live2D/Main/src/Log.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Live2D/Main/src/MatrixManager.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Live2D/Main/src/MatrixManager.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Live2D/Main/src/HackProperties.h", "sourceGroupIndex": 1}, {"backtrace": 11, "compileGroupIndex": 0, "path": "Live2D/Main/src/fine-grained/Model.cpp", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}
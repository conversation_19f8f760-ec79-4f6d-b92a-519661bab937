name: Linux 📦

on:
  push:
    tags:
      - "v*.*.*"
    branches: [ linux-workflow, build-all ]


jobs:
  build-n-publish:
    name: Build📦 for Linux
    runs-on: ubuntu-24.04

    strategy:
      matrix:
        python-version: [ '3.8', '3.9', '3.10', '3.11', '3.12', '3.13' ]
        architecture: [ 'x64' ]
      fail-fast: false
    
    steps:
      - uses: actions/checkout@v4

      - name: Install OpenGL dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y \
            libgl1-mesa-dev \
            libglu1-mesa-dev \
            mesa-common-dev 

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          architecture: ${{ matrix.architecture }}

      - name: Install build deps
        run: |
          python -m pip install --upgrade setuptools 
          python -m pip install wheel
      
      - name: Build a source tarball
        if: ${{ matrix.python-version == '3.13'}}
        run: python setup.py sdist

      - name: Build a binary wheel
        run: |
          python setup.py bdist_wheel

      - name: Upload Wheel to Release
        uses: softprops/action-gh-release@v2
        with:
          # tag_name: v0.3.6
          files: |
            dist/*.whl
            dist/*.tar.gz

      - name: Install twine
        run: python -m pip install twine
      
      # - name: Fix wheel
      #   run: python -m auditwheel repair dist/*.whl --plat manylinux2014_x86_64 -w fix-dist/

      - name: Publish distribution to PyPI
        if: ${{ matrix.python-version == '3.13' }}
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: twine upload dist/*.tar.gz --skip-existing


    
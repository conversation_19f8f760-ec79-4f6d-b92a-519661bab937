#!/usr/bin/env python3
"""
Live2D桌面宠物对话系统模块

这个模块提供了完整的对话功能，包括：
- LLM客户端和预设管理
- 对话界面和响应处理
- 文本显示和快速输入组件
- TTS和STT功能接口（为未来扩展预留）

使用示例：
    from dialogue_system import LLMClient, ChatDialog, TextDisplayManager

    # 创建LLM客户端
    llm_client = LLMClient(config_manager)

    # 创建对话界面
    chat_dialog = ChatDialog(config_manager)

    # 创建文本显示管理器
    text_manager = TextDisplayManager(parent_widget)

高级使用示例：
    from dialogue_system import DialogueSystemManager

    # 创建对话系统管理器（一站式解决方案）
    dialogue_manager = DialogueSystemManager(config_manager, parent_widget)

    # 显示对话界面
    dialogue_manager.show_chat_dialog()

    # 显示快速输入
    dialogue_manager.show_quick_input()

    # 处理消息
    dialogue_manager.handle_message("Hello")
"""

# 导入核心组件
from .core.llm_client import LLMClient
from .core.preset_manager import PresetManager
from .core.chat_dialog import ChatDialog, ChatWorker
from .core.response_handler import ChatResponseHandler

# 导入UI组件
from .ui.text_overlay import TextOverlay, TextDisplayManager
from .ui.quick_input import QuickInputOverlay

__version__ = "1.0.0"
__author__ = "Live2D Desktop Pet Team"


class DialogueSystemManager:
    """对话系统管理器 - 提供一站式对话功能管理"""

    def __init__(self, config_manager=None, parent_widget=None):
        """初始化对话系统管理器"""
        self.config_manager = config_manager
        self.parent_widget = parent_widget

        # 初始化核心组件
        self.llm_client = LLMClient(config_manager)
        self.preset_manager = PresetManager(config_manager)
        self.response_handler = ChatResponseHandler()

        # 初始化UI组件
        if parent_widget:
            self.text_display_manager = TextDisplayManager(parent_widget, self.llm_client)
            self.chat_dialog = None  # 延迟初始化

            # 连接信号
            self._connect_signals()

    def _connect_signals(self):
        """连接信号"""
        if hasattr(self, 'text_display_manager'):
            self.text_display_manager.connect_signals(
                message_handler=self.handle_message,
                preset_handler=self.handle_preset_change
            )

    def show_chat_dialog(self):
        """显示对话界面"""
        if not self.chat_dialog:
            self.chat_dialog = ChatDialog(self.config_manager, self.parent_widget)
        self.chat_dialog.show()
        return self.chat_dialog

    def show_quick_input(self):
        """显示快速输入"""
        if hasattr(self, 'text_display_manager'):
            self.text_display_manager.show_quick_input()

    def show_message(self, message: str):
        """显示消息"""
        if hasattr(self, 'text_display_manager'):
            self.text_display_manager.show_message(message)

    def handle_message(self, message: str):
        """处理消息"""
        if self.llm_client.is_configured():
            try:
                response = self.llm_client.chat(message)
                if response and not response.startswith("❌"):
                    self.show_message(response)
                    return response
                else:
                    error_msg = f"❌ 处理失败: {response}"
                    self.show_message(error_msg)
                    return error_msg
            except Exception as e:
                error_msg = f"❌ 处理异常: {e}"
                self.show_message(error_msg)
                return error_msg
        else:
            error_msg = "❌ LLM客户端未配置"
            self.show_message(error_msg)
            return error_msg

    def handle_preset_change(self, preset_id: str):
        """处理预设切换"""
        self.preset_manager.set_current_preset(preset_id)
        preset = self.preset_manager.get_current_preset()

        # 更新LLM客户端配置
        if preset:
            config_update = {}
            if "system_prompt" in preset:
                config_update["conversation_settings"] = {"system_prompt": preset["system_prompt"]}
            if "api_config" in preset:
                config_update["api_config"] = preset["api_config"]

            if config_update:
                self.llm_client.update_config(config_update)

        # 显示切换消息
        preset_name = preset.get("name", preset_id) if preset else preset_id
        self.show_message(f"预设已切换到: {preset_name}")

    def is_configured(self) -> bool:
        """检查是否已配置"""
        return self.llm_client.is_configured()

    def get_current_preset(self):
        """获取当前预设"""
        return self.preset_manager.get_current_preset()

    def regenerate_last_response(self):
        """重新生成最后一条回复"""
        response = self.llm_client.regenerate_last_response()
        if response and not response.startswith("❌"):
            self.show_message(response)
        return response

    def continue_conversation(self):
        """继续对话"""
        response = self.llm_client.continue_conversation()
        if response and not response.startswith("❌"):
            self.show_message(response)
        return response

    def start_new_chat(self, title: str = None):
        """开始新聊天"""
        self.llm_client.start_new_chat(title)
        self.show_message("🆕 已开始新的聊天会话")

    def get_chat_sessions(self):
        """获取所有聊天会话"""
        return self.llm_client.get_chat_sessions()

    def load_chat_session(self, session_id: str):
        """加载聊天会话"""
        success = self.llm_client.load_chat_session(session_id)
        if success:
            session_info = self.llm_client.get_current_session_info()
            if session_info:
                self.show_message(f"📂 已加载会话: {session_info['title']}")
        return success

    def delete_chat_session(self, session_id: str):
        """删除聊天会话"""
        return self.llm_client.delete_chat_session(session_id)

    def get_current_session_info(self):
        """获取当前会话信息"""
        return self.llm_client.get_current_session_info()

    def enable_context_memory(self, enabled: bool = True):
        """启用/禁用上下文记忆"""
        if hasattr(self, 'text_display_manager'):
            self.text_display_manager.enable_context_memory(enabled)

    def is_context_memory_enabled(self) -> bool:
        """检查是否启用了上下文记忆"""
        if hasattr(self, 'text_display_manager'):
            return self.text_display_manager.is_context_memory_enabled()
        return True


# 公开的API
__all__ = [
    # 核心组件
    "LLMClient",
    "PresetManager",
    "ChatDialog",
    "ChatWorker",
    "ChatResponseHandler",

    # UI组件
    "TextOverlay",
    "TextDisplayManager",
    "QuickInputOverlay",

    # 管理器
    "DialogueSystemManager",
]

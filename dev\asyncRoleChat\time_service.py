import datetime
import time

class TimeService:
    def get_current_time(self, format_type="detailed"):
        now = datetime.datetime.now()
        
        if format_type == "simple":
            return {
                "time": now.strftime("%H:%M"),
                "date": now.strftime("%Y-%m-%d")
            }
        elif format_type == "detailed":
            return {
                "datetime": now.strftime("%Y年%m月%d日 %H:%M:%S"),
                "weekday": now.strftime("%A"),
                "timestamp": int(time.time())
            }
        else:
            return {
                "datetime": now.strftime("%Y-%m-%d %H:%M:%S"),
                "timestamp": int(time.time())
            }

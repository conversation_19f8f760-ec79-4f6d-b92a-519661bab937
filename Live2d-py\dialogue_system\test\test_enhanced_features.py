#!/usr/bin/env python3
"""
测试对话系统升级功能
验证重新生成、继续对话、新聊天、历史聊天、上下文记忆等功能
"""

import sys
import os
from PySide6.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, 
                              QWidget, QLabel, QHBoxLayout, QTextEdit, QGroupBox)
from PySide6.QtCore import Qt

# 添加正确的路径
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'dev'))

def test_console_features():
    """控制台测试新功能"""
    print("🚀 开始对话系统升级功能测试...")
    print("=" * 60)
    
    try:
        # 测试导入
        from settings_dialog import ConfigManager
        from dialogue_system import DialogueSystemManager, LLMClient
        from dialogue_system.core.chat_history_manager import ChatHistoryManager
        print("✅ 模块导入成功")
        
        # 创建配置管理器
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 测试历史管理器
        history_manager = ChatHistoryManager(config_manager)
        print("✅ 历史管理器创建成功")
        
        # 测试会话创建
        session = history_manager.create_new_session("测试会话")
        print(f"✅ 创建会话成功: {session.session_id}")
        
        # 测试消息添加
        history_manager.add_message_to_current("user", "你好")
        history_manager.add_message_to_current("assistant", "你好！我是AI助手。")
        print("✅ 消息添加成功")
        
        # 测试会话保存和加载
        sessions = history_manager.get_all_sessions()
        print(f"✅ 获取会话列表成功，共 {len(sessions)} 个会话")
        
        # 测试LLM客户端新功能
        llm_client = LLMClient(config_manager)
        print("✅ LLM客户端创建成功")
        
        # 测试对话系统管理器
        dialogue_manager = DialogueSystemManager(config_manager)
        print("✅ 对话系统管理器创建成功")
        
        print("\n📊 控制台测试结果: 全部通过 ✅")
        return True
        
    except Exception as e:
        print(f"❌ 控制台测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


class EnhancedFeaturesTestWindow(QMainWindow):
    """升级功能测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_dialogue_system()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("对话系统升级功能测试")
        self.setGeometry(100, 100, 800, 700)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态显示
        self.status_label = QLabel("状态: 初始化完成")
        self.status_label.setStyleSheet("font-weight: bold; color: #007acc;")
        layout.addWidget(self.status_label)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 功能测试区域
        self.create_test_groups(layout)
    
    def create_test_groups(self, parent_layout):
        """创建测试功能组"""
        
        # 基础功能测试
        basic_group = QGroupBox("基础功能测试")
        basic_layout = QHBoxLayout(basic_group)
        
        self.quick_input_btn = QPushButton("测试快速输入")
        self.quick_input_btn.clicked.connect(self.test_quick_input)
        basic_layout.addWidget(self.quick_input_btn)
        
        self.message_display_btn = QPushButton("测试消息显示")
        self.message_display_btn.clicked.connect(self.test_message_display)
        basic_layout.addWidget(self.message_display_btn)
        
        self.context_memory_btn = QPushButton("测试上下文记忆")
        self.context_memory_btn.clicked.connect(self.test_context_memory)
        basic_layout.addWidget(self.context_memory_btn)
        
        parent_layout.addWidget(basic_group)
        
        # 对话功能测试
        chat_group = QGroupBox("对话功能测试")
        chat_layout = QHBoxLayout(chat_group)
        
        self.regenerate_btn = QPushButton("测试重新生成")
        self.regenerate_btn.clicked.connect(self.test_regenerate)
        chat_layout.addWidget(self.regenerate_btn)
        
        self.continue_btn = QPushButton("测试继续对话")
        self.continue_btn.clicked.connect(self.test_continue)
        chat_layout.addWidget(self.continue_btn)
        
        self.new_chat_btn = QPushButton("测试新聊天")
        self.new_chat_btn.clicked.connect(self.test_new_chat)
        chat_layout.addWidget(self.new_chat_btn)
        
        parent_layout.addWidget(chat_group)
        
        # 历史功能测试
        history_group = QGroupBox("历史功能测试")
        history_layout = QHBoxLayout(history_group)
        
        self.chat_dialog_btn = QPushButton("打开对话界面")
        self.chat_dialog_btn.clicked.connect(self.test_chat_dialog)
        history_layout.addWidget(self.chat_dialog_btn)
        
        self.session_info_btn = QPushButton("查看会话信息")
        self.session_info_btn.clicked.connect(self.test_session_info)
        history_layout.addWidget(self.session_info_btn)
        
        self.history_list_btn = QPushButton("查看历史列表")
        self.history_list_btn.clicked.connect(self.test_history_list)
        history_layout.addWidget(self.history_list_btn)
        
        parent_layout.addWidget(history_group)
        
        # 综合测试
        comprehensive_group = QGroupBox("综合测试")
        comprehensive_layout = QHBoxLayout(comprehensive_group)
        
        self.full_test_btn = QPushButton("完整功能测试")
        self.full_test_btn.clicked.connect(self.test_full_features)
        comprehensive_layout.addWidget(self.full_test_btn)
        
        self.clear_log_btn = QPushButton("清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        comprehensive_layout.addWidget(self.clear_log_btn)
        
        parent_layout.addWidget(comprehensive_group)
    
    def init_dialogue_system(self):
        """初始化对话系统"""
        try:
            from settings_dialog import ConfigManager
            from dialogue_system import DialogueSystemManager
            
            self.config_manager = ConfigManager()
            self.dialogue_manager = DialogueSystemManager(self.config_manager, self)
            
            self.log("✅ 对话系统初始化成功")
            self.status_label.setText("状态: 对话系统就绪")
            
        except Exception as e:
            self.log(f"❌ 对话系统初始化失败: {e}")
            self.status_label.setText(f"状态: 初始化失败 - {e}")
    
    def log(self, message: str):
        """添加日志"""
        self.log_text.append(message)
        print(message)  # 同时输出到控制台
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def test_quick_input(self):
        """测试快速输入"""
        try:
            self.dialogue_manager.show_quick_input()
            self.log("✅ 快速输入显示成功")
            self.status_label.setText("状态: 快速输入已显示")
        except Exception as e:
            self.log(f"❌ 快速输入测试失败: {e}")
    
    def test_message_display(self):
        """测试消息显示"""
        try:
            test_message = "这是一个测试消息，验证升级后的文本显示功能是否正常工作。"
            self.dialogue_manager.show_message(test_message)
            self.log("✅ 消息显示成功")
            self.status_label.setText("状态: 消息显示成功")
        except Exception as e:
            self.log(f"❌ 消息显示测试失败: {e}")
    
    def test_context_memory(self):
        """测试上下文记忆"""
        try:
            is_enabled = self.dialogue_manager.is_context_memory_enabled()
            self.log(f"✅ 上下文记忆状态: {'启用' if is_enabled else '禁用'}")
            
            # 切换状态测试
            self.dialogue_manager.enable_context_memory(not is_enabled)
            new_state = self.dialogue_manager.is_context_memory_enabled()
            self.log(f"✅ 上下文记忆切换成功: {'启用' if new_state else '禁用'}")
            
            # 恢复原状态
            self.dialogue_manager.enable_context_memory(is_enabled)
            self.status_label.setText("状态: 上下文记忆测试完成")
        except Exception as e:
            self.log(f"❌ 上下文记忆测试失败: {e}")
    
    def test_regenerate(self):
        """测试重新生成"""
        try:
            response = self.dialogue_manager.regenerate_last_response()
            if response:
                self.log("✅ 重新生成功能测试成功")
            else:
                self.log("⚠️ 重新生成功能需要先有对话历史")
            self.status_label.setText("状态: 重新生成测试完成")
        except Exception as e:
            self.log(f"❌ 重新生成测试失败: {e}")
    
    def test_continue(self):
        """测试继续对话"""
        try:
            response = self.dialogue_manager.continue_conversation()
            if response:
                self.log("✅ 继续对话功能测试成功")
            else:
                self.log("⚠️ 继续对话功能需要LLM配置")
            self.status_label.setText("状态: 继续对话测试完成")
        except Exception as e:
            self.log(f"❌ 继续对话测试失败: {e}")
    
    def test_new_chat(self):
        """测试新聊天"""
        try:
            self.dialogue_manager.start_new_chat("测试新聊天")
            self.log("✅ 新聊天功能测试成功")
            self.status_label.setText("状态: 新聊天测试完成")
        except Exception as e:
            self.log(f"❌ 新聊天测试失败: {e}")
    
    def test_chat_dialog(self):
        """测试对话界面"""
        try:
            chat_dialog = self.dialogue_manager.show_chat_dialog()
            self.log("✅ 对话界面显示成功")
            self.status_label.setText("状态: 对话界面已打开")
        except Exception as e:
            self.log(f"❌ 对话界面测试失败: {e}")
    
    def test_session_info(self):
        """测试会话信息"""
        try:
            session_info = self.dialogue_manager.get_current_session_info()
            if session_info:
                self.log(f"✅ 当前会话: {session_info['title']}")
                self.log(f"   消息数量: {session_info['message_count']}")
            else:
                self.log("⚠️ 当前没有活动会话")
            self.status_label.setText("状态: 会话信息查看完成")
        except Exception as e:
            self.log(f"❌ 会话信息测试失败: {e}")
    
    def test_history_list(self):
        """测试历史列表"""
        try:
            sessions = self.dialogue_manager.get_chat_sessions()
            self.log(f"✅ 历史会话数量: {len(sessions)}")
            for i, session in enumerate(sessions[:3]):  # 只显示前3个
                self.log(f"   {i+1}. {session.title} ({len(session.messages)}条消息)")
            self.status_label.setText("状态: 历史列表查看完成")
        except Exception as e:
            self.log(f"❌ 历史列表测试失败: {e}")
    
    def test_full_features(self):
        """完整功能测试"""
        self.log("🚀 开始完整功能测试...")
        
        # 依次测试所有功能
        test_functions = [
            ("快速输入", self.test_quick_input),
            ("消息显示", self.test_message_display),
            ("上下文记忆", self.test_context_memory),
            ("重新生成", self.test_regenerate),
            ("继续对话", self.test_continue),
            ("新聊天", self.test_new_chat),
            ("会话信息", self.test_session_info),
            ("历史列表", self.test_history_list),
        ]
        
        success_count = 0
        for name, func in test_functions:
            try:
                func()
                success_count += 1
            except Exception as e:
                self.log(f"❌ {name}测试失败: {e}")
        
        self.log(f"🎉 完整功能测试完成: {success_count}/{len(test_functions)} 项通过")
        self.status_label.setText(f"状态: 完整测试完成 ({success_count}/{len(test_functions)})")


def main():
    """主函数"""
    # 先运行控制台测试
    console_success = test_console_features()
    
    if console_success:
        print("\n🎉 控制台测试全部通过！")
        print("现在启动GUI测试...")
        
        # 启动GUI测试
        app = QApplication(sys.argv)
        
        window = EnhancedFeaturesTestWindow()
        window.show()
        
        print("\n📋 GUI测试说明:")
        print("- 基础功能: 快速输入、消息显示、上下文记忆")
        print("- 对话功能: 重新生成、继续对话、新聊天")
        print("- 历史功能: 对话界面、会话信息、历史列表")
        print("- 综合测试: 完整功能测试")
        
        sys.exit(app.exec())
    else:
        print("\n❌ 控制台测试失败，请检查模块配置")
        return False


if __name__ == "__main__":
    main()

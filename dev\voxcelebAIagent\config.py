import os
from pathlib import Path

class Config:
    # LLM配置
    LLM_BASE_URL = "https://api.studio.nebius.ai/v1"
    LLM_MODEL = "deepseek-ai/DeepSeek-V3-0324-fast"
    
    # 音频配置
    SAMPLE_RATE = 16000
    AUDIO_DURATION = 3  # 录音时长(秒)
    CHUNK_SIZE = 1024
    
    # 说话人识别配置
    SIMILARITY_THRESHOLD = 0.8  # 身份匹配阈值
    EMBEDDING_DIM = 512  # 特征向量维度
    
    # 存储配置
    DATA_DIR = Path(__file__).parent / "data"
    SPEAKERS_DB = DATA_DIR / "speakers.json"
    AUDIO_DIR = DATA_DIR / "audio_samples"
    
    @classmethod
    def get_llm_api_key(cls):
        api_file = Path(__file__).parent.parent / "api.txt"
        with open(api_file, 'r', encoding='utf-8') as f:
            return f.read().strip()
    
    @classmethod
    def ensure_dirs(cls):
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.AUDIO_DIR.mkdir(exist_ok=True)

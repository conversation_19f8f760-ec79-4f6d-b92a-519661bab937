#!/usr/bin/env python3
"""
天气查询MCP服务器
提供天气查询功能
使用简化的JSON-RPC协议实现
"""

import asyncio
import json
import sys
import requests
from typing import Any, Dict

try:
    from config import Config
except ImportError:
    class Config:
        WEATHER_BASE_URL = "https://api.openweathermap.org/data/2.5"
        WEATHER_API_KEY = "d5c020123a565d7659190293f6c159ed"

class WeatherService:
    def __init__(self):
        self.api_key = Config.WEATHER_API_KEY
        self.base_url = Config.WEATHER_BASE_URL
        self.city_mapping = {
            "北京": "Beijing", "上海": "Shanghai", "广州": "Guangzhou", "深圳": "Shenzhen",
            "杭州": "Hangzhou", "南京": "Nanjing", "武汉": "Wuhan", "成都": "Chengdu",
            "重庆": "Chongqing", "天津": "Tianjin", "西安": "Xi'an", "苏州": "Suzhou",
            "长沙": "Changsha", "沈阳": "Shenyang", "青岛": "Qingdao", "郑州": "Zhengzhou"
        }

    def _translate_city_name(self, city):
        return self.city_mapping.get(city, city)
        
    def get_current_weather(self, city):
        english_city = self._translate_city_name(city)
        url = f"{self.base_url}/weather"
        params = {
            "q": english_city,
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn"
        }
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            raise Exception(f"天气查询失败: {str(e)}")

class SimpleJSONRPCServer:
    def __init__(self):
        self.weather_service = WeatherService()
        self.tools = {
            "get_weather": self.get_weather
        }

    async def handle_request(self, request_data):
        try:
            request = json.loads(request_data)
            method = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")

            if method == "list_tools":
                return self.create_response(request_id, self.get_tools_list())
            elif method == "call_tool":
                tool_name = params.get("name")
                arguments = params.get("arguments", {})
                result = await self.call_tool(tool_name, arguments)
                return self.create_response(request_id, result)
            else:
                return self.create_error_response(request_id, f"未知方法: {method}")
        except Exception as e:
            return self.create_error_response(None, f"请求处理错误: {str(e)}")

    def create_response(self, request_id, result):
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "result": result
        })

    def create_error_response(self, request_id, error_message):
        return json.dumps({
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": -1,
                "message": error_message
            }
        })

    def get_tools_list(self):
        return {
            "tools": [
                {
                    "name": "get_weather",
                    "description": "查询指定城市的当前天气",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "city": {"type": "string", "description": "城市名称（支持中文）"}
                        },
                        "required": ["city"]
                    }
                }
            ]
        }

    async def call_tool(self, tool_name, arguments):
        if tool_name not in self.tools:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"未知工具: {tool_name}"}]
            }
        try:
            result = await self.tools[tool_name](arguments)
            return result
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"工具执行错误: {str(e)}"}]
            }

    async def get_weather(self, args: Dict[str, Any]) -> Dict[str, Any]:
        city = args["city"]
        try:
            weather_data = self.weather_service.get_current_weather(city)
            
            # 格式化天气信息
            main = weather_data["main"]
            weather = weather_data["weather"][0]
            wind = weather_data.get("wind", {})
            
            weather_info = f"""🌤️ {city}当前天气:
📍 位置: {weather_data["name"]}
🌡️ 温度: {main["temp"]}°C (体感 {main["feels_like"]}°C)
☁️ 天气: {weather["description"]}
💧 湿度: {main["humidity"]}%
🌬️ 风速: {wind.get("speed", "未知")} m/s
🔽 气压: {main["pressure"]} hPa
👁️ 能见度: {weather_data.get("visibility", "未知")} m"""

            return {
                "success": True,
                "content": [{"type": "text", "text": weather_info}]
            }
        except Exception as e:
            return {
                "success": False,
                "content": [{"type": "text", "text": f"天气查询失败: {str(e)}"}]
            }

async def main():
    server = SimpleJSONRPCServer()
    try:
        while True:
            line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            if not line:
                break
            line = line.strip()
            if not line:
                continue
            response = await server.handle_request(line)
            print(response, flush=True)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        error_response = server.create_error_response(None, f"服务器错误: {str(e)}")
        print(error_response, flush=True)

if __name__ == "__main__":
    asyncio.run(main())

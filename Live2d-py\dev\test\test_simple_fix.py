#!/usr/bin/env python3
"""
简化的快速输入修复测试
避免QApplication冲突，专注于核心功能验证
"""

import sys
import os

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

def test_signal_mechanism_simple():
    """简化的信号机制测试"""
    print("🔍 测试信号机制...")
    
    try:
        from main_window import ChatResponseHandler
        from PySide6.QtCore import QObject
        
        # 创建响应处理器
        handler = ChatResponseHandler()
        
        # 测试信号连接
        received_responses = []
        
        def on_response(response):
            print(f"✅ 收到信号响应: {response}")
            received_responses.append(response)
        
        handler.response_ready.connect(on_response)
        
        # 发送测试信号
        test_messages = [
            "测试消息1",
            "测试消息2",
            "包含特殊字符的消息：😊🎉"
        ]
        
        for msg in test_messages:
            print(f"📤 发送测试信号: {msg}")
            handler.emit_response(msg)
        
        # 验证结果
        if len(received_responses) == len(test_messages):
            print("✅ 信号机制测试成功")
            return True
        else:
            print(f"❌ 信号机制测试失败: 期望{len(test_messages)}个响应，实际收到{len(received_responses)}个")
            return False
            
    except Exception as e:
        print(f"❌ 信号机制测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_overlay_creation():
    """测试文本叠加层创建"""
    print("\n🔍 测试文本叠加层创建...")
    
    try:
        from text_overlay import TextOverlay
        from PySide6.QtWidgets import QWidget
        
        # 创建一个虚拟父窗口
        parent = QWidget()
        parent.setGeometry(100, 100, 400, 300)
        
        # 创建文本叠加层
        overlay = TextOverlay(parent)
        
        print(f"✅ 文本叠加层创建成功")
        print(f"  - 类型: {type(overlay)}")
        print(f"  - 父窗口: {overlay.parent_widget}")
        print(f"  - 窗口标志: {overlay.windowFlags()}")
        
        # 测试文本设置
        test_text = "这是一个测试消息"
        overlay.show_text(test_text)
        
        print(f"✅ 文本设置成功")
        print(f"  - 文本内容: {overlay.text()[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 文本叠加层创建测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试配置加载...")
    
    try:
        from settings_dialog import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        print(f"✅ 配置管理器创建成功")
        print(f"  - 配置文件路径: {config_manager.config_file}")
        
        # 检查LLM配置
        llm_config = config_manager.config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        print(f"📊 LLM配置状态:")
        print(f"  - API地址: {api_config.get('base_url', '未配置')}")
        print(f"  - API密钥: {'已配置' if api_config.get('api_key') else '未配置'}")
        print(f"  - 模型: {api_config.get('model', '未配置')}")
        
        return bool(api_config.get('api_key') and api_config.get('base_url'))
        
    except Exception as e:
        print(f"❌ 配置加载测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_client_creation():
    """测试LLM客户端创建"""
    print("\n🔍 测试LLM客户端创建...")
    
    try:
        from llm_client import LLMClient
        from settings_dialog import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建LLM客户端
        llm_client = LLMClient(config_manager)
        
        print(f"✅ LLM客户端创建成功")
        print(f"  - 配置状态: {'已配置' if llm_client.is_configured() else '未配置'}")
        
        return llm_client.is_configured()
        
    except Exception as e:
        print(f"❌ LLM客户端创建测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化的快速输入修复测试...")
    print("=" * 60)
    
    # 测试1: 信号机制
    signal_result = test_signal_mechanism_simple()
    
    # 测试2: 文本叠加层创建
    overlay_result = test_text_overlay_creation()
    
    # 测试3: 配置加载
    config_result = test_config_loading()
    
    # 测试4: LLM客户端创建
    llm_result = test_llm_client_creation()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"- 信号机制测试: {'✅ 成功' if signal_result else '❌ 失败'}")
    print(f"- 文本叠加层测试: {'✅ 成功' if overlay_result else '❌ 失败'}")
    print(f"- 配置加载测试: {'✅ 成功' if config_result else '❌ 失败'}")
    print(f"- LLM客户端测试: {'✅ 成功' if llm_result else '❌ 失败'}")
    
    all_passed = signal_result and overlay_result and config_result and llm_result
    
    if all_passed:
        print("\n🎉 所有核心组件测试通过！")
        print("快速输入功能的基础组件都正常工作。")
        
        print("\n💡 下一步:")
        print("1. 启动主程序测试完整功能")
        print("2. 使用快速输入功能验证修复效果")
        
    elif signal_result and overlay_result:
        print("\n🎯 核心功能正常！")
        print("信号机制和文本显示组件都正常工作。")
        if not config_result:
            print("⚠️ 配置问题：请检查API配置是否正确")
        if not llm_result:
            print("⚠️ LLM问题：请检查API连接是否正常")
            
    else:
        print("\n⚠️ 核心组件有问题，需要进一步调试")
        
        if not signal_result:
            print("❌ 信号机制问题：可能影响回调功能")
        if not overlay_result:
            print("❌ 文本显示问题：可能影响界面显示")

if __name__ == "__main__":
    main()

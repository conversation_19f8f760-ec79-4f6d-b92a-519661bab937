#!/usr/bin/env python3
"""
测试APITestWorker功能
模拟界面API测试的工作方式
"""

import sys
import os
import json
import time

# 添加路径
sys.path.append(os.path.dirname(__file__))

def test_api_worker():
    """测试APITestWorker"""
    print("🔍 测试APITestWorker...")
    
    try:
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        print(f"使用配置: {api_config}")
        
        # 模拟APITestWorker的工作方式
        from settings_dialog import APITestWorker
        
        # 创建测试结果收集器
        class TestResultCollector:
            def __init__(self):
                self.success = False
                self.error_message = None
                self.finished = False
            
            def on_success(self):
                print("✅ API测试成功信号收到")
                self.success = True
                self.finished = True
            
            def on_failed(self, error):
                print(f"❌ API测试失败信号收到: {error}")
                self.error_message = error
                self.finished = True
        
        collector = TestResultCollector()
        
        # 创建APITestWorker
        worker = APITestWorker(api_config)
        worker.test_success.connect(collector.on_success)
        worker.test_failed.connect(collector.on_failed)
        
        print("📤 启动API测试线程...")
        worker.start()
        
        # 等待测试完成
        timeout = 30  # 30秒超时
        elapsed = 0
        while not collector.finished and elapsed < timeout:
            time.sleep(0.1)
            elapsed += 0.1
        
        if not collector.finished:
            print("⏰ 测试超时")
            return False
        
        if collector.success:
            print("🎉 APITestWorker测试成功！")
            return True
        else:
            print(f"❌ APITestWorker测试失败: {collector.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ APITestWorker测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_llm_client():
    """直接测试LLMClient（作为对比）"""
    print("\n🔍 直接测试LLMClient（作为对比）...")
    
    try:
        from llm_client import LLMClient
        
        # 创建简化配置
        class SimpleConfig:
            def __init__(self):
                with open("config.json", "r", encoding="utf-8") as f:
                    self.config = json.load(f)
        
        config_manager = SimpleConfig()
        llm_client = LLMClient(config_manager)
        
        if not llm_client.is_configured():
            print("❌ LLMClient未配置")
            return False
        
        print("📤 发送测试消息...")
        response = llm_client.chat("Hello")
        
        if response and not response.startswith("❌"):
            print(f"✅ LLMClient直接测试成功: {response[:50]}...")
            return True
        else:
            print(f"❌ LLMClient直接测试失败: {response}")
            return False
            
    except Exception as e:
        print(f"❌ LLMClient直接测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始APITestWorker测试...")
    print("=" * 60)
    
    # 测试1: 直接LLMClient调用（确保基础功能正常）
    direct_result = test_direct_llm_client()
    
    # 测试2: APITestWorker（模拟界面测试）
    worker_result = test_api_worker()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"- 直接LLMClient测试: {'✅ 成功' if direct_result else '❌ 失败'}")
    print(f"- APITestWorker测试: {'✅ 成功' if worker_result else '❌ 失败'}")
    
    if direct_result and worker_result:
        print("\n🎉 所有测试通过！")
        print("界面API测试功能应该正常工作了。")
        
        print("\n💡 如果界面仍然显示'测试中...'，可能的原因:")
        print("- Qt信号连接问题")
        print("- UI线程更新问题")
        print("- 界面组件状态更新问题")
        
    elif direct_result and not worker_result:
        print("\n⚠️ LLMClient正常，但APITestWorker失败")
        print("问题可能在APITestWorker的实现中")
        
    elif not direct_result and worker_result:
        print("\n⚠️ 这种情况不应该出现")
        
    else:
        print("\n❌ 所有测试失败")
        print("基础API调用有问题")

if __name__ == "__main__":
    main()

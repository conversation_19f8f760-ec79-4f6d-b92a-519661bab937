import datetime
import calendar

class TimeService:
    def __init__(self):
        pass
        
    def get_current_time(self, format_type="detailed"):
        now = datetime.datetime.now()
        
        if format_type == "simple":
            return {
                "time": now.strftime("%H:%M:%S"),
                "date": now.strftime("%Y-%m-%d")
            }
        elif format_type == "detailed":
            return {
                "datetime": now.strftime("%Y年%m月%d日 %H:%M:%S"),
                "year": now.year,
                "month": now.month,
                "day": now.day,
                "hour": now.hour,
                "minute": now.minute,
                "second": now.second,
                "weekday": calendar.day_name[now.weekday()],
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()]
            }
        else:
            return {
                "timestamp": now.timestamp(),
                "iso_format": now.isoformat(),
                "formatted": now.strftime("%Y-%m-%d %H:%M:%S")
            }
    
    def get_time_info(self, info_type="period"):
        now = datetime.datetime.now()
        
        if info_type == "weekday":
            return {
                "weekday": calendar.day_name[now.weekday()],
                "weekday_cn": ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"][now.weekday()],
                "is_weekend": now.weekday() >= 5
            }
        elif info_type == "season":
            month = now.month
            if month in [12, 1, 2]:
                season = "冬季"
            elif month in [3, 4, 5]:
                season = "春季"
            elif month in [6, 7, 8]:
                season = "夏季"
            else:
                season = "秋季"
            return {"season": season, "month": month}
        else:  # period
            hour = now.hour
            if 5 <= hour < 12:
                period = "上午"
                greeting = "早上好"
            elif 12 <= hour < 18:
                period = "下午"
                greeting = "下午好"
            elif 18 <= hour < 22:
                period = "傍晚"
                greeting = "晚上好"
            else:
                period = "深夜"
                greeting = "夜深了"
            
            return {
                "period": period,
                "greeting": greeting,
                "hour": hour,
                "is_work_time": 9 <= hour < 18,
                "is_sleep_time": hour >= 23 or hour < 6
            }

# 文件操作MCP对话工具

这是一个基于MCP (Model Context Protocol) 的智能文件操作助手，可以通过自然语言对话来执行各种文件和目录操作。

## 功能特性

### 🔧 文件操作功能
- **读取文件**: 读取任意文本文件的内容
- **写入文件**: 创建新文件或覆盖现有文件内容
- **追加文件**: 向现有文件末尾追加内容
- **创建目录**: 创建单个或多级目录
- **删除文件/目录**: 安全删除文件或整个目录
- **列出目录**: 查看目录中的所有文件和子目录
- **复制文件**: 复制文件到指定位置
- **移动/重命名**: 移动文件或重命名文件
- **文件信息**: 获取文件的详细信息（大小、修改时间等）

### 🤖 智能对话
- 支持自然语言交互
- 智能理解用户意图
- 提供操作结果反馈
- 错误处理和提示

### 🔒 安全特性
- 文件大小限制
- 支持的文件类型限制
- 操作确认机制
- 错误处理和回滚

## 项目结构

```
ApplyMCP/
├── file_operations_mcp.py    # MCP服务器实现
├── mcp_client.py            # MCP客户端
├── agent.py                 # 对话代理
├── llm_client.py           # LLM客户端
├── config.py               # 配置文件
├── prompts.py              # 提示词
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包
└── README.md              # 说明文档
```

## 安装和使用

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置API密钥

确保在 `../api.txt` 文件中配置了正确的LLM API密钥。

### 3. 运行程序

```bash
python main.py
```

### 4. 开始对话

程序启动后，您可以使用自然语言与助手对话：

```
👤 您: 读取 test.txt 文件的内容

👤 您: 在 workspace 目录创建一个名为 hello.txt 的文件，内容是 Hello World

👤 您: 列出当前目录的所有文件

👤 您: 复制 file1.txt 到 backup/file1_backup.txt
```

## 使用示例

### 基本文件操作

```
# 读取文件
👤 您: 帮我读取 config.py 文件的内容
🤖 助手: 正在读取文件内容...

# 创建文件
👤 您: 创建一个名为 notes.txt 的文件，写入"今天的待办事项"
🤖 助手: 已成功创建文件 notes.txt

# 列出目录
👤 您: 看看当前目录有什么文件
🤖 助手: 当前目录包含以下文件...
```

### 高级操作

```
# 批量操作
👤 您: 把所有 .txt 文件复制到 backup 目录
🤖 助手: 正在执行批量复制操作...

# 文件管理
👤 您: 整理一下项目文件，把所有 Python 文件放到 src 目录
🤖 助手: 正在整理文件结构...
```

## 配置说明

### config.py 配置项

- `LLM_BASE_URL`: LLM API基础URL
- `LLM_MODEL`: 使用的LLM模型
- `DEFAULT_WORK_DIR`: 默认工作目录
- `MAX_FILE_SIZE`: 最大文件大小限制
- `ALLOWED_EXTENSIONS`: 允许的文件扩展名

### 安全设置

- 文件大小限制: 默认10MB
- 支持的文件类型: .txt, .py, .json, .md, .yaml, .yml, .xml, .csv
- 自动创建必要的父目录
- 操作前确认机制

## MCP架构

本项目采用MCP (Model Context Protocol) 架构：

1. **MCP服务器** (`file_operations_mcp.py`): 提供文件操作工具
2. **MCP客户端** (`mcp_client.py`): 与MCP服务器通信
3. **对话代理** (`agent.py`): 集成LLM和MCP功能
4. **主程序** (`main.py`): 用户交互界面

## 扩展开发

### 添加新的文件操作

1. 在 `file_operations_mcp.py` 中添加新工具
2. 在 `mcp_client.py` 中添加对应的客户端方法
3. 在 `agent.py` 中添加函数定义
4. 更新提示词和文档

### 自定义配置

修改 `config.py` 中的配置项来适应您的需求。

## 故障排除

### 常见问题

1. **MCP服务启动失败**
   - 检查Python版本 (需要3.7+)
   - 确认依赖包已正确安装

2. **LLM调用失败**
   - 检查API密钥配置
   - 确认网络连接正常

3. **文件操作权限错误**
   - 检查文件/目录权限
   - 确认路径正确

### 调试模式

在代码中添加调试信息来排查问题：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License

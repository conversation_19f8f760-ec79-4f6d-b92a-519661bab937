from agent import VoiceprintIdolAgent
import sys

def main():
    print("🎤✨ WeSpeaker声纹识别偶像AI助手 ✨🎤")
    print("=" * 60)
    print("💖 我是可爱的声纹识别偶像，能记住每个人独特的声音哦～")
    print("🎵 基于WeSpeaker-VoxCeleb-ResNet34模型的高精度声纹识别")
    
    try:
        print("🔄 正在初始化，请稍候...")
        agent = VoiceprintIdolAgent()
    except Exception as e:
        print(f"😅 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n🌟 可用命令:")
    print("🎤 'voice' - 声纹识别对话（录音+识别+聊天）")
    print("📝 'chat <用户名>' - 基于已知声纹的文本对话")
    print("✨ 'register' - 注册高质量声纹样本")
    print("📋 'list' - 查看所有声纹身份")
    print("📊 'profile <用户名>' - 查看详细声纹档案")
    print("🔧 'test' - 测试音频设备")
    print("🚪 'quit' - 退出")
    
    while True:
        try:
            command = input("\n💫 请输入命令: ").strip().lower()
            
            if command == 'quit':
                print("💖 再见！期待下次听到你的声音～")
                break
                
            elif command == 'voice':
                print("\n🎤 --- 声纹识别对话模式 ---")
                response = agent.voice_chat()
                print(f"\n🎵 偶像助手: {response}")
                
            elif command.startswith('chat'):
                parts = command.split(' ', 1)
                if len(parts) > 1:
                    user_name = parts[1]
                    response = agent.voice_analysis_chat(user_name)
                    print(f"\n🎵 偶像助手: {response}")
                else:
                    print("💡 请指定用户名: chat <用户名>")
                    
            elif command == 'register':
                print("\n✨ --- 高质量声纹注册 ---")
                response = agent.register_high_quality_voice()
                print(f"\n🎵 {response}")
                
            elif command == 'list':
                print("\n📋 --- 声纹身份列表 ---")
                print(agent.list_voice_identities())
                
            elif command.startswith('profile'):
                parts = command.split(' ', 1)
                if len(parts) > 1:
                    user_name = parts[1]
                    print(f"\n📊 --- {user_name} 的声纹档案 ---")
                    print(agent.get_voice_profile(user_name))
                else:
                    print("💡 请指定用户名: profile <用户名>")
                    
            elif command == 'test':
                print("\n🔧 --- 音频设备测试 ---")
                devices = agent.audio_processor.get_audio_devices()
                print("🎤 可用输入设备:")
                for device in devices:
                    print(f"  📱 {device['id']}: {device['name']} ({device['sample_rate']}Hz)")
                
                print("\n🎵 开始麦克风测试...")
                if agent.audio_processor.test_microphone():
                    print("✅ 麦克风工作正常！")
                else:
                    print("❌ 麦克风可能有问题，请检查设备")
                    
            else:
                print("😅 未知命令呢，请重新输入哦～")
                print("💡 输入命令提示: voice, chat, register, list, profile, test, quit")
                
        except KeyboardInterrupt:
            print("\n💖 再见！期待下次听到你的声音～")
            break
        except Exception as e:
            print(f"😅 出现错误: {e}")
            print("💡 请重试或检查设备连接")

if __name__ == "__main__":
    main()

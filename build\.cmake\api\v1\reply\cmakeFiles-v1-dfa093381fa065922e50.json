{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/LCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CompilerId/VS-10.vcxproj.in"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CompilerId/VS-10.vcxproj.in"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeInspectCLinker.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build/CMakeFiles/4.0.3/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/Internal/CMakeInspectCXXLinker.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in"}, {"path": "Live2D/cmake/Live2D.cmake"}, {"path": "Live2D/cmake/Core.cmake"}, {"path": "Live2D/cmake/Glad.cmake"}, {"path": "Live2D/cmake/Framework.cmake"}, {"path": "Live2D/cmake/Main.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindOpenGL.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"path": "cmake/Wrapper.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPython3.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "E:/Program Files/python/Lib/site-packages/cmake/data/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"path": "Live2D/Glad/CMakeLists.txt"}, {"path": "Live2D/Framework/CMakeLists.txt"}, {"path": "Live2D/Framework/src/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Effect/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Id/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Math/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Model/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Motion/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Physics/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Rendering/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Rendering/OpenGL/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Type/CMakeLists.txt"}, {"path": "Live2D/Framework/src/Utils/CMakeLists.txt"}, {"path": "Live2D/Main/CMakeLists.txt"}, {"path": "Live2D/Main/patch_1.cmake"}, {"path": "Live2D/Main/src/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/build", "source": "D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py"}, "version": {"major": 1, "minor": 1}}
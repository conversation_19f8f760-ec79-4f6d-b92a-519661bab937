target_sources(${LIB_NAME}
  PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismNativeInclude_D3D11.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismOffscreenSurface_D3D11.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismOffscreenSurface_D3D11.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismRenderer_D3D11.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismRenderer_D3D11.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismRenderState_D3D11.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismRenderState_D3D11.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismShader_D3D11.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismShader_D3D11.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismType_D3D11.hpp
)

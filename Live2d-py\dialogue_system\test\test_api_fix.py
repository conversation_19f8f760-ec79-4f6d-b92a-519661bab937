#!/usr/bin/env python3
"""
测试API响应修复效果
验证配置管理和错误处理改进
"""

import sys
import os

# 添加正确的路径
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'dev'))

def test_config_validation():
    """测试配置验证功能"""
    print("🔍 测试配置验证功能...")
    
    try:
        from settings_dialog import ConfigManager
        from dialogue_system.core.llm_client import LLMClient
        
        # 创建空配置的LLM客户端
        config_manager = ConfigManager()
        llm_client = LLMClient(config_manager)
        
        print(f"初始配置状态: {llm_client.is_configured()}")
        
        # 测试配置验证
        is_valid = llm_client._validate_config()
        print(f"配置验证结果: {is_valid}")
        
        # 测试聊天功能（应该返回配置错误）
        response = llm_client.chat("测试消息")
        print(f"聊天响应: {response}")
        
        # 模拟设置API配置
        print("\n🔍 模拟设置API配置...")
        test_config = {
            "api_config": {
                "api_key": "test-key-12345",
                "model": "gpt-3.5-turbo",
                "base_url": "https://api.openai.com/v1"
            }
        }
        
        llm_client.update_config(test_config)
        print(f"更新后配置状态: {llm_client.is_configured()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_preset_config_integration():
    """测试预设配置集成"""
    print("\n🔍 测试预设配置集成...")
    
    try:
        from settings_dialog import ConfigManager
        from dialogue_system.core.preset_manager import PresetManager
        from dialogue_system.core.llm_client import LLMClient
        
        config_manager = ConfigManager()
        
        # 设置一些测试配置
        config_manager.config["llm"] = {
            "api_config": {
                "api_key": "global-test-key",
                "model": "gpt-3.5-turbo",
                "base_url": "https://api.openai.com/v1"
            }
        }
        
        # 创建预设管理器
        preset_manager = PresetManager(config_manager)
        
        # 测试获取当前预设（应该包含API配置）
        current_preset = preset_manager.get_current_preset()
        print(f"当前预设: {current_preset}")
        
        has_api_config = "api_config" in current_preset
        print(f"预设包含API配置: {has_api_config}")
        
        if has_api_config:
            api_key = current_preset["api_config"].get("api_key")
            print(f"预设中的API密钥: {api_key}")
        
        # 测试LLM客户端使用预设配置
        llm_client = LLMClient(config_manager)
        llm_client.preset_manager = preset_manager
        
        # 模拟预设切换
        preset = preset_manager.get_current_preset()
        if preset:
            config_update = {}
            if "system_prompt" in preset:
                config_update["conversation_settings"] = {"system_prompt": preset["system_prompt"]}
            if "api_config" in preset:
                config_update["api_config"] = preset["api_config"]
            
            if config_update:
                llm_client.update_config(config_update)
        
        print(f"预设切换后配置状态: {llm_client.is_configured()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 预设配置集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_messages():
    """测试错误消息改进"""
    print("\n🔍 测试错误消息改进...")
    
    try:
        from settings_dialog import ConfigManager
        from dialogue_system.core.llm_client import LLMClient
        
        # 创建未配置的LLM客户端
        config_manager = ConfigManager()
        llm_client = LLMClient(config_manager)
        
        # 测试各种错误情况
        print("测试1: 未配置API密钥")
        response1 = llm_client.chat("测试")
        print(f"响应1: {response1}")
        
        print("\n测试2: 只设置部分配置")
        llm_client.api_config["api_key"] = "test-key"
        # 但不重新初始化客户端
        response2 = llm_client.chat("测试")
        print(f"响应2: {response2}")
        
        print("\n测试3: 配置验证功能")
        is_valid = llm_client._validate_config()
        print(f"配置验证: {is_valid}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误消息测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dialogue_system_integration():
    """测试对话系统集成"""
    print("\n🔍 测试对话系统集成...")
    
    try:
        from settings_dialog import ConfigManager
        from dialogue_system import DialogueSystemManager
        
        config_manager = ConfigManager()
        
        # 设置测试配置
        config_manager.config["llm"] = {
            "api_config": {
                "api_key": "test-integration-key",
                "model": "gpt-3.5-turbo",
                "base_url": "https://api.openai.com/v1"
            }
        }
        
        # 创建对话系统管理器
        dialogue_manager = DialogueSystemManager(config_manager)
        
        print(f"对话系统配置状态: {dialogue_manager.is_configured()}")
        
        # 测试处理消息（应该返回配置错误，因为API密钥是假的）
        response = dialogue_manager.handle_message("测试消息")
        print(f"对话系统响应: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 API修复效果测试")
    print("=" * 60)
    
    tests = [
        ("配置验证功能", test_config_validation),
        ("预设配置集成", test_preset_config_integration),
        ("错误消息改进", test_error_messages),
        ("对话系统集成", test_dialogue_system_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"- {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！API修复生效。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
    
    print("\n💡 使用建议:")
    print("1. 确保在设置界面正确配置API密钥")
    print("2. 检查预设是否包含完整的API配置")
    print("3. 注意查看控制台的调试信息")
    print("4. 如果仍有问题，请运行 debug_api_response.py 进行详细诊断")


if __name__ == "__main__":
    main()

import numpy as np
import json
import uuid
from datetime import datetime
from pathlib import Path
from config import Config
from audio_processor import AudioProcessor

class SpeakerRecognition:
    def __init__(self):
        self.audio_processor = AudioProcessor()
        self.threshold = Config.SIMILARITY_THRESHOLD
        self.speakers_db_path = Config.SPEAKERS_DB
        self.audio_dir = Config.AUDIO_DIR
        Config.ensure_dirs()
        
        # 加载说话人数据库
        self.speakers_db = self._load_speakers_db()
    
    def _load_speakers_db(self):
        """加载说话人数据库"""
        if self.speakers_db_path.exists():
            with open(self.speakers_db_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    
    def _save_speakers_db(self):
        """保存说话人数据库"""
        with open(self.speakers_db_path, 'w', encoding='utf-8') as f:
            json.dump(self.speakers_db, f, ensure_ascii=False, indent=2)
    
    def cosine_similarity(self, vec1, vec2):
        """计算余弦相似度"""
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        return dot_product / (norm1 * norm2 + 1e-8)
    
    def identify_speaker(self, audio_data):
        """识别说话人"""
        # 预处理音频
        audio_data = self.audio_processor.preprocess_audio(audio_data)
        
        # 提取特征
        features = self.audio_processor.extract_features(audio_data)
        
        best_match = None
        best_similarity = 0
        
        # 与数据库中的所有说话人进行比较
        for speaker_id, speaker_data in self.speakers_db.items():
            stored_features = np.array(speaker_data['features'])
            similarity = self.cosine_similarity(features, stored_features)
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = speaker_id
        
        # 判断是否超过阈值
        if best_similarity > self.threshold:
            return {
                'speaker_id': best_match,
                'similarity': best_similarity,
                'is_known': True,
                'speaker_info': self.speakers_db[best_match]
            }
        else:
            return {
                'speaker_id': None,
                'similarity': best_similarity,
                'is_known': False,
                'features': features.tolist()  # 用于注册新用户
            }
    
    def register_speaker(self, audio_data, name, additional_info=None):
        """注册新说话人"""
        # 预处理音频
        audio_data = self.audio_processor.preprocess_audio(audio_data)
        
        # 提取特征
        features = self.audio_processor.extract_features(audio_data)
        
        # 生成唯一ID
        speaker_id = str(uuid.uuid4())
        
        # 保存音频样本
        audio_file = self.audio_dir / f"{speaker_id}.wav"
        import soundfile as sf
        sf.write(audio_file, audio_data, Config.SAMPLE_RATE)
        
        # 创建说话人记录
        speaker_record = {
            'name': name,
            'speaker_id': speaker_id,
            'features': features.tolist(),
            'audio_file': str(audio_file),
            'registration_time': datetime.now().isoformat(),
            'conversation_history': [],
            'preferences': {},
            'additional_info': additional_info or {}
        }
        
        # 保存到数据库
        self.speakers_db[speaker_id] = speaker_record
        self._save_speakers_db()
        
        return speaker_id
    
    def update_speaker_memory(self, speaker_id, conversation_entry):
        """更新说话人对话记忆"""
        if speaker_id in self.speakers_db:
            self.speakers_db[speaker_id]['conversation_history'].append({
                'timestamp': datetime.now().isoformat(),
                'content': conversation_entry
            })
            
            # 限制历史记录长度
            if len(self.speakers_db[speaker_id]['conversation_history']) > 50:
                self.speakers_db[speaker_id]['conversation_history'] = \
                    self.speakers_db[speaker_id]['conversation_history'][-50:]
            
            self._save_speakers_db()
    
    def get_speaker_context(self, speaker_id):
        """获取说话人上下文信息"""
        if speaker_id in self.speakers_db:
            speaker_data = self.speakers_db[speaker_id]
            recent_conversations = speaker_data['conversation_history'][-5:]  # 最近5次对话
            
            return {
                'name': speaker_data['name'],
                'recent_conversations': recent_conversations,
                'preferences': speaker_data.get('preferences', {}),
                'total_conversations': len(speaker_data['conversation_history'])
            }
        return None

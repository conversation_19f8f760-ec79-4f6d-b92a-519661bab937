import numpy as np
import librosa
import soundfile as sf
import pyaudio
import wave
from pathlib import Path
from config import Config

class AudioProcessor:
    def __init__(self):
        self.sample_rate = Config.SAMPLE_RATE
        self.duration = Config.AUDIO_DURATION
        self.chunk_size = Config.CHUNK_SIZE
        
    def record_audio(self, output_path=None):
        """录制音频"""
        print("🎤 开始录音，请说话...")
        
        audio = pyaudio.PyAudio()
        stream = audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size
        )
        
        frames = []
        for _ in range(int(self.sample_rate * self.duration / self.chunk_size)):
            data = stream.read(self.chunk_size)
            frames.append(data)
        
        stream.stop_stream()
        stream.close()
        audio.terminate()
        
        print("✅ 录音完成")
        
        # 保存音频文件
        if output_path:
            with wave.open(str(output_path), 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(audio.get_sample_size(pyaudio.paInt16))
                wf.setframerate(self.sample_rate)
                wf.writeframes(b''.join(frames))
        
        # 转换为numpy数组
        audio_data = np.frombuffer(b''.join(frames), dtype=np.int16)
        return audio_data.astype(np.float32) / 32768.0
    
    def load_audio(self, file_path):
        """加载音频文件"""
        audio, sr = librosa.load(file_path, sr=self.sample_rate)
        return audio
    
    def extract_features(self, audio_data):
        """提取音频特征 - 使用MFCC作为简化的说话人特征"""
        # 提取MFCC特征
        mfcc = librosa.feature.mfcc(
            y=audio_data, 
            sr=self.sample_rate, 
            n_mfcc=13,
            n_fft=2048,
            hop_length=512
        )
        
        # 计算统计特征
        mfcc_mean = np.mean(mfcc, axis=1)
        mfcc_std = np.std(mfcc, axis=1)
        
        # 提取mel频谱特征
        mel_spec = librosa.feature.melspectrogram(
            y=audio_data,
            sr=self.sample_rate,
            n_mels=40
        )
        mel_mean = np.mean(mel_spec, axis=1)
        
        # 组合特征向量
        features = np.concatenate([mfcc_mean, mfcc_std, mel_mean])
        
        # 归一化
        features = features / (np.linalg.norm(features) + 1e-8)
        
        return features
    
    def preprocess_audio(self, audio_data):
        """音频预处理"""
        # 去除静音段
        audio_data = librosa.effects.trim(audio_data, top_db=20)[0]
        
        # 音量归一化
        audio_data = librosa.util.normalize(audio_data)
        
        return audio_data

# Live2D桌面宠物对话系统模块

## 概述

这是Live2D桌面宠物项目的对话功能模块化重构结果。通过模块化架构，实现了代码的清晰组织、便于维护和扩展。

## 目录结构

```
dialogue_system/
├── __init__.py                    # 模块初始化和统一接口
├── core/                          # 核心对话功能
│   ├── __init__.py
│   ├── llm_client.py             # LLM客户端
│   ├── preset_manager.py         # 预设管理器
│   ├── chat_dialog.py            # 对话界面
│   └── response_handler.py       # 响应处理器
├── ui/                           # 用户界面组件
│   ├── __init__.py
│   ├── text_overlay.py          # 文本显示组件
│   └── quick_input.py           # 快速输入组件
├── test/                        # 测试文件
│   ├── __init__.py
│   ├── test_quick_input.py      # 快速输入测试
│   ├── test_quick_input_complete.py  # 完整功能测试
│   ├── test_module_integration.py    # 模块集成测试
│   └── test_main_window_import.py    # 主窗口导入测试
├── tts/                         # 文本转语音（预留）
│   └── __init__.py
├── stt/                         # 语音转文本（预留）
│   └── __init__.py
├── docs/                        # 文档
│   ├── quick_input_optimization.md  # 快速输入优化说明
│   └── preset_switching_solution.md # 预设切换解决方案
└── README.md                    # 本文件
```

## 主要功能

### 核心组件

1. **LLMClient** - LLM客户端，处理与OpenAI API的交互
2. **PresetManager** - 预设管理器，管理不同的对话预设
3. **ChatDialog** - 对话界面窗口，提供独立的文字对话界面
4. **ChatResponseHandler** - 聊天响应处理器，使用信号机制

### UI组件

1. **TextOverlay** - 文本叠加显示组件，支持打字机效果
2. **TextDisplayManager** - 文本显示管理器，统一管理文本显示
3. **QuickInputOverlay** - 快速输入叠加层，支持预设切换

### 管理器

1. **DialogueSystemManager** - 对话系统管理器，提供一站式对话功能管理

## 使用方法

### 基本使用

```python
from dialogue_system import LLMClient, ChatDialog, TextDisplayManager

# 创建LLM客户端
llm_client = LLMClient(config_manager)

# 创建对话界面
chat_dialog = ChatDialog(config_manager)

# 创建文本显示管理器
text_manager = TextDisplayManager(parent_widget)
```

### 高级使用（推荐）

```python
from dialogue_system import DialogueSystemManager

# 创建对话系统管理器（一站式解决方案）
dialogue_manager = DialogueSystemManager(config_manager, parent_widget)

# 显示对话界面
dialogue_manager.show_chat_dialog()

# 显示快速输入
dialogue_manager.show_quick_input()

# 处理消息
response = dialogue_manager.handle_message("Hello")
```

### 在主窗口中使用

```python
# 在 main_window.py 中
from dialogue_system import ChatDialog, TextDisplayManager, LLMClient, ChatResponseHandler

# 初始化对话组件
self.llm_client = LLMClient(self.config_manager)
self.text_display_manager = TextDisplayManager(self)
self.chat_response_handler = ChatResponseHandler(self)
```

## 重构优势

### 1. 模块化架构
- **清晰的职责分离**: 核心功能、UI组件、测试分别组织
- **便于维护**: 每个模块职责单一，易于理解和修改
- **易于扩展**: 为TTS/STT功能预留了空间

### 2. 代码重用
- **组件化设计**: 各个组件可以独立使用
- **标准化接口**: 统一的API设计
- **配置灵活**: 支持多种配置方式

### 3. 测试友好
- **独立测试**: 每个模块可以独立测试
- **模拟简单**: 依赖注入使得模拟更容易
- **覆盖全面**: 从单元测试到集成测试

### 4. 向后兼容
- **保持原有接口**: 主窗口的使用方式基本不变
- **渐进式迁移**: 可以逐步迁移到新的模块化架构
- **兼容性测试**: 确保原有功能正常工作

## 测试

### 运行测试

```bash
# 基本功能测试
cd dialogue_system/test
python test_quick_input.py

# 完整功能测试
python test_quick_input_complete.py

# 模块集成测试
python test_module_integration.py

# 主窗口导入测试
python test_main_window_import.py
```

### 测试结果

所有测试均通过，验证了：
- ✅ 模块导入正常
- ✅ 基本功能正常
- ✅ 对话系统管理器正常
- ✅ 主窗口import正常
- ✅ 向后兼容性良好

## 未来扩展

### 计划功能

1. **TTS集成** - 在 `dialogue_system/tts/` 目录中实现文本转语音功能
2. **STT集成** - 在 `dialogue_system/stt/` 目录中实现语音转文本功能
3. **插件系统** - 基于模块化架构构建插件系统
4. **多语言支持** - 支持多种语言的对话功能

### 扩展指南

1. **添加新的核心功能**: 在 `dialogue_system/core/` 目录中添加新模块
2. **添加新的UI组件**: 在 `dialogue_system/ui/` 目录中添加新组件
3. **添加测试**: 在 `dialogue_system/test/` 目录中添加相应测试
4. **更新接口**: 在 `dialogue_system/__init__.py` 中导出新的API

## 依赖要求

- Python 3.7+
- PySide6
- OpenAI Python库
- 配置管理器 (ConfigManager)

## 版本信息

- **版本**: 1.0.0
- **作者**: Live2D Desktop Pet Team
- **重构日期**: 2025-01-28

## 贡献指南

1. 遵循现有的代码结构和命名规范
2. 为新功能添加相应的测试
3. 更新文档和README
4. 确保向后兼容性

## 许可证

与Live2D桌面宠物项目保持一致的许可证。

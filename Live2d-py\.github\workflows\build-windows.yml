﻿name: Windows-intel📦

on:
  push:
    tags:
      - "v*.*.*"
    branches: [ windows-workflow, build-all ]


jobs:
  build-n-publish:
    name: Build📦 for Windows
    runs-on: windows-latest

    strategy:
      matrix:
        python-version: [ '3.8.0', '3.9.0', '3.10.0', '3.11.0', '3.12.0', '3.13' ]
        architecture: [ 'x86', 'x64' ]
      fail-fast: false
    
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          architecture: ${{ matrix.architecture }}

      - name: Install build deps
        run: |
          python -m pip install --upgrade setuptools
          python -m pip install wheel

      - name: Build a binary wheel
        run: |
          python setup.py build_ext
          python setup.py bdist_wheel

      - name: Install twine
        run: python -m pip install twine

      - name: Publish distribution to PyPI
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: twine upload dist/* --skip-existing

      - name: Upload Wheel to Release
        uses: softprops/action-gh-release@v2
        with:
          # tag_name: v0.2.2-alpha
          files: |
            dist/*.whl


    
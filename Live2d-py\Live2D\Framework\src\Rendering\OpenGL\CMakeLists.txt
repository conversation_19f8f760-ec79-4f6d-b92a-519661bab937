target_sources(${LIB_NAME}
  PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismOffscreenSurface_OpenGLES2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismOffscreenSurface_OpenGLES2.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismShader_OpenGLES2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismShader_OpenGLES2.hpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismRenderer_OpenGLES2.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/CubismRenderer_OpenGLES2.hpp
)

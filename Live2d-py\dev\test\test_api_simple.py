#!/usr/bin/env python3
"""
简化的API测试脚本
不依赖Qt组件，直接测试API调用
"""

import json
import sys
import os

def test_openai_direct():
    """直接测试OpenAI API调用"""
    print("🔍 直接测试OpenAI API调用...")
    
    try:
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        print(f"API配置: {api_config}")
        
        # 检查配置
        if not api_config.get("api_key"):
            print("❌ API密钥为空")
            return False
        
        if not api_config.get("base_url"):
            print("❌ API地址为空")
            return False
        
        # 导入OpenAI
        try:
            from openai import OpenAI
            print("✅ OpenAI库导入成功")
        except ImportError as e:
            print(f"❌ OpenAI库未安装: {e}")
            print("请安装: pip install openai>=1.0.0")
            return False
        
        # 创建客户端
        try:
            client = OpenAI(
                base_url=api_config["base_url"],
                api_key=api_config["api_key"],
                timeout=10.0
            )
            print("✅ OpenAI客户端创建成功")
        except Exception as e:
            print(f"❌ OpenAI客户端创建失败: {e}")
            return False
        
        # 发送测试请求
        try:
            print("📤 发送测试请求...")
            
            response = client.chat.completions.create(
                model=api_config["model"],
                messages=[
                    {"role": "user", "content": "Hello"}
                ],
                max_tokens=50,
                temperature=0.7
            )
            
            result = response.choices[0].message.content
            print(f"✅ API调用成功")
            print(f"📥 响应: {result}")
            return True
            
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_llm_client_simple():
    """简化测试LLMClient（不依赖Qt）"""
    print("\n🔍 测试LLMClient（简化版）...")
    
    try:
        # 直接导入LLMClient
        sys.path.append(os.path.dirname(__file__))
        from llm_client import LLMClient
        
        # 创建简化的配置管理器
        class SimpleConfig:
            def __init__(self):
                with open("config.json", "r", encoding="utf-8") as f:
                    self.config = json.load(f)
        
        config_manager = SimpleConfig()
        llm_client = LLMClient(config_manager)
        
        print(f"LLM客户端配置状态: {'已配置' if llm_client.is_configured() else '未配置'}")
        
        if not llm_client.is_configured():
            print("❌ LLM客户端未正确配置")
            return False
        
        # 测试对话
        print("📤 发送测试消息...")
        response = llm_client.chat("Hello")
        
        if response and not response.startswith("❌"):
            print("✅ LLMClient测试成功")
            print(f"📥 响应: {response}")
            return True
        else:
            print(f"❌ LLMClient测试失败: {response}")
            return False
            
    except Exception as e:
        print(f"❌ LLMClient测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化API测试...")
    print("=" * 50)
    
    # 测试1: 直接OpenAI调用
    test1_result = test_openai_direct()
    
    # 测试2: LLMClient调用
    test2_result = test_llm_client_simple()
    
    print("\n" + "=" * 50)
    if test1_result and test2_result:
        print("🎉 所有API测试通过！")
        print("\n✅ 确认:")
        print("- API配置正确")
        print("- OpenAI库正常工作")
        print("- 自定义API兼容OpenAI格式")
        print("- LLMClient正常工作")
        
        print("\n💡 如果界面API测试仍然失败，问题可能在于:")
        print("- Qt线程通信")
        print("- 信号连接")
        print("- UI更新机制")
        
    elif test1_result and not test2_result:
        print("⚠️ 直接API调用成功，但LLMClient失败")
        print("问题可能在LLMClient的实现中")
        
    elif not test1_result and test2_result:
        print("⚠️ LLMClient成功，但直接API调用失败")
        print("这种情况不太可能出现")
        
    else:
        print("❌ 所有API测试失败")
        print("\n🔧 建议检查:")
        print("- API密钥是否正确")
        print("- 网络连接是否正常")
        print("- API地址是否可访问")
        print("- 模型名称是否正确")

if __name__ == "__main__":
    main()

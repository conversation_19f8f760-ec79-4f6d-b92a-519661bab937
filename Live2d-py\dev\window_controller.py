#!/usr/bin/env python3
"""
窗口控制器
负责窗口的透明度、置顶、缩放等控制
"""

import ctypes
from ctypes import wintypes
from PySide6.QtCore import QObject, Signal, QPoint
from PySide6.QtWidgets import QMenu, QApplication
from PySide6.QtGui import QCursor


# Windows API 函数定义
user32 = ctypes.windll.user32
dwmapi = ctypes.windll.dwmapi

def make_window_transparent(hwnd):
    """使窗口透明"""
    try:
        # 设置窗口扩展样式
        ex_style = user32.GetWindowLongW(hwnd, -20)  # GWL_EXSTYLE
        user32.SetWindowLongW(hwnd, -20, ex_style | 0x80000 | 0x20)  # WS_EX_LAYERED | WS_EX_TRANSPARENT
        
        # 设置分层窗口属性
        user32.SetLayeredWindowAttributes(hwnd, 0, 255, 2)  # LWA_ALPHA
        
        # 启用点击穿透，但保留模型区域的交互
        user32.SetWindowLongW(hwnd, -20, ex_style | 0x80000)  # 只保留 WS_EX_LAYERED
        
        print("Window transparency applied successfully")
        return True
    except Exception as e:
        print(f"Failed to apply transparency: {e}")
        return False

def make_window_opaque(hwnd):
    """使窗口不透明"""
    try:
        ex_style = user32.GetWindowLongW(hwnd, -20)
        user32.SetWindowLongW(hwnd, -20, ex_style & ~0x80000 & ~0x20)  # 移除透明相关样式
        print("Window opacity restored")
        return True
    except Exception as e:
        print(f"Failed to restore opacity: {e}")
        return False


class WindowController(QObject):
    """窗口控制器"""
    
    # 定义信号
    transparency_changed = Signal(bool)
    always_on_top_changed = Signal(bool)
    window_resized = Signal(int, int)
    
    def __init__(self, window, config_manager):
        super().__init__()
        self.window = window
        self.config_manager = config_manager
        self.transparent_hwnd = None
        self.is_transparent = True
        self.is_always_on_top = True
        
        # 缩放和拖拽状态
        self.is_resizing = False
        self.resize_edge = None
        self.resize_start_pos = None
        self.resize_start_size = None
        self.clickInLA = False
        self.clickX = 0
        self.clickY = 0
    
    def setup_window_properties(self):
        """设置窗口属性"""
        # 获取窗口句柄
        hwnd = int(self.window.winId())
        if hwnd:
            self.transparent_hwnd = hwnd
            self.refresh_transparency()
            self.refresh_always_on_top()
    
    def refresh_transparency(self):
        """刷新透明设置"""
        if not self.transparent_hwnd:
            hwnd = int(self.window.winId())
            if hwnd:
                self.transparent_hwnd = hwnd
        
        if self.transparent_hwnd:
            if self.is_transparent:
                make_window_transparent(self.transparent_hwnd)
                print("透明设置已刷新")
            else:
                make_window_opaque(self.transparent_hwnd)
                print("不透明设置已刷新")
    
    def refresh_always_on_top(self):
        """刷新置顶设置"""
        if self.is_always_on_top:
            self.window.setWindowFlags(self.window.windowFlags() | 0x8)  # Qt.WindowStaysOnTopHint
        else:
            self.window.setWindowFlags(self.window.windowFlags() & ~0x8)
        self.window.show()
    
    def toggle_transparency(self):
        """切换透明模式"""
        self.is_transparent = not self.is_transparent
        self.config_manager.set("window", "transparent", self.is_transparent)
        self.config_manager.save_config()
        self.refresh_transparency()
        self.transparency_changed.emit(self.is_transparent)
        print(f"切换到{'透明' if self.is_transparent else '不透明'}模式")
    
    def toggle_always_on_top(self):
        """切换窗口置顶"""
        self.is_always_on_top = not self.is_always_on_top
        self.config_manager.set("window", "always_on_top", self.is_always_on_top)
        self.config_manager.save_config()
        self.refresh_always_on_top()
        self.always_on_top_changed.emit(self.is_always_on_top)
        print(f"窗口置顶: {'开启' if self.is_always_on_top else '关闭'}")
    
    def show_context_menu(self, global_pos):
        """显示右键菜单"""
        menu = QMenu()
        
        # 透明模式切换
        transparency_text = "关闭透明" if self.is_transparent else "开启透明"
        transparency_action = menu.addAction(f"🔍 {transparency_text}")
        transparency_action.triggered.connect(self.toggle_transparency)
        
        # 窗口置顶切换
        top_text = "取消置顶" if self.is_always_on_top else "窗口置顶"
        top_action = menu.addAction(f"📌 {top_text}")
        top_action.triggered.connect(self.toggle_always_on_top)
        
        menu.addSeparator()
        
        # 设置选项
        settings_action = menu.addAction("⚙️ 设置")
        settings_action.triggered.connect(self.window.show_settings)
        
        menu.addSeparator()
        
        # 退出选项
        exit_action = menu.addAction("❌ 退出")
        exit_action.triggered.connect(self.window.close)
        
        menu.exec(global_pos)
    
    def update_cursor(self, x, y):
        """更新光标样式"""
        edge_size = 10
        width = self.window.width()
        height = self.window.height()
        
        # 检查是否在边缘
        at_right = x >= width - edge_size
        at_bottom = y >= height - edge_size
        at_corner = at_right and at_bottom
        
        if at_corner:
            QApplication.setOverrideCursor(QCursor(7))  # Qt.SizeFDiagCursor
        elif at_right:
            QApplication.setOverrideCursor(QCursor(6))  # Qt.SizeHorCursor
        elif at_bottom:
            QApplication.setOverrideCursor(QCursor(8))  # Qt.SizeVerCursor
        else:
            QApplication.restoreOverrideCursor()
    
    def handle_mouse_press(self, x, y, button):
        """处理鼠标按下事件"""
        edge_size = 10
        width = self.window.width()
        height = self.window.height()
        
        # 检查是否在调整大小区域
        at_right = x >= width - edge_size
        at_bottom = y >= height - edge_size
        at_corner = at_right and at_bottom
        
        if at_corner or at_right or at_bottom:
            self.is_resizing = True
            self.resize_start_pos = QPoint(x, y)
            self.resize_start_size = (width, height)
            
            if at_corner:
                self.resize_edge = 'corner'
            elif at_right:
                self.resize_edge = 'right'
            elif at_bottom:
                self.resize_edge = 'bottom'
            
            print(f"开始调整大小: {self.resize_edge}")
        else:
            # 窗口拖拽
            self.clickInLA = True
            self.clickX = x
            self.clickY = y
    
    def handle_mouse_move(self, x, y):
        """处理鼠标移动事件"""
        # 缩放功能
        if self.is_resizing and self.resize_start_size:
            start_w, start_h = self.resize_start_size
            dx = x - self.resize_start_pos.x()
            dy = y - self.resize_start_pos.y()
            
            new_w, new_h = start_w, start_h
            
            if self.resize_edge == 'right':
                new_w = max(200, start_w + dx)
            elif self.resize_edge == 'bottom':
                new_h = max(200, start_h + dy)
            elif self.resize_edge == 'corner':
                new_w = max(200, start_w + dx)
                new_h = max(200, start_h + dy)
            
            # 调整窗口大小
            self.window.resize(int(new_w), int(new_h))
            self.window_resized.emit(int(new_w), int(new_h))
        
        # 更新光标样式
        elif not self.clickInLA:
            self.update_cursor(x, y)
        
        # 窗口拖拽
        if self.clickInLA:
            new_x = int(self.window.x() + x - self.clickX)
            new_y = int(self.window.y() + y - self.clickY)
            self.window.move(new_x, new_y)
    
    def handle_mouse_release(self, button):
        """处理鼠标释放事件"""
        if self.is_resizing:
            self.is_resizing = False
            self.resize_edge = None
            print("调整大小完成")
        
        self.clickInLA = False
    
    def apply_config(self, config):
        """应用配置"""
        window_config = config["window"]
        
        # 应用窗口设置
        self.is_transparent = window_config["transparent"]
        self.is_always_on_top = window_config["always_on_top"]
        
        # 应用窗口大小
        width = window_config["width"]
        height = window_config["height"]
        self.window.resize(width, height)
        
        # 刷新窗口属性
        self.refresh_transparency()
        self.refresh_always_on_top()
        
        print(f"窗口配置已应用: 大小={width}x{height}, 透明={self.is_transparent}, 置顶={self.is_always_on_top}")

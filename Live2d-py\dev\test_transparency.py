#!/usr/bin/env python3
"""
测试简化后的透明效果实现
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PySide6.QtWidgets import QApplication, QOpenGLWidget
from PySide6.QtCore import Qt
from PySide6.QtOpenGL import QSurfaceFormat
import live2d

# 透明模式枚举 - 简洁高效
class TransparencyMode:
    QT_NATIVE = "qt_native"                        # Qt原生透明（推荐）
    WINDOWS_API_COLORKEY = "windows_api_colorkey"  # Windows API 颜色键透明
    WINDOWS_API_ALPHA = "windows_api_alpha"        # Windows API Alpha透明

class TestTransparencyWindow(QOpenGLWidget):
    """测试透明效果的简单窗口"""
    
    def __init__(self):
        super().__init__()
        self.transparency_mode = TransparencyMode.QT_NATIVE
        self.is_transparent = True
        
        # 设置OpenGL格式
        format = QSurfaceFormat()
        format.setAlphaBufferSize(8)
        format.setDepthBufferSize(24)
        format.setStencilBufferSize(8)
        format.setVersion(2, 1)
        format.setProfile(QSurfaceFormat.OpenGLContextProfile.CompatibilityProfile)
        self.setFormat(format)
        
        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.resize(400, 500)
        
        print(f"测试窗口初始化完成，透明模式: {self.transparency_mode}")
    
    def initializeGL(self):
        """初始化OpenGL"""
        live2d.glewInit()
        live2d.init()
        print("OpenGL初始化完成")
    
    def paintGL(self):
        """绘制函数 - 测试不同的背景色"""
        if self.transparency_mode == TransparencyMode.QT_NATIVE:
            # Qt原生透明：使用白色背景+Alpha0（借鉴QtLive2dDesktop）
            live2d.clearBuffer(1.0, 1.0, 1.0, 0.0)
        elif self.transparency_mode == TransparencyMode.WINDOWS_API_COLORKEY:
            # Windows API 颜色键透明：使用纯黑色背景
            live2d.clearBuffer(0.0, 0.0, 0.0, 1.0)
        elif self.transparency_mode == TransparencyMode.WINDOWS_API_ALPHA:
            # Windows API Alpha透明：使用透明背景
            live2d.clearBuffer(0.0, 0.0, 0.0, 0.0)
    
    def keyPressEvent(self, event):
        """键盘事件 - 切换透明模式"""
        if event.key() == Qt.Key.Key_1:
            self.transparency_mode = TransparencyMode.QT_NATIVE
            print("切换到Qt原生透明模式")
        elif event.key() == Qt.Key.Key_2:
            self.transparency_mode = TransparencyMode.WINDOWS_API_COLORKEY
            print("切换到Windows API颜色键透明模式")
        elif event.key() == Qt.Key.Key_3:
            self.transparency_mode = TransparencyMode.WINDOWS_API_ALPHA
            print("切换到Windows API Alpha透明模式")
        elif event.key() == Qt.Key.Key_Escape:
            self.close()
        
        self.update()

def main():
    app = QApplication(sys.argv)
    
    print("=== 透明效果测试 ===")
    print("按键说明:")
    print("1 - Qt原生透明模式")
    print("2 - Windows API颜色键透明")
    print("3 - Windows API Alpha透明")
    print("ESC - 退出")
    print("==================")
    
    window = TestTransparencyWindow()
    window.show()
    
    try:
        app.exec()
    finally:
        live2d.dispose()
        print("测试完成")

if __name__ == "__main__":
    main()

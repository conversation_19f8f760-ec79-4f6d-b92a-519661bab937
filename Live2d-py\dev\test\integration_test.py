#!/usr/bin/env python3
"""
集成测试脚本
验证所有对话组件是否正常工作
"""

import sys
import os

# 添加路径
sys.path.append(os.path.dirname(__file__))

def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from llm_client import LLMClient, PresetManager
        print("✅ LLM客户端模块导入成功")
    except Exception as e:
        print(f"❌ LLM客户端模块导入失败: {e}")
        return False
    
    try:
        from chat_dialog import ChatDialog
        print("✅ 对话界面模块导入成功")
    except Exception as e:
        print(f"❌ 对话界面模块导入失败: {e}")
        return False
    
    try:
        from text_overlay import TextDisplayManager, TextOverlay, QuickInputOverlay
        print("✅ 文本显示模块导入成功")
    except Exception as e:
        print(f"❌ 文本显示模块导入失败: {e}")
        return False
    
    try:
        from settings_dialog import ConfigManager
        print("✅ 配置管理模块导入成功")
    except Exception as e:
        print(f"❌ 配置管理模块导入失败: {e}")
        return False
    
    return True

def test_config_manager():
    """测试配置管理器"""
    print("\n🔍 测试配置管理器...")
    
    try:
        from settings_dialog import ConfigManager
        config_manager = ConfigManager("test_config.json")
        
        # 检查LLM配置是否存在
        llm_config = config_manager.get("llm", {})
        if llm_config:
            print("✅ LLM配置加载成功")
            print(f"   API配置: {llm_config.get('api_config', {}).get('base_url', 'N/A')}")
            print(f"   模型: {llm_config.get('api_config', {}).get('model', 'N/A')}")
        else:
            print("❌ LLM配置缺失")
            return False
        
        # 检查文本显示配置
        text_config = config_manager.get("text_display", {})
        if text_config:
            print("✅ 文本显示配置加载成功")
            print(f"   打字速度: {text_config.get('typing_speed', 'N/A')}ms")
            print(f"   自动隐藏: {text_config.get('auto_hide_delay', 'N/A')}ms")
        else:
            print("❌ 文本显示配置缺失")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_llm_client():
    """测试LLM客户端"""
    print("\n🔍 测试LLM客户端...")
    
    try:
        from settings_dialog import ConfigManager
        from llm_client import LLMClient, PresetManager
        
        config_manager = ConfigManager("test_config.json")
        llm_client = LLMClient(config_manager)
        
        print("✅ LLM客户端创建成功")
        print(f"   配置状态: {'已配置' if llm_client.is_configured() else '未配置'}")
        print(f"   系统提示词: {llm_client.get_system_prompt()[:50]}...")
        
        # 测试预设管理器
        preset_manager = PresetManager(config_manager)
        presets = preset_manager.get_all_presets()
        print(f"✅ 预设管理器创建成功，共有 {len(presets)} 个预设")
        
        return True
    except Exception as e:
        print(f"❌ LLM客户端测试失败: {e}")
        return False

def test_qt_components():
    """测试Qt组件（需要Qt环境）"""
    print("\n🔍 测试Qt组件...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt

        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试文本显示组件
        from text_overlay import TextOverlay, QuickInputOverlay
        
        # 创建一个简单的父窗口
        from PySide6.QtWidgets import QWidget
        parent = QWidget()
        parent.resize(400, 500)
        
        text_overlay = TextOverlay(parent)
        quick_input = QuickInputOverlay(parent)
        
        print("✅ Qt文本组件创建成功")
        
        # 测试对话界面（不显示）
        from chat_dialog import ChatDialog
        from settings_dialog import ConfigManager
        
        config_manager = ConfigManager("test_config.json")
        chat_dialog = ChatDialog(config_manager)
        
        print("✅ Qt对话界面创建成功")
        
        return True
    except Exception as e:
        print(f"❌ Qt组件测试失败: {e}")
        return False

def cleanup():
    """清理测试文件"""
    test_files = ["test_config.json"]
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️ 清理测试文件: {file}")
            except:
                pass

def main():
    """主测试函数"""
    print("🚀 开始集成测试...")
    print("=" * 50)
    
    all_passed = True
    
    # 测试模块导入
    if not test_imports():
        all_passed = False
    
    # 测试配置管理器
    if not test_config_manager():
        all_passed = False
    
    # 测试LLM客户端
    if not test_llm_client():
        all_passed = False
    
    # 测试Qt组件
    if not test_qt_components():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！对话功能集成成功！")
        print("\n📋 功能清单:")
        print("✅ LLM客户端 - OpenAI API集成")
        print("✅ 对话界面 - 独立聊天窗口")
        print("✅ 文本显示 - Live2D模型上的文本叠加")
        print("✅ 快速输入 - 模型上的输入框")
        print("✅ 预设管理 - 多种对话预设")
        print("✅ 配置管理 - 持久化配置存储")
        print("✅ 右键菜单 - 对话功能入口")
        print("✅ 快捷键支持 - F2对话, F3快速输入")
        print("\n🔧 使用说明:")
        print("1. 在设置界面配置API信息")
        print("2. 右键菜单选择'文字对话'打开聊天界面")
        print("3. 右键菜单选择'快速输入'进行快速对话")
        print("4. 使用F2/F3快捷键快速访问功能")
        print("5. AI回复会显示在Live2D模型脚部")
        
        print("\n🚧 预留接口:")
        print("- STT语音输入接口（stt_client模块）")
        print("- TTS语音输出接口（tts_client模块）")
        print("- 流式对话接口（支持实时响应）")
    else:
        print("❌ 部分测试失败，请检查错误信息")
    
    # 清理
    cleanup()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
测试文本显示功能
验证快速输入和文本叠加层是否正常工作
"""

import sys
import os

# 添加父目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

def test_text_overlay_standalone():
    """独立测试文本叠加层"""
    print("🔍 独立测试文本叠加层...")
    
    try:
        from PySide6.QtWidgets import QApplication, QWidget
        from text_overlay import TextOverlay, QuickInputOverlay
        
        app = QApplication(sys.argv)
        
        # 创建一个测试窗口
        test_window = QWidget()
        test_window.setGeometry(100, 100, 400, 300)
        test_window.setWindowTitle("测试窗口")
        test_window.show()
        
        # 创建文本叠加层
        text_overlay = TextOverlay(test_window)
        print(f"✅ 文本叠加层创建成功")
        print(f"  - 窗口标志: {text_overlay.windowFlags()}")
        print(f"  - 父窗口: {text_overlay.parent_widget}")
        
        # 显示测试文本
        text_overlay.show_text("这是一个测试消息，用于验证文本显示功能是否正常工作。")
        print(f"✅ 文本显示调用完成")
        print(f"  - 是否可见: {text_overlay.isVisible()}")
        print(f"  - 位置: {text_overlay.pos()}")
        print(f"  - 大小: {text_overlay.size()}")
        
        # 创建快速输入叠加层
        quick_input = QuickInputOverlay(test_window)
        print(f"✅ 快速输入叠加层创建成功")
        
        def on_message_sent(message):
            print(f"📤 收到消息: {message}")
            text_overlay.show_text(f"收到消息: {message}")
        
        quick_input.message_sent.connect(on_message_sent)
        
        print("\n🎮 测试说明:")
        print("- 应该看到一个测试窗口和文本叠加层")
        print("- 按F3或右键选择'快速输入'来测试输入功能")
        print("- 关闭窗口退出测试")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 独立测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_text_display_manager():
    """测试文本显示管理器"""
    print("\n🔍 测试文本显示管理器...")
    
    try:
        from PySide6.QtWidgets import QApplication, QWidget
        from text_overlay import TextDisplayManager
        
        app = QApplication(sys.argv)
        
        # 创建测试窗口
        test_window = QWidget()
        test_window.setGeometry(100, 100, 400, 300)
        test_window.setWindowTitle("文本显示管理器测试")
        test_window.show()
        
        # 创建文本显示管理器
        text_manager = TextDisplayManager(test_window)
        print(f"✅ 文本显示管理器创建成功")
        
        def on_quick_input_message(message):
            print(f"📤 快速输入消息: {message}")
            text_manager.show_message(f"回复: {message}")
        
        # 连接信号
        text_manager.connect_signals(on_quick_input_message)
        
        # 显示测试消息
        text_manager.show_message("文本显示管理器测试消息")
        
        print("\n🎮 测试说明:")
        print("- 应该看到测试消息显示在窗口底部")
        print("- 可以测试快速输入功能")
        print("- 关闭窗口退出测试")
        
        return app.exec()
        
    except Exception as e:
        print(f"❌ 文本显示管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始文本显示功能测试...")
    print("=" * 50)
    
    choice = input("选择测试模式:\n1. 独立测试文本叠加层\n2. 测试文本显示管理器\n请输入选择 (1/2): ")
    
    if choice == "1":
        test_text_overlay_standalone()
    elif choice == "2":
        test_text_display_manager()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()

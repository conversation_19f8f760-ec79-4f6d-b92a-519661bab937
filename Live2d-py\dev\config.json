{"window": {"always_on_top": true, "transparent": true, "transparency_mode": "qt_native", "alpha_value": 240, "width": 346, "height": 626, "x": 643, "y": 206, "position_locked": true}, "model": {"current_model_path": "d:\\BaiduNetdiskDownload\\SOVITS\\LLM对话\\Live2d-py\\dev\\..\\examples\\..\\Resources\\v3/Hiyori/Hiyori.model3.json", "scan_directories": ["D:/BaiduNetdiskDownload/SOVITS/LLM对话/Live2d-py/Resources"], "auto_scan": true, "manual_scale_factor": 1.07, "sync_scale_enabled": true, "base_window_width": 400, "base_window_height": 500, "min_scale": 0.3, "max_scale": 3.0, "random_motion_enabled": true, "random_motion_interval_min": 10.0, "random_motion_interval_max": 30.0, "random_expression_enabled": true, "random_expression_interval_min": 15.0, "random_expression_interval_max": 45.0, "available_expressions": ["0", "1", "2", "3", "4", "5"], "selected_expressions": [], "available_motions": ["Idle", "TapBody", "TapHead", "Shake", "Flick", "PinchIn", "PinchOut"], "selected_motions": [], "multi_model_enabled": false, "selected_models": []}, "llm": {"api_config": {"base_url": "https://hzmeaaogifcw.ap-northeast-1.clawcloudrun.com/v1", "api_key": "qq1230", "model": "gpt-3.5-turbo", "timeout": 30.0, "max_retries": 3}, "default_params": {"temperature": 0.7, "max_tokens": 1000, "top_p": 0.98, "presence_penalty": 0.0, "frequency_penalty": 0.0}, "conversation_settings": {"max_history_length": 20, "save_history": true, "system_prompt": "你是一个友好的AI助手。"}}, "openai_config": {"base_url": "https://hzmeaaogifcw.ap-northeast-1.clawcloudrun.com/v1", "api_key": "qq1230", "model": "[PAY]gemini-2.5-pro-openai", "default_params": {"temperature": 1.15, "max_tokens": 32000, "top_p": 0.98, "presence_penalty": 0.0, "frequency_penalty": 0.0}}, "llm_presets": {"default": {"name": "默认预设", "system_prompt": "你是一个友好的AI助手。", "api_config": {"model": "gpt-3.5-turbo", "temperature": 0.7, "max_tokens": 1000}}, "魔女": {"name": "魔女", "system_prompt": "你是我的ai女儿", "api_config": {"base_url": "https://hzmeaaogifcw.ap-northeast-1.clawcloudrun.com/v1", "model": "[PAY]gemini-2.5-pro-openai", "temperature": 0.7, "max_tokens": 1000}}}, "text_display": {"typing_speed": 50, "auto_hide_delay": 5000, "max_chars_per_line": 20, "max_lines": 3, "typing_animation": false}, "model_positions": {"Hiyori.model3": {"offset_x": 0.0, "offset_y": 0.0}}, "voice_dialogue": {"enabled": true, "microphone_config": {"sample_rate": 16000, "chunk_size": 1024, "channels": 1, "format": "int16", "device_index": null, "auto_select_device": true, "volume_threshold": 0.01, "noise_reduction": true}, "key_triggered_config": {"trigger_key": "space", "hold_to_record": true, "min_recording_duration": 0.5, "max_recording_duration": 30.0, "silence_timeout": 2.0, "auto_stop_on_silence": true}, "realtime_config": {"voice_activity_threshold": 0.02, "silence_duration": 1.5, "min_speech_duration": 0.8, "max_speech_duration": 30.0, "energy_threshold": 300, "dynamic_energy_threshold": true}, "stt_config": {"model_path": "D:/huggingface_cache/hub/models--Systran--faster-whisper-large-v3/snapshots/edaa852ec7e145841d8ffdb056a99866b5f0a478", "device": "auto", "compute_type": "float16", "language": "auto", "task": "transcribe", "beam_size": 5, "temperature": [0.0, 0.2, 0.4, 0.6, 0.8, 1.0], "compression_ratio_threshold": 2.4, "log_prob_threshold": -1.0, "no_speech_threshold": 0.6, "vad_filter": true, "vad_parameters": {"threshold": 0.5, "min_speech_duration_ms": 250, "max_speech_duration_s": 30, "min_silence_duration_ms": 2000, "speech_pad_ms": 400}}, "tts_config": {"api_url": "http://localhost:9880", "default_params": {"text_lang": "zh", "top_k": 20, "top_p": 0.6, "temperature": 0.6, "speed_factor": 1.0, "cut_punc": ",.;?!、，。？！；："}, "audio_settings": {"auto_play": true, "save_audio": false, "output_dir": "output/audio", "format": "wav", "volume": 1.0, "playback_rate": 1.0}, "current_preset": "default", "presets": {"default": {"name": "默认语音", "refer_wav_path": "", "prompt_text": "", "prompt_lang": "zh", "text_lang": "zh", "cut_punc": ",.;?!、，。？！；：", "top_k": 20, "top_p": 0.6, "temperature": 0.6, "speed_factor": 1.0}}}, "ui_settings": {"show_voice_indicator": true, "show_volume_meter": true, "show_status_text": true, "voice_button_position": "bottom_right", "hotkey_enabled": true, "notification_enabled": true, "auto_hide_ui": true, "ui_timeout": 3.0}, "integration_settings": {"llm_integration": true, "auto_send_to_llm": true, "llm_response_tts": true, "context_memory": true, "conversation_history": true, "max_history_length": 10}}}
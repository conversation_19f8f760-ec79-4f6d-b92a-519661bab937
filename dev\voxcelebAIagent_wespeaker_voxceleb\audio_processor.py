import sounddevice as sd
import numpy as np
import threading
import time
from config import Config

class VoiceprintAudioProcessor:
    def __init__(self):
        self.sample_rate = Config.SAMPLE_RATE
        self.duration = Config.AUDIO_DURATION
        self.recording = False
        self.audio_data = None
    
    def record_audio(self):
        """录制音频用于声纹识别"""
        print(f"🎤 开始录制声纹样本 {self.duration} 秒...")
        try:
            self.audio_data = sd.rec(
                int(self.duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=1,
                dtype=np.float32
            )
            sd.wait()  # 等待录音完成
            print("✅ 声纹录制完成")
            return self.audio_data.flatten()
        except Exception as e:
            raise Exception(f"声纹录制失败: {str(e)}")
    
    def record_with_voice_guidance(self):
        """带语音引导的录音"""
        print("🎵 准备录制您的声纹样本...")
        print("💡 请在录音期间自然说话，比如介绍自己或随意聊天")
        
        for i in range(3, 0, -1):
            print(f"⏰ {i}...")
            time.sleep(1)
        
        print("🎤 开始录音！请开始说话...")
        return self.record_audio()
    
    def record_long_sample(self, duration=5):
        """录制较长的声纹样本以提高识别精度"""
        print(f"🎤 录制高质量声纹样本 {duration} 秒...")
        print("💡 请持续说话，内容可以是自我介绍、朗读或随意聊天")
        
        try:
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=1,
                dtype=np.float32
            )
            
            # 实时显示录音进度
            for i in range(duration):
                time.sleep(1)
                print(f"⏳ 录音中... {i+1}/{duration}秒")
            
            sd.wait()
            print("✅ 高质量声纹样本录制完成")
            return audio_data.flatten()
            
        except Exception as e:
            raise Exception(f"长时间录音失败: {str(e)}")
    
    def analyze_audio_quality(self, audio_data):
        """分析音频质量"""
        try:
            # 计算音频统计信息
            rms = np.sqrt(np.mean(audio_data**2))
            peak = np.max(np.abs(audio_data))
            snr_estimate = 20 * np.log10(rms / (np.std(audio_data) + 1e-10))
            
            # 检测静音段
            silence_threshold = 0.01
            silence_ratio = np.sum(np.abs(audio_data) < silence_threshold) / len(audio_data)
            
            quality_info = {
                'rms_level': float(rms),
                'peak_level': float(peak),
                'snr_estimate': float(snr_estimate),
                'silence_ratio': float(silence_ratio),
                'duration': len(audio_data) / self.sample_rate
            }
            
            # 质量评估
            if rms < 0.001:
                quality_info['quality'] = 'very_low'
                quality_info['message'] = '音频信号太弱，请靠近麦克风'
            elif silence_ratio > 0.7:
                quality_info['quality'] = 'low'
                quality_info['message'] = '静音时间过长，请持续说话'
            elif rms > 0.01 and silence_ratio < 0.3:
                quality_info['quality'] = 'good'
                quality_info['message'] = '音频质量良好'
            else:
                quality_info['quality'] = 'medium'
                quality_info['message'] = '音频质量一般，建议重新录制'
            
            return quality_info
            
        except Exception as e:
            return {'quality': 'unknown', 'message': f'质量分析失败: {e}'}
    
    def get_audio_devices(self):
        """获取可用音频设备"""
        try:
            devices = sd.query_devices()
            input_devices = []
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    input_devices.append({
                        'id': i,
                        'name': device['name'],
                        'channels': device['max_input_channels'],
                        'sample_rate': device['default_samplerate']
                    })
            return input_devices
        except Exception as e:
            print(f"获取音频设备失败: {e}")
            return []
    
    def test_microphone(self):
        """测试麦克风并提供反馈"""
        print("🎤 测试麦克风...")
        try:
            # 录制1秒测试音频
            test_audio = sd.rec(
                int(1 * self.sample_rate),
                samplerate=self.sample_rate,
                channels=1,
                dtype=np.float32
            )
            sd.wait()
            
            # 分析测试音频
            quality_info = self.analyze_audio_quality(test_audio.flatten())
            
            print(f"📊 音频质量: {quality_info['quality']}")
            print(f"💡 建议: {quality_info['message']}")
            print(f"📈 音频强度: {quality_info['rms_level']:.4f}")
            print(f"🔇 静音比例: {quality_info['silence_ratio']:.2%}")
            
            return quality_info['quality'] in ['good', 'medium']
            
        except Exception as e:
            print(f"麦克风测试失败: {e}")
            return False
    
    def set_input_device(self, device_id):
        """设置输入设备"""
        try:
            sd.default.device[0] = device_id
            print(f"✅ 已设置输入设备: {device_id}")
        except Exception as e:
            print(f"❌ 设置输入设备失败: {e}")

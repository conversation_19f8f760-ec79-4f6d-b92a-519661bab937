# 快速输入栏优化说明

## 概述
本次优化主要针对快速输入栏进行了功能增强和样式美化，让用户能够清楚地知道当前使用的预设，并可以方便地切换预设。

## 主要改进

### 1. 预设选择功能
- **新增预设下拉框**：在快速输入栏顶部添加了预设选择组件
- **显示当前预设**：用户可以清楚地看到当前使用的是哪个预设
- **预设信息显示**：下拉框中显示预设名称和模型信息（如：默认预设 (gemini-2.5-pro-openai)）
- **实时切换**：用户可以在快速输入时直接切换预设，无需进入设置界面

### 2. 样式优化
- **现代化设计**：使用渐变背景和更美观的色彩方案
- **改进的按钮样式**：添加渐变效果和hover状态
- **更好的布局**：合理安排预设选择、输入框和按钮的位置
- **视觉反馈**：添加阴影效果和更好的边框样式
- **统一的色彩主题**：使用蓝色系作为主色调，灰色作为辅助色

### 3. 功能增强
- **预设切换通知**：切换预设时会显示确认消息
- **配置同步**：预设切换会立即更新LLM客户端配置
- **对话历史清空**：切换预设时自动清空对话历史，使用新的系统提示词
- **错误处理**：添加了完善的错误处理机制

## 技术实现

### 修改的文件
1. **dialogue-system/ui/quick_input.py**
   - 修改 `QuickInputOverlay` 类，添加预设选择功能
   - 新增 `preset_changed` 信号
   - 添加预设加载和切换方法
   - 优化UI样式和布局

2. **dialogue-system/ui/text_overlay.py**
   - 修改 `TextDisplayManager` 类，支持预设切换信号连接
   - 保持与快速输入组件的集成

### 新增方法
- `load_presets()`: 加载预设列表到下拉框
- `set_current_preset()`: 设置当前选中的预设
- `on_preset_changed()`: 处理预设切换事件

### 信号机制
- `preset_changed(str)`: 当预设切换时发射，传递预设ID
- `message_sent(str)`: 当发送消息时发射，传递消息内容

## 使用方法

### 基本使用
```python
from dialogue_system.ui.text_overlay import TextDisplayManager

# 创建文本显示管理器
text_manager = TextDisplayManager(parent_widget)

# 连接信号处理器
text_manager.connect_signals(
    message_handler=handle_message,
    preset_handler=handle_preset_change
)

# 显示快速输入栏
text_manager.show_quick_input()
```

### 信号处理
```python
def handle_message(message: str):
    """处理接收到的消息"""
    print(f"收到消息: {message}")
    # 处理消息逻辑

def handle_preset_change(preset_id: str):
    """处理预设切换"""
    print(f"预设切换到: {preset_id}")
    # 更新LLM配置
```

## 样式特性

### 色彩方案
- **主色调**: #007acc (蓝色)
- **辅助色**: #888 (灰色)
- **背景色**: 渐变白色 (rgba(250,250,250,245) -> rgba(240,240,240,245))
- **边框色**: rgba(120,120,120,180)

### 交互效果
- **悬停效果**: 颜色变深，提供视觉反馈
- **按下效果**: 颜色进一步变深
- **焦点效果**: 边框颜色变为主色调

### 布局特点
- **固定尺寸**: 320x140 像素
- **圆角设计**: 12px 圆角
- **内边距**: 12px
- **组件间距**: 8px

## 兼容性

### 依赖要求
- PySide6
- 配置管理器 (ConfigManager)

### 向后兼容
- 保持原有的 `TextDisplayManager` 接口
- 保持原有的信号机制
- 支持无配置管理器的使用场景

## 测试

### 测试文件
- `dialogue-system/test/test_quick_input.py`: 基本功能测试
- `dialogue-system/test/test_quick_input_complete.py`: 完整功能测试

### 测试方法
```bash
cd dialogue-system/test
python test_quick_input.py
python test_quick_input_complete.py
```

## 未来扩展

### 计划功能
- **快捷键支持**: 添加键盘快捷键
- **历史记录**: 保存输入历史
- **自动完成**: 基于历史的自动完成
- **主题切换**: 支持多种视觉主题

### 性能优化
- **延迟加载**: 预设列表的延迟加载
- **缓存机制**: 预设数据的缓存
- **异步处理**: 预设切换的异步处理

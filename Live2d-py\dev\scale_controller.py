#!/usr/bin/env python3
"""
缩放控制器
负责模型的缩放控制和同步缩放功能
"""

from PySide6.QtCore import QObject, Signal


class ScaleController(QObject):
    """缩放控制器"""
    
    # 定义信号
    scale_changed = Signal(float)
    sync_scale_changed = Signal(bool)
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.manual_scale_factor = 1.0
        self.sync_scale_enabled = True
        self.base_window_width = 400
        self.base_window_height = 500
        self.min_scale = 0.3
        self.max_scale = 3.0
    
    def load_config(self):
        """加载缩放配置"""
        model_config = self.config_manager.config["model"]
        
        self.manual_scale_factor = model_config.get("manual_scale_factor", 1.0)
        self.sync_scale_enabled = model_config.get("sync_scale_enabled", True)
        self.base_window_width = model_config.get("base_window_width", 400)
        self.base_window_height = model_config.get("base_window_height", 500)
        self.min_scale = model_config.get("min_scale", 0.3)
        self.max_scale = model_config.get("max_scale", 3.0)
        
        print(f"缩放配置已加载: 手动={self.manual_scale_factor:.2f}, 同步={self.sync_scale_enabled}")
    
    def calculate_sync_scale(self, window_width: int, window_height: int) -> float:
        """计算同步缩放系数"""
        if not self.sync_scale_enabled:
            return 1.0
        
        # 计算基于窗口大小的缩放系数
        scale_x = window_width / self.base_window_width
        scale_y = window_height / self.base_window_height
        
        # 使用较小的缩放系数以保持比例
        sync_scale = min(scale_x, scale_y)
        
        # 应用缩放范围限制
        sync_scale = max(self.min_scale, min(self.max_scale, sync_scale))
        
        return sync_scale
    
    def calculate_final_scale(self, window_width: int, window_height: int) -> float:
        """计算最终缩放系数"""
        sync_scale = self.calculate_sync_scale(window_width, window_height)
        final_scale = sync_scale * self.manual_scale_factor
        
        # 应用最终缩放范围限制
        final_scale = max(self.min_scale, min(self.max_scale, final_scale))
        
        return final_scale
    
    def set_manual_scale(self, scale: float, window_width: int, window_height: int) -> float:
        """设置手动缩放系数"""
        # 计算同步缩放系数
        sync_scale = self.calculate_sync_scale(window_width, window_height)
        
        # 计算手动缩放的有效范围
        if sync_scale > 0:
            min_manual = self.min_scale / sync_scale
            max_manual = self.max_scale / sync_scale
        else:
            min_manual = self.min_scale
            max_manual = self.max_scale
        
        # 应用范围限制
        self.manual_scale_factor = max(min_manual, min(max_manual, scale))
        
        # 保存到配置
        self.config_manager.set("model", "manual_scale_factor", self.manual_scale_factor)
        
        # 计算最终缩放
        final_scale = self.calculate_final_scale(window_width, window_height)
        
        self.scale_changed.emit(final_scale)
        print(f"手动缩放设置为: {self.manual_scale_factor:.2f}, 最终缩放: {final_scale:.2f}")
        
        return final_scale
    
    def adjust_scale(self, factor: float, window_width: int, window_height: int) -> float:
        """调整缩放（相对调整）"""
        new_scale = self.manual_scale_factor * factor
        return self.set_manual_scale(new_scale, window_width, window_height)
    
    def reset_scale(self, window_width: int, window_height: int) -> float:
        """重置缩放到1.0"""
        return self.set_manual_scale(1.0, window_width, window_height)
    
    def handle_window_resize(self, window_width: int, window_height: int) -> float:
        """处理窗口大小变化"""
        if not self.sync_scale_enabled:
            return self.manual_scale_factor
        
        # 重新计算缩放
        final_scale = self.calculate_final_scale(window_width, window_height)
        self.scale_changed.emit(final_scale)
        
        print(f"窗口大小变更: {window_width}x{window_height}, 缩放更新为: {final_scale:.2f}")
        return final_scale
    
    def handle_wheel_event(self, delta: int, window_width: int, window_height: int) -> float:
        """处理滚轮缩放事件"""
        scale_factor = 1.1 if delta > 0 else 0.9
        return self.adjust_scale(scale_factor, window_width, window_height)
    
    def handle_key_scale(self, key_type: str, window_width: int, window_height: int) -> float:
        """处理键盘缩放事件"""
        if key_type == "plus":
            return self.adjust_scale(1.1, window_width, window_height)
        elif key_type == "minus":
            return self.adjust_scale(0.9, window_width, window_height)
        elif key_type == "reset":
            return self.reset_scale(window_width, window_height)
        
        return self.calculate_final_scale(window_width, window_height)
    
    def set_sync_scale_enabled(self, enabled: bool, window_width: int, window_height: int) -> float:
        """设置同步缩放开关"""
        self.sync_scale_enabled = enabled
        self.config_manager.set("model", "sync_scale_enabled", enabled)
        
        # 重新计算缩放
        final_scale = self.calculate_final_scale(window_width, window_height)
        self.scale_changed.emit(final_scale)
        self.sync_scale_changed.emit(enabled)
        
        print(f"同步缩放: {'开启' if enabled else '关闭'}, 当前缩放: {final_scale:.2f}")
        return final_scale
    
    def set_base_size(self, width: int, height: int, window_width: int, window_height: int) -> float:
        """设置基准窗口大小"""
        self.base_window_width = width
        self.base_window_height = height
        
        self.config_manager.set("model", "base_window_width", width)
        self.config_manager.set("model", "base_window_height", height)
        
        # 重新计算缩放
        final_scale = self.calculate_final_scale(window_width, window_height)
        self.scale_changed.emit(final_scale)
        
        print(f"基准尺寸设置为: {width}x{height}, 当前缩放: {final_scale:.2f}")
        return final_scale
    
    def set_scale_range(self, min_scale: float, max_scale: float, window_width: int, window_height: int) -> float:
        """设置缩放范围"""
        self.min_scale = min_scale
        self.max_scale = max_scale
        
        self.config_manager.set("model", "min_scale", min_scale)
        self.config_manager.set("model", "max_scale", max_scale)
        
        # 重新应用当前缩放（可能会被新范围限制）
        final_scale = self.calculate_final_scale(window_width, window_height)
        self.scale_changed.emit(final_scale)
        
        print(f"缩放范围设置为: {min_scale:.1f} - {max_scale:.1f}, 当前缩放: {final_scale:.2f}")
        return final_scale
    
    def get_scale_info(self, window_width: int, window_height: int) -> dict:
        """获取缩放信息"""
        sync_scale = self.calculate_sync_scale(window_width, window_height)
        final_scale = self.calculate_final_scale(window_width, window_height)
        
        return {
            "manual_scale": self.manual_scale_factor,
            "sync_scale": sync_scale,
            "final_scale": final_scale,
            "sync_enabled": self.sync_scale_enabled,
            "base_size": (self.base_window_width, self.base_window_height),
            "scale_range": (self.min_scale, self.max_scale)
        }

#!/usr/bin/env python3
"""简约高效的模型列表获取示例程序"""

import requests
import json

# 配置
LLM_BASE_URL = "https://api.studio.nebius.ai/v1"

def get_api_key():
    """从文件读取API密钥"""
    with open("api.txt", "r") as f:
        return f.read().strip()

def list_models():
    """获取可用模型列表"""
    headers = {
        "Authorization": f"Bearer {get_api_key()}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{LLM_BASE_URL}/models", headers=headers)
        response.raise_for_status()
        
        models = response.json()
        print(f"找到 {len(models.get('data', []))} 个可用模型:")
        
        for model in models.get('data', []):
            print(f"- {model['id']}")
            
        return models
        
    except requests.RequestException as e:
        print(f"请求失败: {e}")
        return None

if __name__ == "__main__":
    list_models()

import requests
import asyncio
from datetime import datetime
from config import Config

class WeatherService:
    """天气服务（整合自原weatherAIagent）"""
    def __init__(self):
        self.api_key = Config.WEATHER_API_KEY
        self.base_url = Config.WEATHER_BASE_URL
        
    def get_current_weather(self, city):
        """获取当前天气"""
        url = f"{self.base_url}/weather"
        params = {
            "q": city,
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn"
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            # 格式化返回结果
            return {
                "city": city,
                "temperature": f"{data['main']['temp']:.1f}°C",
                "weather": data['weather'][0]['description'],
                "humidity": f"{data['main']['humidity']}%",
                "wind": f"{data['wind']['speed']} m/s",
                "description": f"{city}现在{data['weather'][0]['description']}，温度{data['main']['temp']:.1f}度，湿度{data['main']['humidity']}%呢！"
            }
            
        except requests.RequestException as e:
            raise Exception(f"天气查询失败: {str(e)}")
    
    def get_weather_forecast(self, city, days=3):
        """获取天气预报"""
        url = f"{self.base_url}/forecast"
        params = {
            "q": city,
            "appid": self.api_key,
            "units": "metric",
            "lang": "zh_cn",
            "cnt": days * 8  # 每天8个时间点
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            # 处理预报数据
            forecast_list = []
            current_date = None
            daily_data = {}
            
            for item in data['list'][:days*8]:
                date = datetime.fromtimestamp(item['dt']).strftime('%Y-%m-%d')
                if date != current_date:
                    if current_date and daily_data:
                        forecast_list.append(daily_data)
                    current_date = date
                    daily_data = {
                        "date": date,
                        "weather": item['weather'][0]['description'],
                        "temp_min": item['main']['temp_min'],
                        "temp_max": item['main']['temp_max']
                    }
                else:
                    # 更新当日最高最低温度
                    daily_data['temp_min'] = min(daily_data['temp_min'], item['main']['temp_min'])
                    daily_data['temp_max'] = max(daily_data['temp_max'], item['main']['temp_max'])
            
            if daily_data:
                forecast_list.append(daily_data)
            
            # 格式化结果
            forecast_text = f"{city}未来{len(forecast_list)}天的天气预报：\n"
            for i, day in enumerate(forecast_list):
                day_name = ["今天", "明天", "后天"][i] if i < 3 else f"{i+1}天后"
                forecast_text += f"{day_name}: {day['weather']}, {day['temp_min']:.1f}°C - {day['temp_max']:.1f}°C\n"
            
            return {
                "city": city,
                "days": len(forecast_list),
                "forecast": forecast_list,
                "description": forecast_text.strip()
            }
            
        except requests.RequestException as e:
            raise Exception(f"天气预报查询失败: {str(e)}")

class TimeService:
    """时间服务（整合自原systemtimeAIagent）"""
    def __init__(self):
        pass
        
    def get_current_time(self, format_type="detailed"):
        """获取当前时间"""
        now = datetime.now()
        
        if format_type == "simple":
            return {
                "time": now.strftime("%H:%M"),
                "description": f"现在是{now.strftime('%H:%M')}哦！"
            }
        elif format_type == "date":
            return {
                "date": now.strftime("%Y-%m-%d"),
                "weekday": now.strftime("%A"),
                "description": f"今天是{now.strftime('%Y年%m月%d日')}，{self._get_weekday_chinese(now.strftime('%A'))}呢！"
            }
        else:  # detailed
            return {
                "time": now.strftime("%Y-%m-%d %H:%M:%S"),
                "weekday": now.strftime("%A"),
                "description": f"现在是{now.strftime('%Y年%m月%d日 %H:%M:%S')}，{self._get_weekday_chinese(now.strftime('%A'))}呢！"
            }
    
    def get_time_info(self, info_type="period"):
        """获取时间相关信息"""
        now = datetime.now()
        hour = now.hour
        
        if info_type == "period":
            if 5 <= hour < 12:
                period = "早上"
                mood = "新的一天开始啦！"
            elif 12 <= hour < 14:
                period = "中午"
                mood = "该吃午饭了呢～"
            elif 14 <= hour < 18:
                period = "下午"
                mood = "下午时光真美好！"
            elif 18 <= hour < 22:
                period = "晚上"
                mood = "晚上好呀～"
            else:
                period = "深夜"
                mood = "这么晚了，要注意休息哦！"
            
            return {
                "period": period,
                "hour": hour,
                "description": f"现在是{period}呢！{mood}"
            }
        
        elif info_type == "season":
            month = now.month
            if month in [12, 1, 2]:
                season = "冬天"
            elif month in [3, 4, 5]:
                season = "春天"
            elif month in [6, 7, 8]:
                season = "夏天"
            else:
                season = "秋天"
            
            return {
                "season": season,
                "month": month,
                "description": f"现在是{season}呢！{month}月的{season}特别美～"
            }
        
        else:
            return self.get_current_time("detailed")
    
    def _get_weekday_chinese(self, weekday_english):
        """将英文星期转换为中文"""
        weekday_map = {
            "Monday": "星期一",
            "Tuesday": "星期二", 
            "Wednesday": "星期三",
            "Thursday": "星期四",
            "Friday": "星期五",
            "Saturday": "星期六",
            "Sunday": "星期日"
        }
        return weekday_map.get(weekday_english, weekday_english)

class ServiceIntegrator:
    """服务整合器，统一管理所有服务"""
    def __init__(self):
        self.weather_service = WeatherService()
        self.time_service = TimeService()
    
    async def execute_weather_query(self, city: str):
        """异步执行天气查询"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.weather_service.get_current_weather, city)
    
    async def execute_forecast_query(self, city: str, days: int = 3):
        """异步执行天气预报查询"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.weather_service.get_weather_forecast, city, days)
    
    async def execute_time_query(self, format_type: str = "detailed"):
        """异步执行时间查询"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.time_service.get_current_time, format_type)
    
    async def execute_time_info_query(self, info_type: str = "period"):
        """异步执行时间信息查询"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.time_service.get_time_info, info_type)

# 函数定义，用于LLM函数调用
WEATHER_FUNCTIONS = [
    {
        "name": "get_current_weather",
        "description": "获取指定城市的当前天气信息",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "城市名称，例如：北京、上海、广州"
                }
            },
            "required": ["city"]
        }
    },
    {
        "name": "get_weather_forecast",
        "description": "获取指定城市的天气预报",
        "parameters": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string",
                    "description": "城市名称，例如：北京、上海、广州"
                },
                "days": {
                    "type": "integer",
                    "description": "预报天数，默认3天",
                    "minimum": 1,
                    "maximum": 5
                }
            },
            "required": ["city"]
        }
    }
]

TIME_FUNCTIONS = [
    {
        "name": "get_current_time",
        "description": "获取当前时间信息",
        "parameters": {
            "type": "object",
            "properties": {
                "format_type": {
                    "type": "string",
                    "enum": ["simple", "detailed", "date"],
                    "description": "时间格式类型：simple(仅时间)、detailed(详细)、date(仅日期)"
                }
            }
        }
    },
    {
        "name": "get_time_info",
        "description": "获取时间相关信息",
        "parameters": {
            "type": "object",
            "properties": {
                "info_type": {
                    "type": "string",
                    "enum": ["period", "season"],
                    "description": "信息类型：period(时段)、season(季节)"
                }
            }
        }
    }
]

# 合并所有函数
ALL_FUNCTIONS = WEATHER_FUNCTIONS + TIME_FUNCTIONS

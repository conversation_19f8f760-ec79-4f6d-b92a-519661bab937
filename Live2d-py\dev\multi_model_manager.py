#!/usr/bin/env python3
"""
多模型管理器 - 管理多个独立的Live2D模型窗口
"""

import os
import sys
import ctypes
from typing import List, Dict

from PySide6.QtCore import QObject, Signal, QPoint, Qt
from PySide6.QtOpenGLWidgets import QOpenGLWidget

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import live2d.v3 as live2d

# Windows API 常量和函数
GWL_EXSTYLE = -20
WS_EX_LAYERED = 0x80000
LWA_COLORKEY = 0x1
user32 = ctypes.windll.user32

def make_window_transparent_colorkey(hwnd):
    """使用 Windows API 颜色键设置窗口透明"""
    try:
        ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        new_ex_style = ex_style | WS_EX_LAYERED
        user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)
        user32.SetLayeredWindowAttributes(hwnd, 0x000000, 255, LWA_COLORKEY)
        return True
    except Exception as e:
        print(f"✗ Windows API ColorKey transparency failed: {e}")
        return False


class ModelWindow(QOpenGLWidget):
    """独立的模型窗口"""
    
    # 窗口关闭信号
    window_closed = Signal(str)  # 发送模型路径
    
    def __init__(self, model_path: str, window_offset: QPoint = QPoint(0, 0), share_context=None):
        super().__init__()
        self.model_path = model_path
        self.window_offset = window_offset
        self.model = None
        self.gl_initialized = False  # 标记OpenGL是否已初始化

        # 设置窗口属性
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.resize(400, 500)

        # 设置窗口标题（用于调试）
        model_name = os.path.splitext(os.path.basename(model_path))[0]
        self.setWindowTitle(f"Live2D - {model_name}")

        # 使用默认的OpenGL格式（与主窗口保持一致）
        # 主程序已经设置了QSurfaceFormat.setDefaultFormat()
        # 这里不需要重复设置，直接使用默认格式即可
        print(f"多模型窗口使用默认OpenGL格式")

        # 应用窗口偏移
        if window_offset.x() != 0 or window_offset.y() != 0:
            current_pos = self.pos()
            self.move(current_pos + window_offset)

        print(f"创建模型窗口: {model_name} at {self.pos()}")
    
    def initializeGL(self) -> None:
        """初始化OpenGL和模型"""
        if self.gl_initialized:
            print(f"OpenGL已初始化，跳过: {os.path.basename(self.model_path)}")
            return

        try:
            print(f"开始初始化多模型窗口OpenGL: {os.path.basename(self.model_path)}")

            # 将当前窗口作为 OpenGL 的上下文
            self.makeCurrent()

            # 获取OpenGL信息
            try:
                import OpenGL.GL as gl
                version = gl.glGetString(gl.GL_VERSION)
                renderer = gl.glGetString(gl.GL_RENDERER)
                print(f"多模型窗口 OpenGL Version: {version}")
                print(f"多模型窗口 OpenGL Renderer: {renderer}")
            except Exception as e:
                print(f"Failed to get OpenGL info: {e}")

            # Live2D已经在主程序中初始化，这里不需要重复初始化
            # 只需要确保OpenGL上下文正确
            print("多模型窗口 使用已初始化的Live2D")

            # 设置OpenGL状态（与主窗口保持一致）
            self.setup_opengl_state()

            # 创建模型
            try:
                self.model = live2d.LAppModel()
                print("多模型窗口 Live2D model object created successfully")
            except Exception as e:
                print(f"多模型窗口 Failed to create Live2D model: {e}")
                return

            # 加载指定的模型
            if os.path.exists(self.model_path):
                print(f"开始加载模型: {self.model_path}")

                # 检查模型文件和相关文件
                model_dir = os.path.dirname(self.model_path)
                print(f"模型目录: {model_dir}")

                # 检查纹理文件
                texture_files = []
                for file in os.listdir(model_dir):
                    if file.endswith('.png'):
                        texture_files.append(file)
                print(f"找到纹理文件: {texture_files}")

                # 加载模型JSON
                print(f"绝对模型路径: {os.path.abspath(self.model_path)}")

                try:
                    self.model.LoadModelJson(self.model_path)
                    print(f"✓ 模型JSON加载完成: {self.model_path}")
                except Exception as e:
                    print(f"✗ 模型JSON加载失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return

                # 检查模型是否有效加载
                print(f"模型对象状态: {self.model is not None}")
                if hasattr(self.model, 'IsValid') and callable(getattr(self.model, 'IsValid')):
                    is_valid = self.model.IsValid()
                    print(f"模型有效性检查: {is_valid}")
                    if not is_valid:
                        print(f"✗ 模型加载失败，模型无效: {self.model_path}")
                        return

                # 强制刷新OpenGL状态，确保纹理正确绑定
                print("强制刷新OpenGL状态...")
                self.makeCurrent()

                # 等待一小段时间确保纹理加载完成
                import time
                time.sleep(0.2)

                # 调整模型大小以适应当前窗口
                self.model.Resize(self.width(), self.height())
                print(f"模型大小调整为: {self.width()}x{self.height()}")

                # 尝试重新加载渲染器以确保纹理正确绑定
                print("重新初始化渲染器...")
                try:
                    # 这可能有助于解决纹理绑定问题
                    if hasattr(self.model, 'ReloadRenderer'):
                        self.model.ReloadRenderer()
                        print("✓ 渲染器重新加载成功")
                    else:
                        print("模型不支持ReloadRenderer方法")
                except Exception as e:
                    print(f"渲染器重新加载失败: {e}")

                # 强制更新一次以确保渲染正常
                print("强制更新模型状态...")
                try:
                    self.model.Update()
                    print("✓ 模型状态更新成功")
                except Exception as e:
                    print(f"✗ 模型状态更新失败: {e}")

                print(f"✓ 模型完整加载流程完成")

            else:
                print(f"✗ 模型文件不存在: {self.model_path}")
                return

            # 设置模型缩放
            self.model.SetScale(1.0)

            # 以 fps = 60 的频率进行绘图
            self.startTimer(int(1000 / 60))

            # 标记初始化完成
            self.gl_initialized = True
            print(f"✓ 模型窗口初始化完成: {os.path.basename(self.model_path)}")

        except Exception as e:
            print(f"✗ 模型窗口初始化失败: {e}")
            import traceback
            traceback.print_exc()

    def setup_opengl_state(self):
        """设置OpenGL状态（与主窗口保持一致）"""
        import OpenGL.GL as gl

        # 启用混合
        gl.glEnable(gl.GL_BLEND)
        gl.glBlendFunc(gl.GL_SRC_ALPHA, gl.GL_ONE_MINUS_SRC_ALPHA)

        # 禁用深度测试（2D渲染）
        gl.glDisable(gl.GL_DEPTH_TEST)

        # 禁用抗锯齿相关设置，减少黑边（保持与原版本一致）
        gl.glDisable(gl.GL_MULTISAMPLE)
        gl.glHint(gl.GL_LINE_SMOOTH_HINT, gl.GL_NICEST)
        gl.glHint(gl.GL_POLYGON_SMOOTH_HINT, gl.GL_NICEST)

        print("多模型窗口 OpenGL状态已配置")
    
    def resizeGL(self, w: int, h: int) -> None:
        """窗口大小改变时调用"""
        if self.model:
            self.model.Resize(w, h)

    def paintGL(self) -> None:
        """绘制模型（与主窗口保持一致的透明处理）"""
        # 确保OpenGL上下文是当前的
        self.makeCurrent()

        # 使用透明背景（多模型窗口默认透明）
        live2d.clearBuffer(0.0, 0.0, 0.0, 0.0)

        # 更新和绘制模型
        if self.model:
            try:
                self.model.Update()
                self.model.Draw()
            except Exception as e:
                print(f"多模型窗口渲染错误: {e}")
                import traceback
                traceback.print_exc()

    def timerEvent(self, event) -> None:
        """定时器事件"""
        # 忽略未使用的参数
        _ = event
        if self.model:
            # 简单的鼠标跟随效果
            cursor_pos = self.mapFromGlobal(self.cursor().pos())
            x, y = cursor_pos.x(), cursor_pos.y()
            self.model.Drag(x, y)

        self.update()

    def mousePressEvent(self, event):
        """鼠标按下事件 - 支持拖拽窗口"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_start_position = event.globalPosition().toPoint()

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 拖拽窗口"""
        if hasattr(self, 'drag_start_position') and event.buttons() == Qt.MouseButton.LeftButton:
            delta = event.globalPosition().toPoint() - self.drag_start_position
            self.move(self.pos() + delta)
            self.drag_start_position = event.globalPosition().toPoint()

    def showEvent(self, event):
        """窗口显示事件 - 应用透明效果并确保OpenGL初始化"""
        super().showEvent(event)

        # 延迟应用透明效果，确保窗口句柄可用
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        try:
            hwnd = int(self.winId())
            make_window_transparent_colorkey(hwnd)
            print(f"多模型窗口透明效果已应用: {os.path.basename(self.model_path)}")
        except Exception as e:
            print(f"应用透明效果失败: {e}")

        # 确保OpenGL已初始化（如果还没有的话）
        if not self.gl_initialized:
            # 使用QTimer延迟初始化，确保窗口完全显示
            from PySide6.QtCore import QTimer
            QTimer.singleShot(100, self.delayed_gl_init)

    def delayed_gl_init(self):
        """延迟的OpenGL初始化"""
        if not self.gl_initialized:
            print(f"延迟初始化OpenGL: {os.path.basename(self.model_path)}")
            # 强制触发initializeGL
            self.makeCurrent()
            # 直接调用initializeGL而不是依赖自动触发
            self.initializeGL()
            self.update()

    def closeEvent(self, event):
        """窗口关闭事件"""
        print(f"关闭模型窗口: {os.path.basename(self.model_path)}")

        # 发送窗口关闭信号
        self.window_closed.emit(self.model_path)

        # 清理模型资源
        if hasattr(self, 'model') and self.model:
            del self.model
            self.model = None

        super().closeEvent(event)


class MultiModelManager(QObject):
    """多模型管理器"""
    
    # 信号定义
    model_loaded = Signal(str)      # 模型加载完成
    model_closed = Signal(str)      # 模型窗口关闭
    all_models_closed = Signal()    # 所有模型窗口关闭
    
    def __init__(self, config_manager):
        super().__init__()
        self.config_manager = config_manager
        self.model_windows: Dict[str, ModelWindow] = {}  # 模型路径 -> 窗口对象
        self.window_offset_step = 50  # 窗口偏移步长
    
    def load_models(self, model_paths: List[str]):
        """加载多个模型"""
        print(f"开始加载 {len(model_paths)} 个模型...")
        
        for i, model_path in enumerate(model_paths):
            if model_path in self.model_windows:
                print(f"模型已加载，跳过: {os.path.basename(model_path)}")
                continue
            
            try:
                # 计算窗口偏移，避免窗口重叠
                offset = QPoint(i * self.window_offset_step, i * self.window_offset_step)
                
                # 创建模型窗口
                window = ModelWindow(model_path, offset)
                
                # 连接窗口关闭信号
                window.window_closed.connect(self.on_model_window_closed)
                
                # 显示窗口
                window.show()
                
                # 保存窗口引用
                self.model_windows[model_path] = window
                
                # 发送加载完成信号
                self.model_loaded.emit(model_path)
                
                print(f"✓ 模型窗口创建成功: {os.path.basename(model_path)}")
                
            except Exception as e:
                print(f"✗ 创建模型窗口失败 {os.path.basename(model_path)}: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"多模型加载完成，当前活动窗口数: {len(self.model_windows)}")
    
    def close_all_models(self):
        """关闭所有模型窗口"""
        print("关闭所有模型窗口...")
        
        # 复制窗口列表，避免在迭代时修改字典
        windows_to_close = list(self.model_windows.values())
        
        for window in windows_to_close:
            try:
                window.close()
            except Exception as e:
                print(f"关闭窗口时出错: {e}")
        
        # 清空窗口字典
        self.model_windows.clear()
        
        # 发送所有窗口关闭信号
        self.all_models_closed.emit()
        
        print("所有模型窗口已关闭")
    
    def close_model(self, model_path: str):
        """关闭指定模型窗口"""
        if model_path in self.model_windows:
            try:
                window = self.model_windows[model_path]
                window.close()
                print(f"✓ 关闭模型窗口: {os.path.basename(model_path)}")
            except Exception as e:
                print(f"✗ 关闭模型窗口失败: {e}")
        else:
            print(f"模型窗口不存在: {os.path.basename(model_path)}")
    
    def on_model_window_closed(self, model_path: str):
        """模型窗口关闭回调"""
        if model_path in self.model_windows:
            del self.model_windows[model_path]
            print(f"模型窗口已从管理器中移除: {os.path.basename(model_path)}")
        
        # 发送模型关闭信号
        self.model_closed.emit(model_path)
        
        # 如果所有窗口都关闭了，发送信号
        if not self.model_windows:
            self.all_models_closed.emit()
    
    def get_active_models(self) -> List[str]:
        """获取当前活动的模型列表"""
        return list(self.model_windows.keys())
    
    def get_window_count(self) -> int:
        """获取当前窗口数量"""
        return len(self.model_windows)
    
    def is_model_loaded(self, model_path: str) -> bool:
        """检查模型是否已加载"""
        return model_path in self.model_windows

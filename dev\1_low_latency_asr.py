#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
低延迟语音输入示例程序
使用SenseVoice模型和WebRTCVAD进行实时语音识别
"""

import pyaudio
import webrtcvad
import numpy as np
import threading
import queue
import time
from collections import deque
from funasr import AutoModel


class LowLatencyASR:
    def __init__(self, model_path="D:/huggingface_cache/hub/models--SenseVoice", 
                 sample_rate=16000, frame_duration=30):
        """
        初始化低延迟ASR系统
        
        Args:
            model_path: SenseVoice模型路径
            sample_rate: 采样率，必须是8000, 16000, 32000, 48000之一
            frame_duration: VAD帧长度(ms)，可选10, 20, 30
        """
        self.sample_rate = sample_rate
        self.frame_duration = frame_duration
        self.frame_size = int(sample_rate * frame_duration / 1000)
        self.chunk_size = self.frame_size
        
        # 初始化WebRTC VAD
        self.vad = webrtcvad.Vad(0)  # 敏感度: 0-3, 3最敏感
        
        # 初始化SenseVoice模型
        print("正在加载SenseVoice模型...")
        self.model = AutoModel(
            model=model_path,
            trust_remote_code=True,
            device="cuda:0" if self._check_cuda() else "cpu",
        )
        print("模型加载完成")
        
        # 音频缓冲区
        self.audio_buffer = deque(maxlen=int(sample_rate * 3))  # 3秒缓冲
        self.speech_frames = []
        self.silence_count = 0
        self.speech_count = 0
        
        # 线程控制
        self.is_recording = False
        self.audio_queue = queue.Queue()
        
        # 初始化PyAudio
        self.audio = pyaudio.PyAudio()
        
    def _check_cuda(self):
        """检查CUDA是否可用"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """音频输入回调函数"""
        if self.is_recording:
            self.audio_queue.put(in_data)
        return (None, pyaudio.paContinue)
    
    def _process_audio_frame(self, frame):
        """处理单个音频帧"""
        # 转换为int16格式用于VAD
        audio_int16 = np.frombuffer(frame, dtype=np.int16)
        
        # VAD检测
        is_speech = self.vad.is_speech(frame, self.sample_rate)
        
        if is_speech:
            self.speech_count += 1
            self.silence_count = 0
            self.speech_frames.append(audio_int16)
        else:
            self.silence_count += 1
            
            # 如果有语音数据且静音超过阈值，进行识别
            if self.speech_frames and self.silence_count > 10:  # 300ms静音
                self._recognize_speech()
                self.speech_frames = []
                self.speech_count = 0
    
    def _recognize_speech(self):
        """识别语音"""
        if not self.speech_frames:
            return
            
        # 合并语音帧
        audio_data = np.concatenate(self.speech_frames)
        
        # 最小长度检查
        if len(audio_data) < self.sample_rate * 0.5:  # 至少0.5秒
            return
            
        try:
            # 转换为float32格式
            audio_float = audio_data.astype(np.float32) / 32768.0
            
            # 使用SenseVoice识别
            res = self.model.generate(
                input=audio_float,
                cache={},
                language="auto",
                use_itn=True,
                batch_size_s=60,
            )
            
            if res and len(res) > 0:
                text = res[0]["text"]
                if text.strip():
                    print(f"识别结果: {text}")
                    
        except Exception as e:
            print(f"识别错误: {e}")
    
    def _audio_processing_thread(self):
        """音频处理线程"""
        while self.is_recording:
            try:
                # 获取音频数据，超时1秒
                frame = self.audio_queue.get(timeout=1.0)
                self._process_audio_frame(frame)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"音频处理错误: {e}")
    
    def start_recording(self):
        """开始录音和识别"""
        if self.is_recording:
            print("已在录音中...")
            return
            
        print("开始录音，说话时会自动识别...")
        print("按Ctrl+C停止录音")
        
        self.is_recording = True
        
        # 启动音频处理线程
        self.processing_thread = threading.Thread(target=self._audio_processing_thread)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        # 启动音频流
        self.stream = self.audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size,
            stream_callback=self._audio_callback
        )
        
        self.stream.start_stream()
        
        try:
            while self.is_recording:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.stop_recording()
    
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return
            
        print("\n停止录音...")
        self.is_recording = False
        
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        
        # 处理剩余的语音数据
        if self.speech_frames:
            self._recognize_speech()
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'audio'):
            self.audio.terminate()


def main():
    """主函数"""
    try:
        # 创建ASR实例
        asr = LowLatencyASR()
        
        # 开始录音识别
        asr.start_recording()
        
    except Exception as e:
        print(f"程序错误: {e}")
    finally:
        print("程序结束")


if __name__ == "__main__":
    main()

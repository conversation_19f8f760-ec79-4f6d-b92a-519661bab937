import os
import sys
import ctypes
from ctypes import wintypes
import random
import time

import OpenGL.GL as gl
import numpy as np
from PySide6.QtCore import QTimerEvent, Qt, QPoint, QTimer, Signal, QObject
from PySide6.QtGui import QMouseEvent, QCursor, QKeyEvent, QSurfaceFormat, QAction
from PySide6.QtOpenGLWidgets import QOpenGLWidget
from PySide6.QtWidgets import QApplication, QMenu
from PySide6.QtGui import QGuiApplication

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import live2d.v3 as live2d
import examples.resources as resources

# 导入设置模块
from settings_dialog import ConfigManager, NewSettingsDialog
from model_controller import ModelController
from window_controller import WindowController
from scale_controller import ScaleController
from multi_model_manager import MultiModelManager

# 导入对话相关模块
from dialogue_system import <PERSON>t<PERSON>ialog, TextDisplayManager, LLMClient, ChatResponseHandler


# Windows API 常量和函数
GWL_EXSTYLE = -20
WS_EX_LAYERED = 0x80000
LWA_COLORKEY = 0x1
LWA_ALPHA = 0x2
user32 = ctypes.windll.user32

# 透明模式枚举 - 简洁高效
class TransparencyMode:
    QT_NATIVE = "qt_native"                        # Qt原生透明（推荐）
    WINDOWS_API_COLORKEY = "windows_api_colorkey"  # Windows API 颜色键透明
    WINDOWS_API_ALPHA = "windows_api_alpha"        # Windows API Alpha透明

def make_window_transparent_colorkey(hwnd):
    """使用 Windows API 颜色键设置窗口透明（原方法）"""
    try:
        ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        new_ex_style = ex_style | WS_EX_LAYERED
        user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)
        # 使用更精确的黑色值，减少黑边问题
        user32.SetLayeredWindowAttributes(hwnd, 0x000000, 255, LWA_COLORKEY)
        print("✓ Windows API ColorKey transparency applied")
        return True
    except Exception as e:
        print(f"✗ Windows API ColorKey transparency failed: {e}")
        return False

def make_window_transparent_alpha(hwnd, alpha=240):
    """使用 Windows API Alpha通道设置窗口透明"""
    try:
        ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        new_ex_style = ex_style | WS_EX_LAYERED
        user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)
        # 使用Alpha透明度
        user32.SetLayeredWindowAttributes(hwnd, 0, alpha, LWA_ALPHA)
        print(f"✓ Windows API Alpha transparency applied (alpha={alpha})")
        return True
    except Exception as e:
        print(f"✗ Windows API Alpha transparency failed: {e}")
        return False

def make_window_opaque(hwnd):
    """移除窗口透明效果，显示正常窗口"""
    try:
        ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        new_ex_style = ex_style & ~WS_EX_LAYERED  # 移除分层窗口样式
        user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)
        print("✓ Window transparency removed - now opaque")
        return True
    except Exception as e:
        print(f"✗ Failed to remove transparency: {e}")
        return False



def motion_finished_callback():
    """动作结束回调"""
    print("Motion finished")





class TransparentWindow(QOpenGLWidget):
    """无边框透明窗口 Live2D 显示"""

    def __init__(self) -> None:
        super().__init__()
        self.isInLA = False
        self.clickInLA = False
        self.a = 0
        self.clickX = -1
        self.clickY = -1
        self.model: live2d.LAppModel | None = None
        self.systemScale = QGuiApplication.primaryScreen().devicePixelRatio()

        # 配置管理器
        self.config_manager = ConfigManager()

        # 多模型管理器
        self.multi_model_manager = MultiModelManager(self.config_manager)

        # 设置对话框
        self.settings_dialog = None

        # 对话相关组件
        self.chat_dialog = None
        self.text_display_manager = None
        self.llm_client = None

        # 随机动作相关变量
        self.last_random_motion_time = time.time()
        self.random_motion_interval = random.uniform(5.0, 10.0)  # 将根据配置更新
        self.last_random_expression_time = time.time()
        self.random_expression_interval = random.uniform(5.0, 10.0)  # 将根据配置更新

        # 缩放功能相关变量
        self.is_resizing = False
        self.resize_edge = None  # 'right', 'bottom', 'corner'
        self.resize_start_pos = QPoint()
        self.resize_start_size = None
        self.current_scale = 1.0
        self.border_width = 10  # 边框检测宽度

        # 模型拖拽相关变量
        self.is_dragging_model = False
        self.model_drag_start_pos = QPoint()
        self.current_model_offset_x = 0.0
        self.current_model_offset_y = 0.0

        # 透明模式切换
        self.is_transparent = True
        self.transparent_hwnd = None
        self.transparency_mode = TransparencyMode.QT_NATIVE  # 默认使用Qt原生透明

        # 位置固定功能
        self.position_locked = False

        # 使用兼容的 OpenGL 格式设置，确保Live2D正常工作
        format = QSurfaceFormat()
        format.setAlphaBufferSize(8)
        format.setDepthBufferSize(24)
        format.setStencilBufferSize(8)
        format.setSamples(0)  # 禁用多重采样抗锯齿
        format.setVersion(3, 3)  # 使用OpenGL 3.3获得更好的透明支持
        format.setProfile(QSurfaceFormat.OpenGLContextProfile.CompatibilityProfile)  # 使用兼容模式确保Live2D正常工作
        format.setSwapBehavior(QSurfaceFormat.SwapBehavior.DoubleBuffer)
        format.setSwapInterval(1)  # 启用垂直同步
        self.setFormat(format)

        # 应用配置
        self.apply_config()

        print("Transparent window initialized with Windows API method, anti-aliasing disabled, and scaling support")

    def apply_config(self):
        """应用配置设置"""
        config = self.config_manager.config

        # 应用窗口设置
        window_config = config["window"]

        # 更新透明设置
        self.is_transparent = window_config["transparent"]
        self.transparency_mode = window_config.get("transparency_mode", TransparencyMode.QT_NATIVE)

        # 设置窗口标志 - 透明时无边框，不透明时有标题栏
        flags = Qt.WindowType.FramelessWindowHint if self.is_transparent else Qt.WindowType.Window
        if window_config["always_on_top"]:
            flags |= Qt.WindowType.WindowStaysOnTopHint
        self.setWindowFlags(flags)

        # 初始化时设置Qt原生透明属性
        if self.is_transparent and self.transparency_mode == TransparencyMode.QT_NATIVE:
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
            print("✅ 初始化时启用Qt原生透明属性")
        else:
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)

        # 设置窗口大小和位置
        self.resize(window_config["width"], window_config["height"])
        # 安全地设置窗口位置，如果没有x,y配置则使用默认位置
        x = window_config.get("x", 100)
        y = window_config.get("y", 100)
        self.move(x, y)

        # 设置位置固定
        self.position_locked = window_config.get("position_locked", False)

        # 应用模型设置
        model_config = config["model"]
        # 兼容旧配置
        self.manual_scale_factor = model_config.get("manual_scale_factor", model_config.get("scale", 1.0))

        # 更新随机动作间隔
        self.update_random_intervals()

        # 初始化对话组件
        self.init_chat_components()

        print("配置已应用")

    def init_chat_components(self):
        """初始化对话相关组件"""
        try:
            # 初始化LLM客户端
            self.llm_client = LLMClient(self.config_manager)

            # 初始化文本显示管理器
            self.text_display_manager = TextDisplayManager(self)

            # 初始化聊天响应处理器
            self.chat_response_handler = ChatResponseHandler(self)
            self.chat_response_handler.response_ready.connect(self.show_ai_response)

            # 连接快速输入信号
            self.text_display_manager.connect_signals(
                message_handler=self.on_quick_input_message,
                preset_handler=self.on_preset_changed
            )

            print("✅ 对话组件初始化成功")
        except Exception as e:
            print(f"❌ 对话组件初始化失败: {e}")
            import traceback
            traceback.print_exc()

    def on_quick_input_message(self, message: str):
        """处理快速输入的消息"""
        print(f"🎯 收到快速输入消息: {message}")

        if self.llm_client and self.llm_client.is_configured():
            print("✅ LLM客户端已配置，开始处理对话")
            # 在后台处理对话
            import threading
            def chat_thread():
                try:
                    print(f"📤 发送消息到LLM: {message}")
                    response = self.llm_client.chat(message)
                    print(f"📥 收到LLM响应: {response[:100] if response else 'None'}...")

                    if response:
                        # 使用信号机制在主线程中显示回复
                        print("🔄 准备通过信号显示回复")
                        self.chat_response_handler.emit_response(response)
                    else:
                        print("❌ LLM返回空响应")
                        self.chat_response_handler.emit_response("❌ 未收到有效回复")
                except Exception as e:
                    print(f"❌ 对话处理失败: {e}")
                    import traceback
                    traceback.print_exc()
                    self.chat_response_handler.emit_response(f"❌ 对话处理失败: {e}")

            threading.Thread(target=chat_thread, daemon=True).start()
        else:
            print("❌ LLM客户端未配置")
            self.text_display_manager.show_message("❌ 请先在设置中配置API信息")

    def show_ai_response(self, response: str):
        """显示AI回复"""
        print(f"🎯 准备显示AI回复: {response[:50]}...")

        if self.text_display_manager:
            print("📱 文本显示管理器存在，调用show_message")
            try:
                self.text_display_manager.show_message(response)
                print("✅ show_message调用完成")

                # 检查文本叠加层状态并强制刷新
                if hasattr(self.text_display_manager, 'text_overlay'):
                    overlay = self.text_display_manager.text_overlay
                    print(f"📊 文本叠加层状态:")
                    print(f"  - 是否可见: {overlay.isVisible()}")
                    print(f"  - 位置: {overlay.pos()}")
                    print(f"  - 大小: {overlay.size()}")
                    print(f"  - 父窗口: {overlay.parent_widget}")
                    print(f"  - 窗口标志: {overlay.windowFlags()}")

                    # 强制刷新和显示
                    if not overlay.isVisible():
                        print("⚠️ 文本叠加层不可见，尝试强制显示")
                        overlay.update_position()
                        overlay.show()
                        overlay.raise_()
                        overlay.activateWindow()
                        print(f"🔄 强制显示后状态: 可见={overlay.isVisible()}, 位置={overlay.pos()}")

                    # 强制重绘
                    overlay.repaint()
                    overlay.update()

            except Exception as e:
                print(f"❌ show_message调用失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ 文本显示管理器不存在")
            # 备用显示机制：创建临时文本叠加层
            try:
                print("🔧 尝试创建临时文本显示")
                from text_overlay import TextOverlay
                temp_overlay = TextOverlay(self)
                temp_overlay.show_text(response)
                print("✅ 临时文本显示创建成功")
            except Exception as e:
                print(f"❌ 临时文本显示失败: {e}")
                # 最后的备用方案：控制台输出
                print(f"📢 AI回复: {response}")

        print("🎯 show_ai_response方法执行完成")

    def on_preset_changed(self, preset_id: str):
        """处理预设切换"""
        print(f"🔄 预设切换到: {preset_id}")

        if self.llm_client:
            try:
                # 获取预设配置
                presets = self.config_manager.config.get("llm_presets", {})
                preset = presets.get(preset_id, {})

                if preset:
                    print(f"📋 预设配置: {preset}")

                    # 构建完整的配置更新
                    config_update = {}

                    # 处理API配置
                    preset_api_config = preset.get("api_config", {})
                    if preset_api_config:
                        # 确保包含必要的API配置
                        api_config_update = self.llm_client.api_config.copy()
                        api_config_update.update(preset_api_config)
                        config_update["api_config"] = api_config_update
                        print(f"📡 API配置更新: {api_config_update}")

                    # 处理系统提示词
                    system_prompt = preset.get("system_prompt", "")
                    if system_prompt:
                        config_update["conversation_settings"] = {
                            "system_prompt": system_prompt,
                            "max_history_length": self.llm_client.conversation_settings.get("max_history_length", 20),
                            "save_history": self.llm_client.conversation_settings.get("save_history", True)
                        }
                        print(f"💬 系统提示词更新: {system_prompt[:50]}...")

                    # 处理默认参数（如果预设中有的话）
                    if "temperature" in preset_api_config:
                        default_params_update = self.llm_client.default_params.copy()
                        if "temperature" in preset_api_config:
                            default_params_update["temperature"] = preset_api_config["temperature"]
                        if "max_tokens" in preset_api_config:
                            default_params_update["max_tokens"] = preset_api_config["max_tokens"]
                        config_update["default_params"] = default_params_update
                        print(f"⚙️ 默认参数更新: {default_params_update}")

                    # 使用LLMClient的update_config方法进行完整更新
                    self.llm_client.update_config(config_update)

                    # 清空对话历史，使用新的系统提示词
                    self.llm_client.clear_history()

                    preset_name = preset.get("name", preset_id)
                    print(f"✅ 预设已切换到: {preset_name}")
                    print(f"🔧 当前LLM配置:")
                    print(f"  - API URL: {self.llm_client.api_config.get('base_url', 'N/A')}")
                    print(f"  - 模型: {self.llm_client.api_config.get('model', 'N/A')}")
                    print(f"  - 温度: {self.llm_client.default_params.get('temperature', 'N/A')}")
                    print(f"  - 系统提示词: {self.llm_client.current_system_prompt[:50] if self.llm_client.current_system_prompt else 'N/A'}...")

                    # 显示切换提示
                    if self.text_display_manager:
                        self.text_display_manager.show_message(f"已切换到预设: {preset_name}")
                else:
                    print(f"❌ 预设不存在: {preset_id}")

            except Exception as e:
                print(f"❌ 预设切换失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ LLM客户端未初始化")

    def update_random_intervals(self):
        """更新随机动作和表情的间隔时间"""
        model_config = self.config_manager.config["model"]
        print(f"更新随机间隔: 动作={model_config['random_motion_enabled']}, 表情={model_config['random_expression_enabled']}")
        self.random_motion_interval = random.uniform(
            model_config["random_motion_interval_min"],
            model_config["random_motion_interval_max"]
        )
        self.random_expression_interval = random.uniform(
            model_config["random_expression_interval_min"],
            model_config["random_expression_interval_max"]
        )
        print(f"新的随机间隔: 动作={self.random_motion_interval:.1f}s, 表情={self.random_expression_interval:.1f}s")

    def calculate_sync_scale(self):
        """计算基于窗口大小的同步缩放比例"""
        model_config = self.config_manager.config["model"]

        if not model_config.get("sync_scale_enabled", True):
            return 1.0

        base_width = model_config.get("base_window_width", 400)
        base_height = model_config.get("base_window_height", 500)

        current_width = self.width()
        current_height = self.height()

        # 计算宽度和高度的缩放比例，取较小值以确保模型完全适配
        width_ratio = current_width / base_width
        height_ratio = current_height / base_height
        sync_scale = min(width_ratio, height_ratio)

        # 应用缩放范围限制
        min_scale = model_config.get("min_scale", 0.3)
        max_scale = model_config.get("max_scale", 3.0)
        sync_scale = max(min_scale, min(max_scale, sync_scale))

        return sync_scale

    def calculate_final_scale(self):
        """计算最终的模型缩放比例（同步缩放 × 手动缩放）"""
        sync_scale = self.calculate_sync_scale()
        manual_scale = getattr(self, 'manual_scale_factor', 1.0)

        final_scale = sync_scale * manual_scale

        # 再次应用缩放范围限制
        model_config = self.config_manager.config["model"]
        min_scale = model_config.get("min_scale", 0.3)
        max_scale = model_config.get("max_scale", 3.0)
        final_scale = max(min_scale, min(max_scale, final_scale))

        return final_scale, sync_scale, manual_scale

    def apply_model_scale(self, force_update=False):
        """应用模型缩放"""
        if not self.model:
            return

        final_scale, sync_scale, manual_scale = self.calculate_final_scale()

        # 只在缩放发生变化时更新
        if force_update or abs(getattr(self, 'current_scale', 0) - final_scale) > 0.01:
            self.current_scale = final_scale
            self.model.SetScale(final_scale)

            model_config = self.config_manager.config["model"]
            if model_config.get("sync_scale_enabled", True):
                print(f"模型缩放更新: 同步={sync_scale:.2f} × 手动={manual_scale:.2f} = 最终={final_scale:.2f}")
            else:
                print(f"模型缩放更新: 手动={manual_scale:.2f} = 最终={final_scale:.2f}")

    def set_always_on_top(self, enabled):
        """设置窗口置顶"""
        flags = self.windowFlags()
        if enabled:
            flags |= Qt.WindowType.WindowStaysOnTopHint
        else:
            flags &= ~Qt.WindowType.WindowStaysOnTopHint
        self.setWindowFlags(flags)
        self.show()  # 重新显示窗口以应用标志变更
        print(f"窗口置顶: {'开启' if enabled else '关闭'}")

    def show_settings(self):
        """显示设置对话框"""
        if self.settings_dialog is None:
            self.settings_dialog = NewSettingsDialog(self.config_manager, self)
            self.settings_dialog.config_changed.connect(self.on_config_changed)
            # 连接手动播放控制信号
            self.settings_dialog.play_motion_requested.connect(self.play_motion)
            self.settings_dialog.play_expression_requested.connect(self.play_expression)
            self.settings_dialog.reset_pose_requested.connect(self.reset_pose)
            self.settings_dialog.reset_expression_requested.connect(self.reset_expression)
            # 连接多模型控制信号
            self.settings_dialog.load_multiple_models_requested.connect(self.load_multiple_models)
            self.settings_dialog.close_all_models_requested.connect(self.close_all_models)

        # 设置对话框位置（在主窗口旁边）
        main_pos = self.pos()
        self.settings_dialog.move(main_pos.x() + self.width() + 10, main_pos.y())

        self.settings_dialog.show()
        self.settings_dialog.raise_()
        self.settings_dialog.activateWindow()

    def on_config_changed(self, section, key, value):
        """配置变更回调"""
        # 忽略未使用的参数
        _ = section, key, value
        print("配置已变更，正在应用新设置...")

        # 保存当前状态
        current_pos = self.pos()
        was_visible = self.isVisible()

        # 分步骤应用配置，避免模型丢失
        self.apply_config_safely()

        # 确保窗口可见
        if was_visible and not self.isVisible():
            self.show()

        # 恢复窗口位置
        self.move(current_pos)

        # 刷新对话界面配置
        if self.chat_dialog:
            self.chat_dialog.refresh_config()

        # 刷新文本显示管理器配置
        if self.text_display_manager:
            text_display_config = self.config_manager.config.get("text_display", {})
            self.text_display_manager.update_config(text_display_config)

        print("配置应用完成")

    def apply_config_safely(self):
        """安全地应用配置，避免模型丢失"""
        config = self.config_manager.config
        window_config = config["window"]
        model_config = config["model"]

        print("正在应用窗口配置...")

        # 1. 先处理窗口大小（不会影响模型）
        new_width = window_config["width"]
        new_height = window_config["height"]
        if self.width() != new_width or self.height() != new_height:
            # 临时标记为非用户调整，避免触发同步缩放
            self.is_resizing = False
            self.resize(new_width, new_height)
            print(f"窗口大小已更新为: {new_width} x {new_height}")

        # 2. 处理透明模式（不会影响模型）
        self.is_transparent = window_config["transparent"]
        self.transparency_mode = window_config.get("transparency_mode", TransparencyMode.QT_NATIVE)
        self.refresh_transparency()
        print(f"透明模式已更新为: {'开启' if self.is_transparent else '关闭'} (方式: {self.transparency_mode})")

        # 3. 最后处理窗口置顶（可能会重建窗口）
        current_always_on_top = bool(self.windowFlags() & Qt.WindowType.WindowStaysOnTopHint)
        new_always_on_top = window_config["always_on_top"]

        if current_always_on_top != new_always_on_top:
            # 保存模型状态
            model_was_loaded = self.model is not None

            # 更新窗口标志
            flags = self.windowFlags()
            if new_always_on_top:
                flags |= Qt.WindowType.WindowStaysOnTopHint
            else:
                flags &= ~Qt.WindowType.WindowStaysOnTopHint

            self.setWindowFlags(flags)
            self.show()  # 重新显示窗口
            print(f"窗口置顶已更新为: {'开启' if new_always_on_top else '关闭'}")

            # 如果模型之前已加载，确保它仍然存在
            if model_was_loaded and self.model:
                # 重新应用模型设置
                self.current_scale = model_config["scale"]
                self.model.SetScale(self.current_scale)
                self.model.Resize(self.width(), self.height())
                print("模型状态已恢复")

        # 4. 检查是否需要切换模型
        new_model_path = model_config.get("current_model_path", "")
        current_model_path = getattr(self, 'current_model_path', "")

        if new_model_path and new_model_path != current_model_path:
            if self.switch_model(new_model_path):
                self.current_model_path = new_model_path

        # 5. 应用模型相关设置
        if self.model:
            print("正在应用模型配置...")

            # 更新手动缩放系数
            self.manual_scale_factor = model_config.get("manual_scale_factor", model_config.get("scale", 1.0))

            # 应用同步缩放
            self.apply_model_scale(force_update=True)

            # 确保模型适应当前窗口大小
            self.model.Resize(self.width(), self.height())

            # 更新随机动作间隔
            self.update_random_intervals()
            print("模型配置应用完成")

    def showEvent(self, event):
        """窗口显示时应用透明处理"""
        super().showEvent(event)

        # 获取窗口句柄并应用透明
        hwnd = int(self.winId())
        if hwnd:
            self.transparent_hwnd = hwnd
            self.apply_transparency_mode()

    def initializeGL(self) -> None:
        """初始化 OpenGL"""
        print("Initializing OpenGL...")

        # 检查OpenGL版本
        try:
            version = gl.glGetString(gl.GL_VERSION)
            renderer = gl.glGetString(gl.GL_RENDERER)
            print(f"OpenGL Version: {version}")
            print(f"OpenGL Renderer: {renderer}")
        except Exception as e:
            print(f"Failed to get OpenGL info: {e}")

        # 检查Live2D版本
        try:
            live2d_version = live2d.LIVE2D_VERSION
            print(f"Live2D版本: {live2d_version}")
        except Exception as e:
            print(f"无法获取Live2D版本: {e}")

        # 使用正确的初始化函数
        try:
            print("Initializing Live2D OpenGL...")
            live2d.glInit()
            print("✅ Live2D OpenGL initialization successful")
        except Exception as e:
            print(f"❌ Live2D OpenGL initialization failed: {e}")
            import traceback
            traceback.print_exc()
            return

        # 设置基本的 OpenGL 状态，优化透明效果
        self.setup_opengl_state()

        # 创建模型
        try:
            print("Creating Live2D model object...")
            self.model = live2d.LAppModel()
            print("✅ Live2D model object created successfully")
        except Exception as e:
            print(f"❌ 创建Live2D模型对象失败: {e}")
            import traceback
            traceback.print_exc()
            self.model = None
            return

        # 直接加载模型
        print("Starting immediate model loading...")
        self.load_initial_model()

        # 验证模型是否成功加载
        if self.model is None:
            print("❌ 模型加载失败，窗口将显示为空")
        else:
            print("✅ 模型初始化流程完成")

        # 以 fps = 120 的频率进行绘图
        self.startTimer(int(1000 / 120))

    def load_initial_model(self):
        """延迟加载初始模型"""
        print("Loading initial model...")

        # 加载模型（优先使用配置中的模型）
        model_config = self.config_manager.config["model"]
        config_model_path = model_config.get("current_model_path", "")

        if config_model_path and os.path.exists(config_model_path):
            model_path = config_model_path
            self.current_model_path = config_model_path
            print(f"Using configured model: {model_path}")
        else:
            # 使用默认模型
            if live2d.LIVE2D_VERSION == 3:
                model_path = os.path.join(resources.RESOURCES_DIRECTORY, "v3/Hiyori/Hiyori.model3.json")
            else:
                model_path = os.path.join(resources.RESOURCES_DIRECTORY, "v2/shizuku/shizuku.model.json")

            self.current_model_path = model_path
            # 更新配置
            self.config_manager.set("model", "current_model_path", model_path)
            print(f"Using default model: {model_path}")

        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            print(f"ERROR: Model file does not exist: {model_path}")
            return

        print(f"Loading model: {model_path}")

        # 检查模型文件和相关文件是否存在
        model_dir = os.path.dirname(model_path)
        print(f"Model directory: {model_dir}")

        # 检查纹理文件
        texture_files = []
        for file in os.listdir(model_dir):
            if file.endswith('.png'):
                texture_files.append(file)
        print(f"Found texture files: {texture_files}")

        # 检查纹理目录
        texture_dir = os.path.join(model_dir, "Hiyori.2048")
        if os.path.exists(texture_dir):
            texture_files_in_dir = []
            for file in os.listdir(texture_dir):
                if file.endswith('.png'):
                    texture_files_in_dir.append(file)
            print(f"Found texture files in {texture_dir}: {texture_files_in_dir}")

        try:
            print("Starting model JSON loading...")
            print(f"Absolute model path: {os.path.abspath(model_path)}")

            # 确保OpenGL上下文是当前的
            self.makeCurrent()

            # 检查OpenGL状态
            gl_error = gl.glGetError()
            if gl_error != gl.GL_NO_ERROR:
                print(f"OpenGL error before model loading: {gl_error}")

            # 直接加载模型，不使用线程
            print("Calling model.LoadModelJson...")
            self.model.LoadModelJson(model_path)
            print("Model JSON loaded successfully")

            # 再次检查OpenGL状态
            gl_error = gl.glGetError()
            if gl_error != gl.GL_NO_ERROR:
                print(f"OpenGL error after model loading: {gl_error}")

            # 调整模型大小以适应当前窗口
            window_width, window_height = self.width(), self.height()
            print(f"Resizing model to window size: {window_width}x{window_height}")
            self.model.Resize(window_width, window_height)
            print("Model resized successfully")

            # 检查模型是否有效
            if hasattr(self.model, 'IsValid') and callable(getattr(self.model, 'IsValid')):
                is_valid = self.model.IsValid()
                print(f"Model validity check: {is_valid}")
                if not is_valid:
                    print("⚠️ 警告: 模型验证失败")

            print("✅ 模型加载完成")

        except Exception as e:
            print(f"❌ 模型加载异常: {e}")
            import traceback
            traceback.print_exc()

            # 尝试重新创建模型对象
            try:
                print("尝试重新创建模型对象...")
                self.model = live2d.LAppModel()
                print("模型对象重新创建成功")
            except Exception as e2:
                print(f"模型对象重新创建失败: {e2}")
                self.model = None

        # 应用配置中的缩放设置
        self.manual_scale_factor = model_config.get("manual_scale_factor", model_config.get("scale", 1.0))
        self.apply_model_scale(force_update=True)

        # 加载保存的模型位置
        self.load_model_position()

        print("OpenGL initialization complete")

    def resizeGL(self, w: int, h: int) -> None:
        """窗口大小改变时调用"""
        if self.model:
            # 调整模型渲染区域大小
            self.model.Resize(w, h)

            # 只有在启用同步缩放且是用户手动调整窗口大小时才应用缩放
            model_config = self.config_manager.config["model"]
            if (model_config.get("sync_scale_enabled", True) and
                hasattr(self, 'is_resizing') and self.is_resizing):
                # 用户正在手动调整窗口大小，应用同步缩放
                self.apply_model_scale(force_update=True)
                print(f"用户调整窗口大小: {w}x{h}, 模型缩放已同步更新")
            else:
                # 系统或程序调整窗口大小，只调整渲染区域，不改变模型缩放
                print(f"窗口大小变更: {w}x{h}, 仅调整渲染区域")

        # 更新文本显示组件位置
        if self.text_display_manager:
            self.text_display_manager.update_positions()



    def paintGL(self) -> None:
        """绘制函数 - 优化的透明处理"""
        if self.is_transparent:
            if self.transparency_mode == TransparencyMode.QT_NATIVE:
                # Qt原生透明：使用完全透明背景（修复白底问题qt）
                live2d.clearBuffer(0.0, 0.0, 0.0, 0.0)
            elif self.transparency_mode == TransparencyMode.WINDOWS_API_COLORKEY:
                # Windows API 颜色键透明：使用纯黑色背景
                live2d.clearBuffer(0.0, 0.0, 0.0, 1.0)
            elif self.transparency_mode == TransparencyMode.WINDOWS_API_ALPHA:
                # Windows API Alpha透明：使用透明背景
                live2d.clearBuffer(0.0, 0.0, 0.0, 0.0)
        else:
            # 不透明模式：使用深灰色背景，便于看到窗口边界
            live2d.clearBuffer(0.2, 0.2, 0.2, 1.0)

        # 更新和绘制模型
        if self.model:
            try:
                self.model.Update()
                self.model.Draw()
            except Exception as e:
                print(f"❌ 模型绘制错误: {e}")
                import traceback
                traceback.print_exc()
        else:
            # 模型未加载时显示调试信息（仅在第一次显示）
            if not hasattr(self, '_model_debug_shown'):
                print("⚠️ 模型未加载，无法绘制")
                self._model_debug_shown = True

    def timerEvent(self, event: QTimerEvent | None) -> None:
        """定时器事件 - 包含随机动作和表情"""
        # 忽略未使用的参数
        _ = event
        if not self.isVisible():
            return

        current_time = time.time()
        model_config = self.config_manager.config["model"]

        # 初始动作播放
        if self.a == 0:
            if self.model:
                self.model.StartMotion("TapBody", 0, live2d.MotionPriority.FORCE, onFinishMotionHandler=motion_finished_callback)
            self.a += 1

        # 随机动作触发（根据配置）
        if (model_config["random_motion_enabled"] and
            current_time - self.last_random_motion_time > self.random_motion_interval):
            if self.model:
                self.model.StartRandomMotion("Idle", live2d.MotionPriority.IDLE, onFinishMotionHandler=motion_finished_callback)
                print("Random idle motion triggered")
            self.last_random_motion_time = current_time
            # 根据配置重新设置随机间隔
            self.random_motion_interval = random.uniform(
                model_config["random_motion_interval_min"],
                model_config["random_motion_interval_max"]
            )

        # 随机表情触发（根据配置）
        if (model_config["random_expression_enabled"] and
            current_time - self.last_random_expression_time > self.random_expression_interval):
            if self.model:
                expression_name = self.model.SetRandomExpression()
                if expression_name:
                    print(f"Random expression triggered: {expression_name}")
            self.last_random_expression_time = current_time
            # 根据配置重新设置随机间隔
            self.random_expression_interval = random.uniform(
                model_config["random_expression_interval_min"],
                model_config["random_expression_interval_max"]
            )

        # 检测鼠标是否在 Live2D 区域内
        local_x, local_y = QCursor.pos().x() - self.x(), QCursor.pos().y() - self.y()
        if self.isInL2DArea(local_x, local_y):
            self.isInLA = True
        else:
            self.isInLA = False

        self.update()

    def get_resize_edge(self, x, y):
        """检测鼠标是否在可缩放边缘"""
        w, h = self.width(), self.height()
        border = self.border_width

        # 右下角
        if x >= w - border and y >= h - border:
            return 'corner'
        # 右边缘
        elif x >= w - border:
            return 'right'
        # 下边缘
        elif y >= h - border:
            return 'bottom'

        return None

    def update_cursor(self, x, y):
        """根据鼠标位置更新光标样式"""
        edge = self.get_resize_edge(x, y)

        if edge == 'corner':
            self.setCursor(Qt.CursorShape.SizeFDiagCursor)  # 对角线缩放光标
        elif edge == 'right':
            self.setCursor(Qt.CursorShape.SizeHorCursor)    # 水平缩放光标
        elif edge == 'bottom':
            self.setCursor(Qt.CursorShape.SizeVerCursor)    # 垂直缩放光标
        else:
            self.setCursor(Qt.CursorShape.ArrowCursor)      # 默认光标

    def isInL2DArea(self, click_x, click_y):
        """检测点击位置是否在 Live2D 模型区域内"""
        # 不透明模式下，整个窗口都是有效区域
        if not self.is_transparent:
            return True

        try:
            h = self.height()
            alpha = gl.glReadPixels(
                int(click_x * self.systemScale),
                int((h - click_y) * self.systemScale),
                1, 1,
                gl.GL_RGBA,
                gl.GL_UNSIGNED_BYTE
            )[3]
            return alpha > 0
        except:
            # 如果读取失败，在不透明模式下返回True，透明模式下返回False
            return not self.is_transparent

    def mousePressEvent(self, event: QMouseEvent) -> None:
        """鼠标按下事件 - 支持缩放、拖拽和模型移动"""
        x, y = event.scenePosition().x(), event.scenePosition().y()

        # 位置固定时禁用所有拖拽和移动操作
        if self.position_locked:
            return

        # 检查是否按住Ctrl键进行模型拖拽
        modifiers = QApplication.keyboardModifiers()
        if (modifiers & Qt.KeyboardModifier.ControlModifier and
            event.button() == Qt.MouseButton.LeftButton and
            self.model and self.isInL2DArea(x, y)):
            # 开始模型拖拽
            self.is_dragging_model = True
            self.model_drag_start_pos = QPoint(int(x), int(y))
            print("Model dragging started (Ctrl+Left Click)")
            return

        # 检查是否在缩放边缘
        resize_edge = self.get_resize_edge(x, y)
        if resize_edge:
            self.is_resizing = True
            self.resize_edge = resize_edge
            self.resize_start_pos = QPoint(int(x), int(y))
            self.resize_start_size = (self.width(), self.height())
            print(f"Resize started from {resize_edge} edge")
        elif self.isInL2DArea(x, y) or not self.is_transparent:
            # 透明模式下检查Live2D区域，不透明模式下允许所有区域
            self.clickInLA = True
            self.clickX, self.clickY = x, y
            print("Mouse pressed in Live2D area")

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 支持缩放、模型拖拽和交互"""
        x, y = event.scenePosition().x(), event.scenePosition().y()

        # 模型拖拽完成
        if self.is_dragging_model:
            self.is_dragging_model = False
            # 保存当前模型偏移量
            if self.model:
                # 获取当前偏移量并保存
                # 这里需要从模型获取当前偏移量，暂时使用计算值
                dx = x - self.model_drag_start_pos.x()
                dy = y - self.model_drag_start_pos.y()
                offset_scale = 0.002
                self.current_model_offset_x += dx * offset_scale
                self.current_model_offset_y -= dy * offset_scale

                # 限制范围
                max_offset = 1.0
                self.current_model_offset_x = max(-max_offset, min(max_offset, self.current_model_offset_x))
                self.current_model_offset_y = max(-max_offset, min(max_offset, self.current_model_offset_y))

                # 保存到配置
                self.save_model_position()
                print(f"Model dragging completed. Final offset: ({self.current_model_offset_x:.3f}, {self.current_model_offset_y:.3f})")
            return

        if self.is_resizing:
            self.is_resizing = False
            self.resize_edge = None
            print("Resize completed")
        elif event.button() == Qt.MouseButton.RightButton:
            # 右键菜单
            self.show_context_menu(event.globalPosition().toPoint())
        elif self.isInLA and self.model:
            # 点击时触发随机动作和表情（参考其他文件的实现）
            if self.model.HitTest("Body", x, y):
                self.model.StartRandomMotion("TapBody", live2d.MotionPriority.NORMAL)
                print("Body clicked - Random TapBody motion triggered")
            if self.model.HitTest("Head", x, y):
                expression_name = self.model.SetRandomExpression()
                print(f"Head clicked - Random expression triggered: {expression_name}")
            self.clickInLA = False
            print("Mouse released - Interaction completed")

    def show_context_menu(self, pos):
        """显示右键菜单"""
        menu = QMenu(self)

        # 设置菜单样式
        menu.setStyleSheet("""
            QMenu {
                background-color: rgba(240, 240, 240, 230);
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 3px;
            }
            QMenu::item:selected {
                background-color: rgba(0, 120, 215, 180);
                color: white;
            }
        """)

        # 添加菜单项
        settings_action = QAction("⚙️ 设置", self)
        settings_action.triggered.connect(self.show_settings)
        menu.addAction(settings_action)

        # 对话功能
        chat_action = QAction("💬 文字对话", self)
        chat_action.triggered.connect(self.show_chat_dialog)
        menu.addAction(chat_action)

        # 快速输入
        quick_input_action = QAction("⌨️ 快速输入", self)
        quick_input_action.triggered.connect(self.show_quick_input)
        menu.addAction(quick_input_action)

        menu.addSeparator()

        # 重置模型位置
        reset_position_action = QAction("🎯 重置模型位置", self)
        reset_position_action.triggered.connect(self.reset_model_position)
        menu.addAction(reset_position_action)

        menu.addSeparator()

        # 窗口置顶切换
        always_on_top = self.config_manager.get("window", "always_on_top", True)
        top_action = QAction(f"📌 窗口置顶 {'✓' if always_on_top else ''}", self)
        top_action.triggered.connect(self.toggle_always_on_top)
        menu.addAction(top_action)

        # 透明模式切换
        transparent = self.config_manager.get("window", "transparent", True)
        transparent_action = QAction(f"👻 透明模式 {'✓' if transparent else ''}", self)
        transparent_action.triggered.connect(self.toggle_transparency_mode)
        menu.addAction(transparent_action)

        # 位置固定切换
        position_locked = self.config_manager.get("window", "position_locked", False)
        lock_action = QAction(f"🔒 位置固定 {'✓' if position_locked else ''}", self)
        lock_action.triggered.connect(self.toggle_position_lock)
        menu.addAction(lock_action)

        # 透明模式子菜单
        transparency_menu = menu.addMenu("🔧 透明模式")
        current_mode = getattr(self, 'transparency_mode', TransparencyMode.QT_NATIVE)

        modes = [
            (TransparencyMode.QT_NATIVE, "Qt原生透明 (推荐)"),
            (TransparencyMode.WINDOWS_API_COLORKEY, "Windows API 颜色键"),
            (TransparencyMode.WINDOWS_API_ALPHA, "Windows API Alpha")
        ]

        for mode_id, mode_name in modes:
            is_current = current_mode == mode_id
            mode_action = QAction(f"{mode_name} {'✓' if is_current else ''}", self)
            # 使用闭包正确捕获mode_id
            def make_handler(mode):
                return lambda: self.set_transparency_mode(mode)
            mode_action.triggered.connect(make_handler(mode_id))
            transparency_menu.addAction(mode_action)

        menu.addSeparator()

        exit_action = QAction("❌ 退出", self)
        exit_action.triggered.connect(self.close)
        menu.addAction(exit_action)

        menu.exec(pos)

    def show_chat_dialog(self):
        """显示对话界面"""
        if not self.chat_dialog:
            self.chat_dialog = ChatDialog(self.config_manager, self)
            # 连接信号，当收到回复时在模型上显示
            self.chat_dialog.message_received.connect(self.show_ai_response)

        self.chat_dialog.show()
        self.chat_dialog.raise_()
        self.chat_dialog.activateWindow()

    def show_quick_input(self):
        """显示快速输入"""
        if self.text_display_manager:
            self.text_display_manager.show_quick_input()

    def toggle_always_on_top(self):
        """切换窗口置顶状态"""
        current = self.config_manager.get("window", "always_on_top", True)
        new_value = not current
        self.config_manager.set("window", "always_on_top", new_value)
        self.config_manager.save_config()
        self.set_always_on_top(new_value)

        # 同步更新设置界面
        self.sync_settings_dialog()

    def toggle_transparency_mode(self):
        """切换透明模式"""
        current = self.config_manager.get("window", "transparent", True)
        new_value = not current
        self.config_manager.set("window", "transparent", new_value)
        self.config_manager.save_config()
        self.is_transparent = new_value

        # 使用统一的刷新方法
        self.refresh_transparency()
        print(f"切换到{'透明' if self.is_transparent else '不透明'}模式")

        # 同步更新设置界面
        self.sync_settings_dialog()

    def set_transparency_mode(self, mode):
        """设置透明通道处理方式"""
        self.transparency_mode = mode
        self.config_manager.set("window", "transparency_mode", mode)
        self.config_manager.save_config()

        # 刷新透明设置
        self.refresh_transparency()
        print(f"透明通道处理方式已切换为: {mode}")

        # 同步更新设置界面
        self.sync_settings_dialog()

    def toggle_position_lock(self):
        """切换位置固定状态"""
        current = self.config_manager.get("window", "position_locked", False)
        new_value = not current
        self.config_manager.set("window", "position_locked", new_value)
        self.config_manager.save_config()
        self.position_locked = new_value

        print(f"位置固定: {'开启' if self.position_locked else '关闭'}")

        # 同步更新设置界面
        self.sync_settings_dialog()

    def sync_settings_dialog(self):
        """同步更新设置界面的显示"""
        if self.settings_dialog and self.settings_dialog.isVisible():
            # 重新加载设置界面的显示
            self.settings_dialog.load_settings()
            print("设置界面已同步更新")

    def switch_model(self, model_path: str):
        """切换模型"""
        if not os.path.exists(model_path):
            print(f"模型文件不存在: {model_path}")
            return False

        try:
            print(f"正在切换模型: {model_path}")

            # 保存当前状态
            current_scale = getattr(self, 'manual_scale_factor', 1.0)
            was_visible = self.isVisible()

            # 释放当前模型
            if self.model:
                self.model = None

            # 确保OpenGL上下文是当前的
            self.makeCurrent()

            # 重新配置OpenGL状态
            self.setup_opengl_state()

            # 加载新模型
            self.model = live2d.LAppModel()
            self.model.LoadModelJson(model_path)

            # 调整模型大小以适应当前窗口
            self.model.Resize(self.width(), self.height())

            # 恢复缩放状态
            self.manual_scale_factor = current_scale
            self.apply_model_scale(force_update=True)

            # 更新当前模型路径
            self.current_model_path = model_path

            # 加载保存的模型位置
            self.load_model_position()

            # 重新初始化模型状态
            self.reset_model_state()

            # 强制刷新透明设置
            self.refresh_transparency()

            # 确保窗口可见
            if was_visible:
                self.show()
                # 再次确保透明设置正确应用
                self.refresh_transparency()
                # 强制重绘
                self.update()

            # 更新配置
            self.config_manager.set("model", "current_model_path", model_path)
            self.config_manager.save_config()

            model_name = os.path.splitext(os.path.basename(model_path))[0]
            print(f"模型切换成功: {model_name}")
            return True

        except Exception as e:
            print(f"模型切换失败: {e}")
            return False

    def setup_opengl_state(self):
        """设置OpenGL状态 - Live2D兼容配置"""
        try:
            # 启用混合，使用标准混合函数
            gl.glEnable(gl.GL_BLEND)
            gl.glBlendFunc(gl.GL_SRC_ALPHA, gl.GL_ONE_MINUS_SRC_ALPHA)

            # 禁用深度测试（2D渲染）
            gl.glDisable(gl.GL_DEPTH_TEST)

            # 禁用抗锯齿，避免边缘问题
            gl.glDisable(gl.GL_MULTISAMPLE)

            # 设置纹理参数，优化Live2D渲染
            gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MAG_FILTER, gl.GL_LINEAR)
            gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MIN_FILTER, gl.GL_LINEAR)

            # 设置像素存储对齐
            gl.glPixelStorei(gl.GL_UNPACK_ALIGNMENT, 4)  # Live2D推荐使用4字节对齐

            # 检查OpenGL错误
            gl_error = gl.glGetError()
            if gl_error != gl.GL_NO_ERROR:
                print(f"OpenGL状态设置错误: {gl_error}")
            else:
                print(f"✅ OpenGL状态配置成功 (透明模式: {self.transparency_mode})")

        except Exception as e:
            print(f"❌ OpenGL状态设置失败: {e}")
            import traceback
            traceback.print_exc()

    def apply_transparency_mode(self):
        """应用当前的透明模式"""
        if not self.is_transparent:
            if self.transparent_hwnd:
                make_window_opaque(self.transparent_hwnd)
            print("✅ 窗口设为不透明")
            return

        if self.transparency_mode == TransparencyMode.QT_NATIVE:
            # Qt原生透明：确保Windows API透明被清除
            if self.transparent_hwnd:
                make_window_opaque(self.transparent_hwnd)
            print("✅ 使用Qt原生透明模式")
        elif self.transparency_mode == TransparencyMode.WINDOWS_API_COLORKEY:
            if self.transparent_hwnd:
                make_window_transparent_colorkey(self.transparent_hwnd)
                print("✅ 使用Windows API 颜色键透明")
        elif self.transparency_mode == TransparencyMode.WINDOWS_API_ALPHA:
            if self.transparent_hwnd:
                alpha_value = self.config_manager.get("window", "alpha_value", 240)
                make_window_transparent_alpha(self.transparent_hwnd, alpha_value)
                print(f"✅ 使用Windows API Alpha透明 (Alpha: {alpha_value})")



    def refresh_transparency(self):
        """刷新透明设置 - 简洁高效的实现"""
        if not self.transparent_hwnd:
            hwnd = int(self.winId())
            if hwnd:
                self.transparent_hwnd = hwnd

        # 保存当前窗口状态
        was_maximized = self.isMaximized()
        was_visible = self.isVisible()
        current_geometry = self.geometry()
        current_flags = self.windowFlags()
        always_on_top = bool(current_flags & Qt.WindowType.WindowStaysOnTopHint)

        # 确定新的窗口标志
        if self.is_transparent:
            new_flags = Qt.WindowType.FramelessWindowHint
        else:
            new_flags = Qt.WindowType.Window

        if always_on_top:
            new_flags |= Qt.WindowType.WindowStaysOnTopHint

        # 检查是否需要重新创建窗口（Qt原生透明切换时必须重新创建）
        need_recreate = (
            self.is_transparent and
            self.transparency_mode == TransparencyMode.QT_NATIVE and
            not self.testAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        ) or (
            (not self.is_transparent or self.transparency_mode != TransparencyMode.QT_NATIVE) and
            self.testAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        )

        if need_recreate:
            # Qt原生透明需要重新创建窗口
            self.hide()

            # 设置窗口标志
            self.setWindowFlags(new_flags)

            # 设置Qt原生透明属性
            if self.is_transparent and self.transparency_mode == TransparencyMode.QT_NATIVE:
                self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
                print("✅ 启用Qt原生透明属性")
            else:
                self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)
                print("❌ 禁用Qt原生透明属性")

            # 恢复窗口状态
            if was_visible:
                if was_maximized:
                    self.showMaximized()
                else:
                    self.setGeometry(current_geometry)
                    self.show()
        else:
            # 只需要更新窗口标志
            if current_flags != new_flags:
                self.setWindowFlags(new_flags)
                if was_visible:
                    if was_maximized:
                        self.showMaximized()
                    else:
                        self.setGeometry(current_geometry)
                        self.show()

        # 应用透明模式
        self.apply_transparency_mode()

        print(f"✅ 透明设置已刷新: {self.transparency_mode}")

    def reset_model_state(self):
        """重置模型状态"""
        if self.model:
            # 重置动作计数器，确保初始动作能正常播放
            self.a = 0

            # 重置随机动作和表情的时间
            self.last_random_motion_time = time.time()
            self.last_random_expression_time = time.time()

            # 更新随机间隔
            self.update_random_intervals()

            print("模型状态已重置")

    def play_motion(self, motion_group: str, motion_index: int):
        """播放指定动作"""
        if not self.model:
            print("模型未加载，无法播放动作")
            return

        try:
            if motion_index == -1:
                # 播放随机动作
                self.model.StartRandomMotion(motion_group, live2d.MotionPriority.NORMAL)
                print(f"播放随机动作: {motion_group}")
            else:
                # 播放指定动作
                self.model.StartMotion(motion_group, motion_index, live2d.MotionPriority.NORMAL)
                print(f"播放动作: {motion_group}[{motion_index}]")
        except Exception as e:
            print(f"播放动作失败: {e}")

    def play_expression(self, expression_id: str):
        """播放指定表情"""
        if not self.model:
            print("模型未加载，无法播放表情")
            return

        try:
            if expression_id == "random":
                # 播放随机表情
                expression_name = self.model.SetRandomExpression()
                print(f"播放随机表情: {expression_name}")
            elif expression_id == "default":
                # 播放默认表情（使用索引0）
                self.model.SetExpression(0)
                print("播放默认表情")
            else:
                # 尝试将表情ID转换为索引，如果失败则直接使用字符串
                try:
                    # 如果是数字字符串，转换为整数
                    if expression_id.isdigit():
                        expression_index = int(expression_id)
                        self.model.SetExpression(expression_index)
                        print(f"播放表情索引: {expression_index}")
                    else:
                        # 如果是表情名称，尝试通过名称设置
                        # 这里可能需要根据具体的Live2D API调整
                        # 先尝试索引0作为默认
                        self.model.SetExpression(0)
                        print(f"播放表情: {expression_id} (使用默认索引)")
                except ValueError:
                    # 如果转换失败，使用默认表情
                    self.model.SetExpression(0)
                    print(f"播放表情: {expression_id} (转换失败，使用默认)")
        except Exception as e:
            print(f"播放表情失败: {e}")

    def reset_pose(self):
        """重置模型姿态"""
        if not self.model:
            print("模型未加载，无法重置姿态")
            return

        try:
            # 停止当前动作并播放默认姿态
            self.model.StartMotion("Idle", 0, live2d.MotionPriority.FORCE)
            print("模型姿态已重置")
        except Exception as e:
            print(f"重置姿态失败: {e}")

    def reset_expression(self):
        """重置模型表情"""
        if not self.model:
            print("模型未加载，无法重置表情")
            return

        try:
            # 重置为默认表情
            self.model.SetExpression(0)
            print("模型表情已重置")
        except Exception as e:
            print(f"重置表情失败: {e}")

    def load_multiple_models(self, model_paths):
        """加载多个模型"""
        print(f"主窗口收到多模型加载请求: {len(model_paths)} 个模型")

        # 检查多模型模式是否启用
        if not self.config_manager.get("model", "multi_model_enabled", False):
            print("多模型模式未启用，忽略加载请求")
            return

        # 使用多模型管理器加载模型
        self.multi_model_manager.load_models(model_paths)

        print(f"多模型加载完成，当前活动窗口数: {self.multi_model_manager.get_window_count()}")

    def close_all_models(self):
        """关闭所有多模型窗口"""
        print("主窗口收到关闭所有模型的请求")

        # 使用多模型管理器关闭所有模型
        self.multi_model_manager.close_all_models()

        print("所有多模型窗口关闭完成")

    def mouseMoveEvent(self, event: QMouseEvent) -> None:
        """鼠标移动事件 - 支持缩放、拖拽、模型移动和角色跟随"""
        x, y = event.scenePosition().x(), event.scenePosition().y()

        # 位置固定时只允许角色跟随鼠标，禁用其他操作
        if self.position_locked:
            # 角色跟随鼠标（位置固定时仍然允许）
            if self.model:
                self.model.Drag(x, y)
            return

        # 模型拖拽功能
        if self.is_dragging_model and self.model:
            dx = x - self.model_drag_start_pos.x()
            dy = y - self.model_drag_start_pos.y()

            # 计算新的模型偏移量（转换为场景坐标）
            # 使用简单的比例转换，可以根据需要调整
            offset_scale = 0.002  # 调整这个值来控制拖拽敏感度
            new_offset_x = self.current_model_offset_x + dx * offset_scale
            new_offset_y = self.current_model_offset_y - dy * offset_scale  # Y轴反向

            # 限制模型移动范围（防止移出窗口太远）
            max_offset = 1.0  # 最大偏移量
            new_offset_x = max(-max_offset, min(max_offset, new_offset_x))
            new_offset_y = max(-max_offset, min(max_offset, new_offset_y))

            # 应用新的偏移量
            self.model.SetOffset(new_offset_x, new_offset_y)
            print(f"Model offset: ({new_offset_x:.3f}, {new_offset_y:.3f})")
            return

        # 缩放功能
        if self.is_resizing and self.resize_start_size:
            start_w, start_h = self.resize_start_size
            dx = x - self.resize_start_pos.x()
            dy = y - self.resize_start_pos.y()

            new_w, new_h = start_w, start_h

            if self.resize_edge == 'right':
                new_w = max(200, start_w + dx)  # 最小宽度200
            elif self.resize_edge == 'bottom':
                new_h = max(200, start_h + dy)  # 最小高度200
            elif self.resize_edge == 'corner':
                new_w = max(200, start_w + dx)
                new_h = max(200, start_h + dy)

            # 调整窗口大小（缩放逻辑在resizeGL中处理）
            self.resize(int(new_w), int(new_h))

        # 更新光标样式
        elif not self.clickInLA and not self.is_dragging_model:
            self.update_cursor(x, y)

        # 角色跟随鼠标（只在非拖拽模型状态下）
        if self.model and not self.is_resizing and not self.is_dragging_model:
            self.model.Drag(x, y)

        # 窗口拖拽
        if self.clickInLA:
            new_x = int(self.x() + x - self.clickX)
            new_y = int(self.y() + y - self.clickY)
            self.move(new_x, new_y)

    def wheelEvent(self, event):
        """滚轮事件 - 手动缩放调整"""
        if self.model:
            # 获取滚轮滚动方向
            delta = event.angleDelta().y()
            scale_factor = 1.1 if delta > 0 else 0.9

            # 更新手动缩放系数
            self.manual_scale_factor *= scale_factor

            # 应用缩放范围限制
            model_config = self.config_manager.config["model"]
            min_scale = model_config.get("min_scale", 0.3)
            max_scale = model_config.get("max_scale", 3.0)

            # 计算在当前同步缩放下的手动缩放限制
            sync_scale = self.calculate_sync_scale()
            max_manual = max_scale / sync_scale if sync_scale > 0 else max_scale
            min_manual = min_scale / sync_scale if sync_scale > 0 else min_scale

            self.manual_scale_factor = max(min_manual, min(max_manual, self.manual_scale_factor))

            # 应用缩放并更新配置
            self.apply_model_scale()
            self.config_manager.set("model", "manual_scale_factor", self.manual_scale_factor)
            # 同步更新设置界面
            self.sync_settings_dialog()
            print(f"手动缩放调整: {self.manual_scale_factor:.2f}")

    def keyPressEvent(self, event: QKeyEvent) -> None:
        """键盘事件 - 保留缩放快捷键和基本功能"""
        if event.key() == Qt.Key.Key_Escape:
            print("ESC pressed - closing window")
            self.close()
        elif event.key() == Qt.Key.Key_F1:
            # F1 键显示设置界面
            self.show_settings()
        elif event.key() == Qt.Key.Key_F2:
            # F2 键显示对话界面
            self.show_chat_dialog()
        elif event.key() == Qt.Key.Key_F3:
            # F3 键显示快速输入
            self.show_quick_input()
        elif event.key() == Qt.Key.Key_Plus or event.key() == Qt.Key.Key_Equal:
            # + 键放大手动缩放
            if self.model:
                self.manual_scale_factor *= 1.1

                # 应用缩放范围限制
                model_config = self.config_manager.config["model"]
                max_scale = model_config.get("max_scale", 3.0)
                sync_scale = self.calculate_sync_scale()
                max_manual = max_scale / sync_scale if sync_scale > 0 else max_scale

                self.manual_scale_factor = min(max_manual, self.manual_scale_factor)
                self.apply_model_scale()
                self.config_manager.set("model", "manual_scale_factor", self.manual_scale_factor)
                # 同步更新设置界面
                self.sync_settings_dialog()
                print(f"手动缩放增加到: {self.manual_scale_factor:.2f}")
        elif event.key() == Qt.Key.Key_Minus:
            # - 键缩小手动缩放
            if self.model:
                self.manual_scale_factor *= 0.9

                # 应用缩放范围限制
                model_config = self.config_manager.config["model"]
                min_scale = model_config.get("min_scale", 0.3)
                sync_scale = self.calculate_sync_scale()
                min_manual = min_scale / sync_scale if sync_scale > 0 else min_scale

                self.manual_scale_factor = max(min_manual, self.manual_scale_factor)
                self.apply_model_scale()
                self.config_manager.set("model", "manual_scale_factor", self.manual_scale_factor)
                # 同步更新设置界面
                self.sync_settings_dialog()
                print(f"手动缩放减少到: {self.manual_scale_factor:.2f}")
        elif event.key() == Qt.Key.Key_0:
            # 0 键重置手动缩放
            if self.model:
                self.manual_scale_factor = 1.0
                self.apply_model_scale()
                self.config_manager.set("model", "manual_scale_factor", self.manual_scale_factor)
                # 同步更新设置界面
                self.sync_settings_dialog()
                print("手动缩放重置到 1.0")
        super().keyPressEvent(event)

    def save_model_position(self):
        """保存当前模型位置到配置"""
        if not self.model or not hasattr(self, 'current_model_path'):
            return

        try:
            # 获取模型名称作为配置键
            model_name = os.path.splitext(os.path.basename(self.current_model_path))[0]

            # 保存位置信息
            position_config = {
                "offset_x": self.current_model_offset_x,
                "offset_y": self.current_model_offset_y
            }

            # 确保model_positions配置节存在
            if "model_positions" not in self.config_manager.config:
                self.config_manager.config["model_positions"] = {}

            self.config_manager.config["model_positions"][model_name] = position_config
            self.config_manager.save_config()

            print(f"模型位置已保存: {model_name} -> ({self.current_model_offset_x:.3f}, {self.current_model_offset_y:.3f})")

        except Exception as e:
            print(f"保存模型位置失败: {e}")

    def load_model_position(self):
        """从配置加载模型位置"""
        if not self.model or not hasattr(self, 'current_model_path'):
            return

        try:
            # 获取模型名称作为配置键
            model_name = os.path.splitext(os.path.basename(self.current_model_path))[0]

            # 从配置加载位置信息
            model_positions = self.config_manager.config.get("model_positions", {})
            position_config = model_positions.get(model_name, {})

            if position_config:
                self.current_model_offset_x = position_config.get("offset_x", 0.0)
                self.current_model_offset_y = position_config.get("offset_y", 0.0)

                # 应用位置
                self.model.SetOffset(self.current_model_offset_x, self.current_model_offset_y)

                print(f"模型位置已加载: {model_name} -> ({self.current_model_offset_x:.3f}, {self.current_model_offset_y:.3f})")
            else:
                # 没有保存的位置，使用默认值
                self.current_model_offset_x = 0.0
                self.current_model_offset_y = 0.0
                print(f"使用默认模型位置: {model_name}")

        except Exception as e:
            print(f"加载模型位置失败: {e}")
            # 使用默认值
            self.current_model_offset_x = 0.0
            self.current_model_offset_y = 0.0

    def save_window_state(self):
        """保存窗口位置和大小"""
        try:
            self.config_manager.set("window", "x", self.x())
            self.config_manager.set("window", "y", self.y())
            self.config_manager.set("window", "width", self.width())
            self.config_manager.set("window", "height", self.height())
            self.config_manager.save_config()
            print(f"窗口状态已保存: 位置({self.x()}, {self.y()}) 大小({self.width()}x{self.height()})")
        except Exception as e:
            print(f"保存窗口状态失败: {e}")

    def reset_model_position(self):
        """重置模型位置到中心"""
        if not self.model:
            return

        try:
            self.current_model_offset_x = 0.0
            self.current_model_offset_y = 0.0
            self.model.SetOffset(0.0, 0.0)

            # 保存重置后的位置
            self.save_model_position()

            print("模型位置已重置到中心")

        except Exception as e:
            print(f"重置模型位置失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件 - 保存窗口状态"""
        # 保存窗口位置和大小
        self.save_window_state()

        # 保存模型位置
        if self.model:
            self.save_model_position()

        print("窗口状态已保存，程序退出")
        super().closeEvent(event)

    def moveEvent(self, event):
        """窗口移动事件 - 保存位置"""
        super().moveEvent(event)
        # 延迟保存，避免拖拽过程中频繁保存
        if hasattr(self, '_move_timer'):
            self._move_timer.stop()
        else:
            from PySide6.QtCore import QTimer
            self._move_timer = QTimer()
            self._move_timer.setSingleShot(True)
            self._move_timer.timeout.connect(self.save_window_state)
        self._move_timer.start(500)  # 500ms后保存

    def resizeEvent(self, event):
        """窗口大小改变事件 - 保存大小"""
        super().resizeEvent(event)
        # 延迟保存，避免调整过程中频繁保存
        if hasattr(self, '_resize_timer'):
            self._resize_timer.stop()
        else:
            from PySide6.QtCore import QTimer
            self._resize_timer = QTimer()
            self._resize_timer.setSingleShot(True)
            self._resize_timer.timeout.connect(self.save_window_state)
        self._resize_timer.start(500)  # 500ms后保存

    def changeEvent(self, event):
        """窗口状态变化事件"""
        super().changeEvent(event)

        # 处理窗口状态变化（最大化、最小化、还原等）
        if event.type() == event.Type.WindowStateChange:
            # 确保透明设置在窗口状态变化后仍然正确
            if self.isVisible():
                # 延迟刷新透明设置，确保窗口状态稳定后再处理
                if hasattr(self, '_state_timer'):
                    self._state_timer.stop()
                else:
                    from PySide6.QtCore import QTimer
                    self._state_timer = QTimer()
                    self._state_timer.setSingleShot(True)
                    self._state_timer.timeout.connect(self._refresh_after_state_change)
                self._state_timer.start(100)  # 100ms后刷新

    def _refresh_after_state_change(self):
        """窗口状态变化后刷新设置"""
        try:
            # 重新应用透明设置，但不改变窗口状态
            if self.transparent_hwnd:
                self.apply_transparency_mode()
            print(f"窗口状态变化后透明设置已刷新")
        except Exception as e:
            print(f"刷新透明设置失败: {e}")


def main():
    """主函数"""
    print("Starting Live2D Transparent Window Application...")

    # 设置兼容的 OpenGL 格式，确保Live2D正常工作
    format = QSurfaceFormat()
    format.setAlphaBufferSize(8)
    format.setDepthBufferSize(24)
    format.setStencilBufferSize(8)
    format.setSamples(0)  # 禁用多重采样抗锯齿
    format.setVersion(3, 3)  # 使用OpenGL 3.3获得更好的透明支持
    format.setProfile(QSurfaceFormat.OpenGLContextProfile.CompatibilityProfile)  # 使用兼容模式确保Live2D正常工作
    format.setSwapBehavior(QSurfaceFormat.SwapBehavior.DoubleBuffer)
    format.setSwapInterval(1)  # 启用垂直同步
    QSurfaceFormat.setDefaultFormat(format)
    print("OpenGL 3.3 Compatibility Profile configured for Live2D compatibility")

    # 初始化 Live2D
    live2d.init()
    print("Live2D initialized")

    # 创建应用程序
    app = QApplication(sys.argv)

    # 创建透明窗口
    window = TransparentWindow()
    window.show()
    
    print("\n" + "="*85)
    print("LIVE2D 桌宠窗口已创建! (简洁高效透明效果 + OpenGL优化)")
    print("="*85)
    print("配置:")
    print("- OpenGL: 3.3 Compatibility Profile，Live2D兼容优化")
    print("- 窗口: 无边框 + 可配置置顶")
    print("- 透明效果: 3种简洁高效的透明模式")
    print("  * Qt原生透明 (推荐，简洁高效)")
    print("  * Windows API 颜色键透明 (兼容性好)")
    print("  * Windows API Alpha透明 (可调节透明度)")
    print("\n🎮 控制方式:")
    print("- 右键菜单: 访问所有设置和功能")
    print("- F1 键: 打开设置界面")
    print("- 点击身体: 随机 TapBody 动作")
    print("- 点击头部: 随机表情")
    print("\n⚙️ 设置界面功能:")
    print("- 🆕 模型文件扫描和选择")
    print("- 🆕 模型热切换功能")
    print("- 🆕 动作表情自选播放控制")
    print("- 窗口置顶控制 (状态持久化)")
    print("- 🆕 多种透明通道处理方式选择")
    print("- 🆕 Alpha透明度值调节")
    print("- 窗口大小设置")
    print("- 🆕 同步缩放设置 (模型自动适配窗口)")
    print("- 手动缩放调整")
    print("- 缩放范围限制")
    print("- 随机动作/表情开关和间隔设置")
    print("\n� 同步缩放功能:")
    print("- 自动适配: 模型根据窗口大小自动缩放")
    print("- 基准尺寸: 可设置模型1:1显示的窗口大小")
    print("- 智能缩放: 确保模型始终完整显示在窗口内")
    print("- 手动调整: 在同步缩放基础上进行额外调整")
    print("\n�🔧 缩放快捷键:")
    print("- + 键: 增加手动缩放")
    print("- - 键: 减少手动缩放")
    print("- 0 键: 重置手动缩放到 1.0")
    print("- 鼠标滚轮: 调整手动缩放")
    print("\n🖱️ 其他控制:")
    print("- 拖拽模型: 移动窗口")
    print("- 鼠标移动: 角色眼部跟踪")
    print("- 拖拽边缘: 调整窗口大小 (自动同步缩放)")
    print("- ESC: 关闭窗口")
    print("\n💡 提示:")
    print("- 右键菜单可快速切换置顶和透明模式")
    print("- 设置会自动保存到 config.json 文件")
    print("- 同步缩放确保模型在任何窗口大小下都完美适配")
    print("- 可在设置中关闭同步缩放，使用传统的固定缩放模式")
    print("\n🔍 模型扫描功能:")
    print("- 自动扫描指定目录的.model3.json文件")
    print("- 递归搜索子目录")
    print("- 模型有效性验证")
    print("- 智能缓存机制")
    print("- 一键模型切换")
    print("\n🎭 动作表情控制:")
    print("- 手动播放指定动作")
    print("- 随机动作播放")
    print("- 手动播放指定表情")
    print("- 随机表情播放")
    print("- 一键重置姿态和表情")
    print("\n🔧 透明模式:")
    print("- Qt原生透明: 推荐模式，简洁高效，跨平台兼容")
    print("- Windows API 颜色键: 传统方式，兼容性好，黑色区域透明")
    print("- Windows API Alpha: 支持半透明效果，可调节透明度值")
    print("- 右键菜单可快速切换透明模式")
    print("- 设置界面可详细配置各种参数")

    print("\n✨ 新功能:")
    print("- 🆕 简洁高效的透明模式 (Qt原生+Windows API)")
    print("- 🆕 Alpha透明度值调节")
    print("- 🆕 智能透明模式切换")
    print("- 🆕 Live2D模型文件扫描器")
    print("- 🆕 模型热切换系统")
    print("- 🆕 动作表情自选播放控制")
    print("- 🆕 模型与窗口同步缩放")
    print("- 🆕 智能缩放范围控制")
    print("- 🆕 基准尺寸自定义")
    print("- 完全配置化的界面设置")
    print("- 窗口置顶控制和状态指示")
    print("- 配置持久化存储")
    print("="*85)
    
    # 运行应用程序
    try:
        app.exec()
    finally:
        print("Disposing Live2D...")
        live2d.dispose()
        print("Application closed")


if __name__ == "__main__":
    main()


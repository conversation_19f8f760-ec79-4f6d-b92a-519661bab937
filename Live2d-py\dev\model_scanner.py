#!/usr/bin/env python3
"""
Live2D模型扫描器
自动扫描和识别可用的Live2D模型文件
"""

import os
import json
import time
import hashlib
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class ModelInfo:
    """模型信息类"""
    name: str                    # 模型名称
    path: str                   # 模型文件路径(.model3.json)
    directory: str              # 模型所在目录
    size: int                   # 文件大小
    modified_time: float        # 修改时间
    is_valid: bool = True       # 是否有效
    error_message: str = ""     # 错误信息
    preview_image: str = ""     # 预览图路径
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ModelInfo':
        """从字典创建实例"""
        return cls(**data)


class ModelScanner:
    """模型扫描器"""
    
    def __init__(self, cache_file: str = "model_cache.json"):
        self.cache_file = cache_file
        self.cache_data = {}
        self.load_cache()
    
    def load_cache(self):
        """加载缓存"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.cache_data = json.load(f)
                print(f"已加载模型缓存: {len(self.cache_data)} 个条目")
            else:
                self.cache_data = {}
                print("未找到模型缓存文件，将创建新缓存")
        except Exception as e:
            print(f"加载模型缓存失败: {e}")
            self.cache_data = {}
    
    def save_cache(self):
        """保存缓存"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache_data, f, indent=2, ensure_ascii=False)
            print(f"已保存模型缓存: {len(self.cache_data)} 个条目")
        except Exception as e:
            print(f"保存模型缓存失败: {e}")
    
    def get_file_hash(self, file_path: str) -> str:
        """获取文件哈希值（用于缓存验证）"""
        try:
            stat = os.stat(file_path)
            # 使用文件路径、大小和修改时间生成哈希
            content = f"{file_path}_{stat.st_size}_{stat.st_mtime}"
            return hashlib.md5(content.encode()).hexdigest()
        except:
            return ""
    
    def validate_model_file(self, model_path: str) -> tuple[bool, str]:
        """验证模型文件是否有效"""
        try:
            if not os.path.exists(model_path):
                return False, "文件不存在"
            
            if not model_path.endswith('.model3.json'):
                return False, "不是有效的模型文件格式"
            
            # 尝试解析JSON文件
            with open(model_path, 'r', encoding='utf-8') as f:
                model_data = json.load(f)
            
            # 检查必要的字段
            required_fields = ['FileReferences', 'Groups']
            for field in required_fields:
                if field not in model_data:
                    return False, f"缺少必要字段: {field}"
            
            # 检查模型文件引用
            model_dir = os.path.dirname(model_path)
            file_refs = model_data.get('FileReferences', {})
            
            # 检查moc3文件
            if 'Moc' in file_refs:
                moc_path = os.path.join(model_dir, file_refs['Moc'])
                if not os.path.exists(moc_path):
                    return False, f"Moc文件不存在: {file_refs['Moc']}"
            
            return True, ""
            
        except json.JSONDecodeError:
            return False, "JSON格式错误"
        except Exception as e:
            return False, f"验证失败: {str(e)}"
    
    def find_preview_image(self, model_dir: str) -> str:
        """查找模型预览图"""
        preview_extensions = ['.png', '.jpg', '.jpeg']
        preview_names = ['preview', 'icon', 'thumbnail', 'cover']
        
        for name in preview_names:
            for ext in preview_extensions:
                preview_path = os.path.join(model_dir, name + ext)
                if os.path.exists(preview_path):
                    return preview_path
        
        # 如果没找到特定名称的预览图，查找第一个图片文件
        try:
            for file in os.listdir(model_dir):
                if any(file.lower().endswith(ext) for ext in preview_extensions):
                    return os.path.join(model_dir, file)
        except:
            pass
        
        return ""
    
    def scan_directory(self, directory: str, recursive: bool = True) -> List[ModelInfo]:
        """扫描目录中的模型文件"""
        models = []
        scanned_count = 0
        valid_count = 0
        
        print(f"开始扫描目录: {directory}")
        start_time = time.time()
        
        try:
            if recursive:
                # 递归扫描
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        if file.endswith('.model3.json'):
                            model_path = os.path.join(root, file)
                            model_info = self.scan_single_model(model_path)
                            if model_info:
                                models.append(model_info)
                                scanned_count += 1
                                if model_info.is_valid:
                                    valid_count += 1
            else:
                # 只扫描当前目录
                try:
                    for file in os.listdir(directory):
                        if file.endswith('.model3.json'):
                            model_path = os.path.join(directory, file)
                            model_info = self.scan_single_model(model_path)
                            if model_info:
                                models.append(model_info)
                                scanned_count += 1
                                if model_info.is_valid:
                                    valid_count += 1
                except PermissionError:
                    print(f"无权限访问目录: {directory}")
        
        except Exception as e:
            print(f"扫描目录时发生错误: {e}")
        
        elapsed_time = time.time() - start_time
        print(f"扫描完成: 找到 {scanned_count} 个模型文件，其中 {valid_count} 个有效，耗时 {elapsed_time:.2f} 秒")
        
        # 保存缓存
        self.save_cache()
        
        return models
    
    def scan_single_model(self, model_path: str) -> Optional[ModelInfo]:
        """扫描单个模型文件"""
        try:
            # 检查缓存
            file_hash = self.get_file_hash(model_path)
            if file_hash in self.cache_data:
                cached_info = self.cache_data[file_hash]
                # 验证缓存是否仍然有效
                if os.path.exists(model_path):
                    stat = os.stat(model_path)
                    if stat.st_mtime == cached_info.get('modified_time'):
                        return ModelInfo.from_dict(cached_info)
            
            # 获取文件信息
            stat = os.stat(model_path)
            model_dir = os.path.dirname(model_path)
            model_name = os.path.splitext(os.path.basename(model_path))[0]
            
            # 验证模型文件
            is_valid, error_message = self.validate_model_file(model_path)
            
            # 查找预览图
            preview_image = ""
            if is_valid:
                preview_image = self.find_preview_image(model_dir)
            
            # 创建模型信息
            model_info = ModelInfo(
                name=model_name,
                path=model_path,
                directory=model_dir,
                size=stat.st_size,
                modified_time=stat.st_mtime,
                is_valid=is_valid,
                error_message=error_message,
                preview_image=preview_image
            )
            
            # 更新缓存
            self.cache_data[file_hash] = model_info.to_dict()
            
            return model_info
            
        except Exception as e:
            print(f"扫描模型文件失败 {model_path}: {e}")
            return None
    
    def get_valid_models(self, models: List[ModelInfo]) -> List[ModelInfo]:
        """获取有效的模型列表"""
        return [model for model in models if model.is_valid]
    
    def clear_cache(self):
        """清除缓存"""
        self.cache_data = {}
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)
        print("模型缓存已清除")


def test_scanner():
    """测试扫描器功能"""
    scanner = ModelScanner()
    
    # 测试扫描当前目录的上级目录（通常包含示例模型）
    test_dir = os.path.join(os.path.dirname(__file__), "..", "examples", "resources")
    if os.path.exists(test_dir):
        models = scanner.scan_directory(test_dir)
        print(f"\n找到的模型:")
        for model in models:
            status = "✓" if model.is_valid else "✗"
            print(f"{status} {model.name} - {model.path}")
            if not model.is_valid:
                print(f"  错误: {model.error_message}")
    else:
        print(f"测试目录不存在: {test_dir}")


if __name__ == "__main__":
    test_scanner()

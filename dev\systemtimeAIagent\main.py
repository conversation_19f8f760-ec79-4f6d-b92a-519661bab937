from agent import TimeAgent

def main():
    agent = TimeAgent()
    print("⏰ 时间偶像小助手已上线！输入 'quit' 退出")
    
    while True:
        try:
            user_input = input("\n你: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                break
            if not user_input:
                continue
                
            response = agent.chat(user_input)
            print(f"\n小助手: {response}")
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"\n错误: {str(e)}")
    
    print("\n拜拜～记得按时休息哦！💤")

if __name__ == "__main__":
    main()

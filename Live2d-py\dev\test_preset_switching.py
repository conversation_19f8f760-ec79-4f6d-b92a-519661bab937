#!/usr/bin/env python3
"""
测试预设切换功能
"""

import sys
import os
import json

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from settings_dialog import ConfigManager
from llm_client import LLMClient


def test_preset_switching():
    """测试预设切换功能"""
    print("🔍 测试预设切换功能...")
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建LLM客户端
        llm_client = LLMClient(config_manager)
        
        print(f"✅ LLM客户端初始化成功")
        print(f"🔧 初始配置:")
        print(f"  - API URL: {llm_client.api_config.get('base_url', 'N/A')}")
        print(f"  - 模型: {llm_client.api_config.get('model', 'N/A')}")
        print(f"  - 温度: {llm_client.default_params.get('temperature', 'N/A')}")
        print(f"  - 系统提示词: {llm_client.current_system_prompt[:50] if llm_client.current_system_prompt else 'N/A'}...")
        
        # 获取预设列表
        presets = config_manager.config.get("llm_presets", {})
        print(f"\n📋 找到 {len(presets)} 个预设:")
        for preset_id, preset_data in presets.items():
            name = preset_data.get("name", preset_id)
            model = preset_data.get("api_config", {}).get("model", "N/A")
            print(f"  - {preset_id}: {name} (模型: {model})")
        
        # 测试切换到魔女预设
        if "魔女" in presets:
            print(f"\n🔄 切换到魔女预设...")
            preset = presets["魔女"]
            
            # 构建配置更新
            config_update = {}
            
            # 处理API配置
            preset_api_config = preset.get("api_config", {})
            if preset_api_config:
                api_config_update = llm_client.api_config.copy()
                api_config_update.update(preset_api_config)
                config_update["api_config"] = api_config_update
            
            # 处理系统提示词
            system_prompt = preset.get("system_prompt", "")
            if system_prompt:
                config_update["conversation_settings"] = {
                    "system_prompt": system_prompt,
                    "max_history_length": llm_client.conversation_settings.get("max_history_length", 20),
                    "save_history": llm_client.conversation_settings.get("save_history", True)
                }
            
            # 处理默认参数
            if "temperature" in preset_api_config:
                default_params_update = llm_client.default_params.copy()
                if "temperature" in preset_api_config:
                    default_params_update["temperature"] = preset_api_config["temperature"]
                if "max_tokens" in preset_api_config:
                    default_params_update["max_tokens"] = preset_api_config["max_tokens"]
                config_update["default_params"] = default_params_update
            
            # 应用配置更新
            llm_client.update_config(config_update)
            
            print(f"✅ 预设切换完成")
            print(f"🔧 更新后配置:")
            print(f"  - API URL: {llm_client.api_config.get('base_url', 'N/A')}")
            print(f"  - 模型: {llm_client.api_config.get('model', 'N/A')}")
            print(f"  - 温度: {llm_client.default_params.get('temperature', 'N/A')}")
            print(f"  - 系统提示词: {llm_client.current_system_prompt[:50] if llm_client.current_system_prompt else 'N/A'}...")
            
            # 测试是否配置正确
            if llm_client.is_configured():
                print(f"✅ LLM客户端配置正确")
                
                # 测试发送消息
                print(f"\n📤 测试发送消息...")
                response = llm_client.chat("你好")
                
                if response and not response.startswith("❌"):
                    print(f"✅ 消息发送成功: {response[:100]}...")
                    return True
                else:
                    print(f"❌ 消息发送失败: {response}")
                    return False
            else:
                print(f"❌ LLM客户端配置不正确")
                return False
        else:
            print(f"❌ 未找到魔女预设")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始测试预设切换功能...")
    
    success = test_preset_switching()
    
    if success:
        print("\n🎉 预设切换功能测试通过！")
    else:
        print("\n❌ 预设切换功能测试失败！")
    
    return success


if __name__ == "__main__":
    main()

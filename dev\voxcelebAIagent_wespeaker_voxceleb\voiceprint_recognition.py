import torch
import numpy as np
from pyannote.audio import Model, Inference
from scipy.spatial.distance import cdist
from config import Config
import tempfile
import soundfile as sf

class VoiceprintRecognition:
    def __init__(self):
        # 禁用TF32警告
        import warnings
        warnings.filterwarnings("ignore", category=UserWarning, module="pyannote")

        # 设置TF32以提高性能
        if torch.cuda.is_available():
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True

        self.device = torch.device("cuda" if Config.USE_GPU and torch.cuda.is_available() else "cpu")
        print(f"使用设备: {self.device}")

        # 加载WeSpeaker模型
        try:
            # 使用Hugging Face Hub的模型ID而不是本地路径
            self.model = Model.from_pretrained(Config.WESPEAKER_MODEL_ID)
            self.inference = Inference(self.model, window="whole")

            # 移动到GPU
            if self.device.type == "cuda":
                self.inference.to(self.device)

            print("WeSpeaker声纹识别模型加载完成")
        except Exception as e:
            raise Exception(f"模型加载失败: {str(e)}")
    
    def extract_voiceprint(self, audio_data, sample_rate=None):
        """提取声纹特征向量"""
        import os
        import time
        tmp_file_path = None

        try:
            # 创建临时音频文件
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_file_path = tmp_file.name
                # 保存音频数据到临时文件
                sf.write(tmp_file_path, audio_data, Config.SAMPLE_RATE)

            # 等待文件写入完成
            time.sleep(0.1)

            # 提取声纹embedding
            embedding = self.inference(tmp_file_path)

            return embedding.flatten()

        except Exception as e:
            raise Exception(f"声纹提取失败: {str(e)}")
        finally:
            # 确保清理临时文件
            if tmp_file_path and os.path.exists(tmp_file_path):
                try:
                    time.sleep(0.1)  # 等待文件释放
                    os.unlink(tmp_file_path)
                except:
                    pass  # 忽略删除失败
    
    def compute_similarity(self, embedding1, embedding2):
        """计算两个声纹的相似度（使用余弦距离）"""
        try:
            # 确保embedding是2D数组
            if embedding1.ndim == 1:
                embedding1 = embedding1.reshape(1, -1)
            if embedding2.ndim == 1:
                embedding2 = embedding2.reshape(1, -1)
            
            # 计算余弦距离
            distance = cdist(embedding1, embedding2, metric="cosine")[0, 0]
            
            # 转换为相似度（距离越小，相似度越高）
            similarity = 1.0 - distance
            
            return float(similarity)
            
        except Exception as e:
            raise Exception(f"相似度计算失败: {str(e)}")
    
    def preprocess_audio(self, audio_data, sample_rate):
        """预处理音频数据"""
        # 转换为numpy数组
        if isinstance(audio_data, torch.Tensor):
            audio_data = audio_data.cpu().numpy()
        
        # 确保是1D数组
        if audio_data.ndim > 1:
            audio_data = audio_data.flatten()
        
        # 重采样（如果需要）
        if sample_rate != Config.SAMPLE_RATE:
            import librosa
            audio_data = librosa.resample(
                audio_data, 
                orig_sr=sample_rate, 
                target_sr=Config.SAMPLE_RATE
            )
        
        # 归一化
        if np.max(np.abs(audio_data)) > 0:
            audio_data = audio_data / np.max(np.abs(audio_data))
        
        return audio_data
    
    def extract_sliding_window_embeddings(self, audio_data, sample_rate=None):
        """使用滑动窗口提取多个embedding"""
        import os
        import time
        tmp_file_path = None

        try:
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_file:
                tmp_file_path = tmp_file.name
                sf.write(tmp_file_path, audio_data, Config.SAMPLE_RATE)

            # 等待文件写入完成
            time.sleep(0.1)

            # 使用滑动窗口
            sliding_inference = Inference(
                self.model,
                window="sliding",
                duration=Config.WINDOW_DURATION,
                step=1.0
            )

            if self.device.type == "cuda":
                sliding_inference.to(self.device)

            embeddings = sliding_inference(tmp_file_path)

            # 返回平均embedding
            if len(embeddings) > 0:
                return np.mean(embeddings.data, axis=0)
            else:
                return self.extract_voiceprint(audio_data, sample_rate)

        except Exception:
            # 如果滑动窗口失败，回退到整体提取
            return self.extract_voiceprint(audio_data, sample_rate)
        finally:
            # 确保清理临时文件
            if tmp_file_path and os.path.exists(tmp_file_path):
                try:
                    time.sleep(0.1)
                    os.unlink(tmp_file_path)
                except:
                    pass

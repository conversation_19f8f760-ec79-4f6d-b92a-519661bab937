#!/usr/bin/env python3
"""
完整测试快速输入栏功能
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from settings_dialog import ConfigManager
from text_overlay import TextDisplayManager
from llm_client import LLMClient


class TestMainWindow(QMainWindow):
    """测试主窗口，模拟真实的Live2D主窗口"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.llm_client = LLMClient(self.config_manager)
        self.init_ui()
        self.init_text_manager()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("快速输入栏完整功能测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("快速输入栏功能测试")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备就绪")
        self.status_label.setStyleSheet("color: green; margin: 5px;")
        layout.addWidget(self.status_label)
        
        # 当前预设显示
        self.preset_label = QLabel("当前预设: 未知")
        self.preset_label.setStyleSheet("color: blue; margin: 5px;")
        layout.addWidget(self.preset_label)
        
        # 测试按钮
        self.test_btn = QPushButton("显示快速输入栏")
        self.test_btn.clicked.connect(self.show_quick_input)
        self.test_btn.setStyleSheet("padding: 10px; font-size: 14px;")
        layout.addWidget(self.test_btn)
        
        # 消息显示按钮
        self.msg_btn = QPushButton("显示测试消息")
        self.msg_btn.clicked.connect(self.show_test_message)
        self.msg_btn.setStyleSheet("padding: 10px; font-size: 14px;")
        layout.addWidget(self.msg_btn)
        
        # 预设测试按钮
        self.preset_test_btn = QPushButton("测试预设切换")
        self.preset_test_btn.clicked.connect(self.test_preset_switching)
        self.preset_test_btn.setStyleSheet("padding: 10px; font-size: 14px;")
        layout.addWidget(self.preset_test_btn)
        
        # 更新当前预设显示
        self.update_preset_display()
    
    def init_text_manager(self):
        """初始化文本管理器"""
        self.text_display_manager = TextDisplayManager(self)
        self.text_display_manager.connect_signals(
            message_handler=self.on_message_received,
            preset_handler=self.on_preset_changed
        )
    
    def update_preset_display(self):
        """更新预设显示"""
        if hasattr(self.text_display_manager.quick_input, 'current_preset_id'):
            preset_id = self.text_display_manager.quick_input.current_preset_id
            presets = self.config_manager.config.get("llm_presets", {})
            preset = presets.get(preset_id, {})
            preset_name = preset.get("name", preset_id)
            self.preset_label.setText(f"当前预设: {preset_name}")
    
    def show_quick_input(self):
        """显示快速输入栏"""
        print("显示快速输入栏")
        self.status_label.setText("状态: 显示快速输入栏")
        self.text_display_manager.show_quick_input()
    
    def show_test_message(self):
        """显示测试消息"""
        self.status_label.setText("状态: 显示测试消息")
        self.text_display_manager.show_message("这是一个测试消息，用来验证文本显示功能是否正常工作。支持打字机效果和自动换行。")
    
    def test_preset_switching(self):
        """测试预设切换"""
        self.status_label.setText("状态: 测试预设切换")
        presets = self.config_manager.config.get("llm_presets", {})
        
        # 切换到不同的预设
        current_preset = self.text_display_manager.quick_input.current_preset_id
        preset_ids = list(presets.keys())
        
        if len(preset_ids) > 1:
            # 找到下一个预设
            current_index = preset_ids.index(current_preset) if current_preset in preset_ids else 0
            next_index = (current_index + 1) % len(preset_ids)
            next_preset = preset_ids[next_index]
            
            # 切换预设
            self.text_display_manager.quick_input.set_current_preset(next_preset)
            self.on_preset_changed(next_preset)
        else:
            self.status_label.setText("状态: 只有一个预设，无法切换")
    
    def on_message_received(self, message: str):
        """处理接收到的消息"""
        print(f"收到消息: {message}")
        self.status_label.setText(f"状态: 收到消息 - {message[:20]}...")
        
        # 使用LLM处理消息
        if self.llm_client and self.llm_client.is_configured():
            try:
                response = self.llm_client.chat(message)
                if response and not response.startswith("❌"):
                    self.text_display_manager.show_message(response)
                    self.status_label.setText("状态: AI回复已显示")
                else:
                    self.text_display_manager.show_message(f"❌ 处理失败: {response}")
                    self.status_label.setText("状态: AI回复失败")
            except Exception as e:
                error_msg = f"❌ 处理异常: {e}"
                self.text_display_manager.show_message(error_msg)
                self.status_label.setText("状态: 处理异常")
        else:
            self.text_display_manager.show_message("❌ LLM客户端未配置")
            self.status_label.setText("状态: LLM未配置")
    
    def on_preset_changed(self, preset_id: str):
        """处理预设切换"""
        print(f"预设切换到: {preset_id}")
        self.status_label.setText(f"状态: 预设切换到 {preset_id}")
        
        # 更新LLM客户端配置
        presets = self.config_manager.config.get("llm_presets", {})
        preset = presets.get(preset_id, {})
        
        if preset:
            # 构建配置更新
            config_update = {}
            
            # 处理API配置
            preset_api_config = preset.get("api_config", {})
            if preset_api_config:
                api_config_update = self.llm_client.api_config.copy()
                api_config_update.update(preset_api_config)
                config_update["api_config"] = api_config_update
            
            # 处理系统提示词
            system_prompt = preset.get("system_prompt", "")
            if system_prompt:
                config_update["conversation_settings"] = {
                    "system_prompt": system_prompt,
                    "max_history_length": self.llm_client.conversation_settings.get("max_history_length", 20),
                    "save_history": self.llm_client.conversation_settings.get("save_history", True)
                }
            
            # 处理默认参数
            if "temperature" in preset_api_config:
                default_params_update = self.llm_client.default_params.copy()
                if "temperature" in preset_api_config:
                    default_params_update["temperature"] = preset_api_config["temperature"]
                if "max_tokens" in preset_api_config:
                    default_params_update["max_tokens"] = preset_api_config["max_tokens"]
                config_update["default_params"] = default_params_update
            
            # 应用配置更新
            self.llm_client.update_config(config_update)
            self.llm_client.clear_history()
            
            preset_name = preset.get("name", preset_id)
            self.text_display_manager.show_message(f"已切换到预设: {preset_name}")
            self.update_preset_display()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = TestMainWindow()
    window.show()
    
    print("快速输入栏完整功能测试程序启动")
    print("功能说明:")
    print("1. 点击'显示快速输入栏'测试快速输入功能")
    print("2. 在快速输入栏中可以选择不同的预设")
    print("3. 输入消息会使用当前预设进行AI对话")
    print("4. 点击'显示测试消息'测试文本显示功能")
    print("5. 点击'测试预设切换'自动切换预设")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

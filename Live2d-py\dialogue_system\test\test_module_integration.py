#!/usr/bin/env python3
"""
对话系统模块集成测试
验证重构后的模块是否正常工作
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

# 添加正确的路径
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'dev'))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心组件导入
        from dialogue_system.core.llm_client import LLMClient
        from dialogue_system.core.preset_manager import PresetManager
        from dialogue_system.core.chat_dialog import ChatDialog, ChatWorker
        from dialogue_system.core.response_handler import ChatResponseHandler
        print("✅ 核心组件导入成功")
        
        # 测试UI组件导入
        from dialogue_system.ui.text_overlay import TextOverlay, TextDisplayManager
        from dialogue_system.ui.quick_input import QuickInputOverlay
        print("✅ UI组件导入成功")
        
        # 测试统一接口导入
        from dialogue_system import (LLMClient, PresetManager, ChatDialog, 
                                   TextDisplayManager, DialogueSystemManager)
        print("✅ 统一接口导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        from settings_dialog import ConfigManager
        from dialogue_system import LLMClient, PresetManager
        
        # 测试配置管理器
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 测试LLM客户端
        llm_client = LLMClient(config_manager)
        print("✅ LLM客户端创建成功")
        
        # 测试预设管理器
        preset_manager = PresetManager(config_manager)
        presets = preset_manager.get_all_presets()
        print(f"✅ 预设管理器创建成功，找到 {len(presets)} 个预设")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_dialogue_system_manager():
    """测试对话系统管理器"""
    print("\n🔍 测试对话系统管理器...")
    
    try:
        from settings_dialog import ConfigManager
        from dialogue_system import DialogueSystemManager
        
        config_manager = ConfigManager()
        
        # 创建对话系统管理器（无UI）
        dialogue_manager = DialogueSystemManager(config_manager)
        print("✅ 对话系统管理器创建成功")
        
        # 测试配置检查
        is_configured = dialogue_manager.is_configured()
        print(f"✅ 配置检查: {'已配置' if is_configured else '未配置'}")
        
        # 测试预设获取
        current_preset = dialogue_manager.get_current_preset()
        preset_name = current_preset.get("name", "未知") if current_preset else "无"
        print(f"✅ 当前预设: {preset_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话系统管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


class IntegrationTestWindow(QMainWindow):
    """集成测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_dialogue_system()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("对话系统集成测试")
        self.setGeometry(100, 100, 500, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("状态: 初始化完成")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_manager_btn = QPushButton("测试对话系统管理器")
        self.test_manager_btn.clicked.connect(self.test_dialogue_manager)
        layout.addWidget(self.test_manager_btn)
        
        self.test_quick_input_btn = QPushButton("测试快速输入")
        self.test_quick_input_btn.clicked.connect(self.test_quick_input)
        layout.addWidget(self.test_quick_input_btn)
        
        self.test_chat_dialog_btn = QPushButton("测试对话界面")
        self.test_chat_dialog_btn.clicked.connect(self.test_chat_dialog)
        layout.addWidget(self.test_chat_dialog_btn)
        
        self.test_message_btn = QPushButton("测试消息显示")
        self.test_message_btn.clicked.connect(self.test_message_display)
        layout.addWidget(self.test_message_btn)
    
    def init_dialogue_system(self):
        """初始化对话系统"""
        try:
            from settings_dialog import ConfigManager
            from dialogue_system import DialogueSystemManager
            
            self.config_manager = ConfigManager()
            self.dialogue_manager = DialogueSystemManager(self.config_manager, self)
            
            self.status_label.setText("状态: 对话系统初始化成功")
            print("✅ 对话系统初始化成功")
            
        except Exception as e:
            self.status_label.setText(f"状态: 对话系统初始化失败 - {e}")
            print(f"❌ 对话系统初始化失败: {e}")
    
    def test_dialogue_manager(self):
        """测试对话系统管理器"""
        try:
            is_configured = self.dialogue_manager.is_configured()
            current_preset = self.dialogue_manager.get_current_preset()
            preset_name = current_preset.get("name", "未知") if current_preset else "无"
            
            self.status_label.setText(f"状态: 配置状态={is_configured}, 当前预设={preset_name}")
            print(f"✅ 对话管理器测试成功: 配置={is_configured}, 预设={preset_name}")
            
        except Exception as e:
            self.status_label.setText(f"状态: 对话管理器测试失败 - {e}")
            print(f"❌ 对话管理器测试失败: {e}")
    
    def test_quick_input(self):
        """测试快速输入"""
        try:
            self.dialogue_manager.show_quick_input()
            self.status_label.setText("状态: 快速输入显示成功")
            print("✅ 快速输入测试成功")
            
        except Exception as e:
            self.status_label.setText(f"状态: 快速输入测试失败 - {e}")
            print(f"❌ 快速输入测试失败: {e}")
    
    def test_chat_dialog(self):
        """测试对话界面"""
        try:
            chat_dialog = self.dialogue_manager.show_chat_dialog()
            self.status_label.setText("状态: 对话界面显示成功")
            print("✅ 对话界面测试成功")
            
        except Exception as e:
            self.status_label.setText(f"状态: 对话界面测试失败 - {e}")
            print(f"❌ 对话界面测试失败: {e}")
    
    def test_message_display(self):
        """测试消息显示"""
        try:
            self.dialogue_manager.show_message("这是一个测试消息，验证重构后的模块是否正常工作。")
            self.status_label.setText("状态: 消息显示成功")
            print("✅ 消息显示测试成功")
            
        except Exception as e:
            self.status_label.setText(f"状态: 消息显示测试失败 - {e}")
            print(f"❌ 消息显示测试失败: {e}")


def run_console_tests():
    """运行控制台测试"""
    print("🚀 开始对话系统模块集成测试...")
    print("=" * 60)
    
    # 测试1: 模块导入
    import_result = test_imports()
    
    # 测试2: 基本功能
    basic_result = test_basic_functionality()
    
    # 测试3: 对话系统管理器
    manager_result = test_dialogue_system_manager()
    
    print("\n" + "=" * 60)
    print("📊 控制台测试结果:")
    print(f"- 模块导入: {'✅ 成功' if import_result else '❌ 失败'}")
    print(f"- 基本功能: {'✅ 成功' if basic_result else '❌ 失败'}")
    print(f"- 对话系统管理器: {'✅ 成功' if manager_result else '❌ 失败'}")
    
    return import_result and basic_result and manager_result


def main():
    """主函数"""
    # 先运行控制台测试
    console_success = run_console_tests()
    
    if console_success:
        print("\n🎉 控制台测试全部通过！")
        print("现在启动GUI测试...")
        
        # 启动GUI测试
        app = QApplication(sys.argv)
        
        window = IntegrationTestWindow()
        window.show()
        
        print("\n📋 GUI测试说明:")
        print("- 测试对话系统管理器: 验证管理器基本功能")
        print("- 测试快速输入: 验证快速输入组件")
        print("- 测试对话界面: 验证对话界面组件")
        print("- 测试消息显示: 验证文本显示组件")
        
        sys.exit(app.exec())
    else:
        print("\n❌ 控制台测试失败，请检查模块配置")
        return False


if __name__ == "__main__":
    main()

import json
import numpy as np
from datetime import datetime
from pathlib import Path
from config import Config

class IdentityMemory:
    def __init__(self):
        self.db_path = Path(Config.IDENTITY_DB_PATH)
        self.identities = self._load_database()
    
    def _load_database(self):
        """加载身份数据库"""
        if self.db_path.exists():
            try:
                with open(self.db_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 将embedding从列表转回numpy数组
                    for identity in data.values():
                        identity['embedding'] = np.array(identity['embedding'])
                    return data
            except Exception as e:
                print(f"数据库加载失败: {e}")
                return {}
        return {}
    
    def _save_database(self):
        """保存身份数据库"""
        try:
            # 将numpy数组转为列表以便JSON序列化
            data_to_save = {}
            for user_id, identity in self.identities.items():
                data_to_save[user_id] = {
                    'name': identity['name'],
                    'embedding': identity['embedding'].tolist(),
                    'conversation_history': identity['conversation_history'],
                    'preferences': identity['preferences'],
                    'created_at': identity['created_at'],
                    'last_seen': identity['last_seen'],
                    'interaction_count': identity['interaction_count']
                }
            
            with open(self.db_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"数据库保存失败: {e}")
    
    def register_identity(self, name, embedding):
        """注册新身份"""
        user_id = f"user_{len(self.identities) + 1:03d}"
        
        self.identities[user_id] = {
            'name': name,
            'embedding': embedding,
            'conversation_history': [],
            'preferences': {},
            'created_at': datetime.now().isoformat(),
            'last_seen': datetime.now().isoformat(),
            'interaction_count': 1
        }
        
        self._save_database()
        return user_id
    
    def identify_speaker(self, embedding, speaker_recognition):
        """识别说话人"""
        best_match = None
        best_similarity = 0.0
        
        for user_id, identity in self.identities.items():
            similarity = speaker_recognition.compute_similarity(
                embedding, identity['embedding']
            )
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = user_id
        
        if best_similarity >= Config.SIMILARITY_THRESHOLD:
            # 更新最后见面时间和交互次数
            self.identities[best_match]['last_seen'] = datetime.now().isoformat()
            self.identities[best_match]['interaction_count'] += 1
            self._save_database()
            return best_match, best_similarity
        
        return None, best_similarity
    
    def update_conversation(self, user_id, user_message, ai_response):
        """更新对话历史"""
        if user_id in self.identities:
            self.identities[user_id]['conversation_history'].append({
                'timestamp': datetime.now().isoformat(),
                'user': user_message,
                'ai': ai_response
            })
            
            # 只保留最近20条对话
            if len(self.identities[user_id]['conversation_history']) > 20:
                self.identities[user_id]['conversation_history'] = \
                    self.identities[user_id]['conversation_history'][-20:]
            
            self._save_database()
    
    def get_identity_info(self, user_id):
        """获取身份信息"""
        return self.identities.get(user_id, None)
    
    def update_preferences(self, user_id, preferences):
        """更新用户偏好"""
        if user_id in self.identities:
            self.identities[user_id]['preferences'].update(preferences)
            self._save_database()
    
    def get_conversation_context(self, user_id, limit=5):
        """获取对话上下文"""
        if user_id not in self.identities:
            return []
        
        history = self.identities[user_id]['conversation_history']
        return history[-limit:] if history else []

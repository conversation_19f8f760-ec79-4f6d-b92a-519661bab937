#!/usr/bin/env python3
"""
完整测试快速输入栏功能
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt

# 添加正确的路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'dev'))

from settings_dialog import ConfigManager
from dialogue_system.ui.text_overlay import TextDisplayManager
from dialogue_system.core.llm_client import LLMClient


class TestMainWindow(QMainWindow):
    """测试主窗口，模拟真实的Live2D主窗口"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.llm_client = LLMClient(self.config_manager)
        self.init_ui()
        self.init_text_manager()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("完整快速输入栏测试")
        self.setGeometry(100, 100, 500, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("状态: 初始化完成")
        layout.addWidget(self.status_label)
        
        # 预设标签
        self.preset_label = QLabel("当前预设: 默认")
        layout.addWidget(self.preset_label)
        
        # 测试按钮
        self.quick_input_btn = QPushButton("显示快速输入栏")
        self.quick_input_btn.clicked.connect(self.show_quick_input)
        layout.addWidget(self.quick_input_btn)
        
        self.test_message_btn = QPushButton("显示测试消息")
        self.test_message_btn.clicked.connect(self.show_test_message)
        layout.addWidget(self.test_message_btn)
        
        self.test_llm_btn = QPushButton("测试LLM连接")
        self.test_llm_btn.clicked.connect(self.test_llm_connection)
        layout.addWidget(self.test_llm_btn)
        
        # 更新预设显示
        self.update_preset_display()
    
    def init_text_manager(self):
        """初始化文本管理器"""
        self.text_display_manager = TextDisplayManager(self)
        self.text_display_manager.connect_signals(
            message_handler=self.on_message_received,
            preset_handler=self.on_preset_changed
        )
    
    def update_preset_display(self):
        """更新预设显示"""
        if hasattr(self.text_display_manager.quick_input, 'current_preset_id'):
            preset_id = self.text_display_manager.quick_input.current_preset_id
            presets = self.config_manager.config.get("llm_presets", {})
            preset = presets.get(preset_id, {})
            preset_name = preset.get("name", preset_id)
            self.preset_label.setText(f"当前预设: {preset_name}")
    
    def show_quick_input(self):
        """显示快速输入栏"""
        print("显示快速输入栏")
        self.status_label.setText("状态: 显示快速输入栏")
        self.text_display_manager.show_quick_input()
    
    def show_test_message(self):
        """显示测试消息"""
        print("显示测试消息")
        self.status_label.setText("状态: 显示测试消息")
        self.text_display_manager.show_message("这是一个测试消息，用来验证文本显示功能是否正常工作。")
    
    def test_llm_connection(self):
        """测试LLM连接"""
        print("测试LLM连接")
        self.status_label.setText("状态: 测试LLM连接中...")
        
        if self.llm_client.is_configured():
            try:
                response = self.llm_client.chat("Hello")
                if response and not response.startswith("❌"):
                    self.status_label.setText("状态: LLM连接成功")
                    self.text_display_manager.show_message("LLM连接测试成功！")
                else:
                    self.status_label.setText("状态: LLM连接失败")
                    self.text_display_manager.show_message(f"LLM连接失败: {response}")
            except Exception as e:
                self.status_label.setText("状态: LLM连接异常")
                self.text_display_manager.show_message(f"LLM连接异常: {e}")
        else:
            self.status_label.setText("状态: LLM未配置")
            self.text_display_manager.show_message("LLM客户端未配置，请检查设置")
    
    def on_message_received(self, message: str):
        """处理接收到的消息"""
        print(f"收到消息: {message}")
        self.status_label.setText(f"状态: 收到消息 - {message[:20]}...")
        
        # 使用LLM处理消息
        if self.llm_client and self.llm_client.is_configured():
            try:
                response = self.llm_client.chat(message)
                if response and not response.startswith("❌"):
                    self.text_display_manager.show_message(response)
                    self.status_label.setText("状态: AI回复已显示")
                else:
                    self.text_display_manager.show_message(f"❌ 处理失败: {response}")
                    self.status_label.setText("状态: AI回复失败")
            except Exception as e:
                error_msg = f"❌ 处理异常: {e}"
                self.text_display_manager.show_message(error_msg)
                self.status_label.setText("状态: 处理异常")
        else:
            self.text_display_manager.show_message("❌ LLM客户端未配置")
            self.status_label.setText("状态: LLM未配置")
    
    def on_preset_changed(self, preset_id: str):
        """处理预设切换"""
        print(f"预设切换到: {preset_id}")
        self.status_label.setText(f"状态: 预设切换到 {preset_id}")
        
        # 更新预设显示
        self.update_preset_display()
        
        # 显示切换消息
        presets = self.config_manager.config.get("llm_presets", {})
        preset = presets.get(preset_id, {})
        preset_name = preset.get("name", preset_id)
        self.text_display_manager.show_message(f"预设已切换到: {preset_name}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    window = TestMainWindow()
    window.show()
    
    print("完整快速输入栏测试窗口已启动")
    print("功能说明:")
    print("- 显示快速输入栏: 测试快速输入功能")
    print("- 显示测试消息: 测试文本显示功能")
    print("- 测试LLM连接: 测试LLM客户端连接")
    print("- 快速输入栏支持预设切换和消息发送")
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

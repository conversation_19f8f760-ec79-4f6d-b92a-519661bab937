import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from async_role_agent import AsyncRoleAgent

async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试异步角色对话系统...")
    
    # 创建代理
    agent = AsyncRoleAgent()
    
    # 设置消息回调
    def message_callback(role, content):
        print(f"[回调] {role}: {content}")
    
    agent.set_message_callback(message_callback)
    
    try:
        # 启动代理
        print("\n1. 启动代理...")
        await agent.start()
        
        # 测试普通聊天
        print("\n2. 测试普通聊天...")
        response = await agent.chat("你好！")
        print(f"回应: {response}")
        
        # 测试时间查询（即时）
        print("\n3. 测试时间查询...")
        response = await agent.chat("现在几点了？")
        print(f"回应: {response}")
        
        # 测试天气查询（延迟）
        print("\n4. 测试天气查询（会有延迟）...")
        response = await agent.chat("北京今天天气怎么样？")
        print(f"回应: {response}")
        
        # 等待一段时间，看看主动对话
        print("\n5. 等待主动对话（10秒）...")
        await asyncio.sleep(10)
        
        # 查看状态
        print("\n6. 查看系统状态...")
        status = agent.get_status()
        print(f"状态: {status}")
        
        # 继续聊天
        print("\n7. 继续聊天...")
        response = await agent.chat("你刚才在做什么？")
        print(f"回应: {response}")
        
        # 等待更长时间，看看任务完成
        print("\n8. 等待任务完成（60秒）...")
        await asyncio.sleep(60)
        
        # 最后聊天
        print("\n9. 最后聊天...")
        response = await agent.chat("有什么新消息吗？")
        print(f"回应: {response}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 停止代理
        print("\n10. 停止代理...")
        await agent.stop()
        print("✅ 测试完成！")

async def test_quick_functionality():
    """快速功能测试（不等待长时间）"""
    print("⚡ 快速功能测试...")
    
    agent = AsyncRoleAgent()
    
    def message_callback(role, content):
        print(f"[{role}] {content}")
    
    agent.set_message_callback(message_callback)
    
    try:
        await agent.start()
        
        # 测试各种功能
        test_cases = [
            "你好！",
            "现在几点了？",
            "今天是星期几？",
            "北京天气怎么样？",
            "你在做什么？",
            "聊聊别的吧"
        ]
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n--- 测试 {i}: {test_input} ---")
            response = await agent.chat(test_input)
            print(f"回应: {response}")
            await asyncio.sleep(2)  # 短暂等待
        
        # 查看最终状态
        print("\n--- 最终状态 ---")
        status = agent.get_status()
        print(f"任务统计: {status['task_summary']}")
        print(f"对话统计: {status['conversation_stats']}")
        
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await agent.stop()
        print("✅ 快速测试完成！")

def main():
    """主函数"""
    print("🌟 异步角色对话系统测试")
    print("选择测试模式:")
    print("1. 完整测试（包含60秒等待）")
    print("2. 快速测试（不等待长时间）")
    
    try:
        choice = input("请选择 (1/2): ").strip()
        
        if choice == "1":
            asyncio.run(test_basic_functionality())
        elif choice == "2":
            asyncio.run(test_quick_functionality())
        else:
            print("无效选择，运行快速测试...")
            asyncio.run(test_quick_functionality())
            
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"测试出错: {e}")

if __name__ == "__main__":
    main()

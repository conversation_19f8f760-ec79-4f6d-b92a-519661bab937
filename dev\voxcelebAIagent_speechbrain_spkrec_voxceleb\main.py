from agent import VoxCelebAgent
import sys

def main():
    print("🎤 VoxCeleb说话人识别AI助手")
    print("=" * 50)
    
    try:
        agent = VoxCelebAgent()
    except Exception as e:
        print(f"初始化失败: {e}")
        return
    
    print("\n可用命令:")
    print("1. 'voice' - 语音对话（录音+识别+对话）")
    print("2. 'text <用户名>' - 文本对话")
    print("3. 'list' - 查看已注册用户")
    print("4. 'stats <用户名>' - 查看用户统计")
    print("5. 'test' - 测试音频设备")
    print("6. 'quit' - 退出")
    
    while True:
        try:
            command = input("\n请输入命令: ").strip().lower()
            
            if command == 'quit':
                break
            elif command == 'voice':
                print("\n--- 语音对话模式 ---")
                response = agent.process_audio_input()
                print(f"\nAI: {response}")
                
            elif command.startswith('text'):
                parts = command.split(' ', 1)
                user_name = parts[1] if len(parts) > 1 else None
                user_message = input("请输入消息: ").strip()
                if user_message:
                    response = agent.text_only_chat(user_message, user_name)
                    print(f"\nAI: {response}")
                    
            elif command == 'list':
                print(agent.list_identities())
                
            elif command.startswith('stats'):
                parts = command.split(' ', 1)
                if len(parts) > 1:
                    print(agent.get_user_stats(parts[1]))
                else:
                    print("请指定用户名: stats <用户名>")
                    
            elif command == 'test':
                print("\n--- 音频设备测试 ---")
                devices = agent.audio_processor.get_audio_devices()
                print("可用输入设备:")
                for device in devices:
                    print(f"  {device['id']}: {device['name']}")
                
                agent.audio_processor.test_audio_input()
                
            else:
                print("未知命令，请重新输入")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"错误: {e}")
    
    print("\n再见！")

if __name__ == "__main__":
    main()

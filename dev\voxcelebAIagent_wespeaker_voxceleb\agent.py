from voiceprint_recognition import VoiceprintRecognition
from identity_memory import VoiceprintMemory
from audio_processor import VoiceprintAudioProcessor
from llm_client import VoiceprintLLMClient

class VoiceprintIdolAgent:
    def __init__(self):
        print("🎤 初始化声纹识别偶像AI助手...")

        print("📊 加载声纹识别模型...")
        self.voiceprint_recognition = VoiceprintRecognition()

        print("💾 初始化身份记忆系统...")
        self.voiceprint_memory = VoiceprintMemory()

        print("🎵 设置音频处理器...")
        self.audio_processor = VoiceprintAudioProcessor()

        print("🤖 连接LLM客户端...")
        self.llm_client = VoiceprintLLMClient()

        print("✨ 声纹偶像助手初始化完成！")
    
    def voice_chat(self, user_message=None):
        """完整的声纹识别对话流程"""
        try:
            # 录制声纹样本
            audio_data = self.audio_processor.record_with_voice_guidance()
            
            # 分析音频质量
            quality_info = self.audio_processor.analyze_audio_quality(audio_data)
            print(f"🎵 音频质量: {quality_info['quality']} - {quality_info['message']}")
            
            if quality_info['quality'] == 'very_low':
                return "😅 音频质量太低了呢，请重新录制一下哦～"
            
            # 预处理音频
            processed_audio = self.voiceprint_recognition.preprocess_audio(
                audio_data, self.audio_processor.sample_rate
            )
            
            # 提取声纹特征
            voiceprint = self.voiceprint_recognition.extract_voiceprint(processed_audio)
            
            # 识别声纹身份
            user_id, similarity = self.voiceprint_memory.identify_voice(
                voiceprint, self.voiceprint_recognition
            )
            
            if user_id:
                # 已知用户
                identity_info = self.voiceprint_memory.get_identity_info(user_id)
                print(f"🎵 识别到声音: {identity_info['name']} (置信度: {similarity:.3f})")
                
                # 获取对话上下文
                conversation_context = self.voiceprint_memory.get_conversation_context(user_id)
                
            else:
                # 新用户
                print(f"🌟 检测到新的声音！(最高相似度: {similarity:.3f})")
                name = input("💖 请告诉我您的名字: ").strip()
                if not name:
                    name = f"神秘声音{len(self.voiceprint_memory.identities) + 1}"
                
                # 注册新声纹
                user_id = self.voiceprint_memory.register_voiceprint(
                    name, voiceprint, quality_info
                )
                identity_info = self.voiceprint_memory.get_identity_info(user_id)
                conversation_context = []
                print(f"✨ 已记住 {name} 的声音！")
            
            # 获取用户文本输入
            if not user_message:
                user_message = input("💬 请说出您想聊的内容: ").strip()
                if not user_message:
                    return "请输入有效的消息哦～"
            
            # 生成个性化AI回复
            ai_response = self.llm_client.chat_with_voiceprint_identity(
                user_message, identity_info, conversation_context
            )
            
            # 分析用户性格（如果有足够的对话历史）
            if len(conversation_context) >= 3:
                voice_stats = self.voiceprint_memory.get_voice_statistics(user_id)
                personality_analysis = self.llm_client.analyze_personality(
                    conversation_context, voice_stats
                )
                if personality_analysis:
                    self.voiceprint_memory.update_personality_profile(user_id, personality_analysis)
            
            # 更新对话历史
            self.voiceprint_memory.update_conversation(
                user_id, user_message, ai_response, quality_info
            )
            
            return ai_response
            
        except Exception as e:
            return f"😅 处理失败了呢: {str(e)}"
    
    def register_high_quality_voice(self, name=None):
        """注册高质量声纹样本"""
        try:
            if not name:
                name = input("💖 请输入您的姓名: ").strip()
                if not name:
                    name = f"用户{len(self.voiceprint_memory.identities) + 1}"
            
            print(f"🎤 为 {name} 录制高质量声纹样本...")
            
            # 录制5秒高质量样本
            audio_data = self.audio_processor.record_long_sample(5)
            
            # 分析音频质量
            quality_info = self.audio_processor.analyze_audio_quality(audio_data)
            print(f"📊 音频质量评估: {quality_info['quality']}")
            
            if quality_info['quality'] == 'very_low':
                return "😅 音频质量不够好呢，建议重新录制～"
            
            # 预处理和提取声纹
            processed_audio = self.voiceprint_recognition.preprocess_audio(
                audio_data, self.audio_processor.sample_rate
            )
            voiceprint = self.voiceprint_recognition.extract_sliding_window_embeddings(processed_audio)
            
            # 注册声纹
            user_id = self.voiceprint_memory.register_voiceprint(
                name, voiceprint, quality_info
            )
            
            return f"✨ {name} 的高质量声纹已成功注册！ID: {user_id}"
            
        except Exception as e:
            return f"😅 注册失败: {str(e)}"
    
    def voice_analysis_chat(self, user_name):
        """基于已知声纹的文本对话"""
        try:
            # 查找用户
            user_id = None
            for uid, identity in self.voiceprint_memory.identities.items():
                if identity['name'].lower() == user_name.lower():
                    user_id = uid
                    break
            
            if not user_id:
                return f"😅 没有找到 {user_name} 的声纹记录呢～"
            
            identity_info = self.voiceprint_memory.get_identity_info(user_id)
            conversation_context = self.voiceprint_memory.get_conversation_context(user_id)
            voice_stats = self.voiceprint_memory.get_voice_statistics(user_id)
            
            print(f"🎵 找到 {identity_info['name']} 的声纹档案")
            print(f"📊 声纹统计: 平均置信度 {voice_stats['average_confidence']:.3f}")
            
            user_message = input("💬 请输入消息: ").strip()
            if not user_message:
                return "请输入有效的消息哦～"
            
            # 生成回复
            ai_response = self.llm_client.chat_with_voiceprint_identity(
                user_message, identity_info, conversation_context
            )
            
            # 更新对话历史
            self.voiceprint_memory.update_conversation(user_id, user_message, ai_response)
            
            return ai_response
            
        except Exception as e:
            return f"😅 对话失败: {str(e)}"
    
    def list_voice_identities(self):
        """列出所有声纹身份"""
        if not self.voiceprint_memory.identities:
            return "🎵 还没有录制任何声纹样本呢～"
        
        result = "🎤 已注册的声纹身份:\n"
        for user_id, identity in self.voiceprint_memory.identities.items():
            voice_stats = self.voiceprint_memory.get_voice_statistics(user_id)
            result += f"✨ {identity['name']} (ID: {user_id})\n"
            result += f"   🎵 交互次数: {identity['interaction_count']}\n"
            result += f"   📊 平均置信度: {voice_stats['average_confidence']:.3f}\n"
            result += f"   🎭 性格特征: {identity['personality_profile']['communication_style']}\n\n"
        
        return result
    
    def get_voice_profile(self, user_name):
        """获取详细的声纹档案"""
        for user_id, identity in self.voiceprint_memory.identities.items():
            if identity['name'].lower() == user_name.lower():
                voice_stats = self.voiceprint_memory.get_voice_statistics(user_id)
                personality = identity['personality_profile']
                
                return f"""
🎤 {identity['name']} 的声纹档案:
📅 注册时间: {identity['created_at']}
🕐 最后交互: {identity['last_interaction']}
🔢 交互次数: {identity['interaction_count']}
📊 平均识别置信度: {voice_stats['average_confidence']:.3f}
📈 声纹稳定性: {voice_stats['voice_stability']:.3f}
🎭 交流风格: {personality['communication_style']}
💖 兴趣爱好: {', '.join(personality['interests']) if personality['interests'] else '还在发现中'}
💬 对话历史: {len(identity['conversation_history'])}条
"""
        return f"😅 没有找到 {user_name} 的声纹记录呢～"

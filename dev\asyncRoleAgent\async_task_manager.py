import asyncio
import time
import uuid
from typing import Dict, Any, Optional
from enum import Enum

class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

class TaskType(Enum):
    WEATHER = "weather"
    TIME = "time"
    FORECAST = "forecast"

class AsyncTaskManager:
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.task_counter = 0
        self.completed_tasks_queue = asyncio.Queue()  # 用于通知任务完成
        
    def generate_task_id(self, task_type: str) -> str:
        """生成唯一任务ID"""
        self.task_counter += 1
        return f"{task_type}_{self.task_counter}_{uuid.uuid4().hex[:8]}"
    
    async def start_weather_query(self, city: str, delay_seconds: int = 60) -> str:
        """启动天气查询任务（带延迟模拟）"""
        task_id = self.generate_task_id("weather")
        
        self.tasks[task_id] = {
            "id": task_id,
            "type": TaskType.WEATHER,
            "status": TaskStatus.PENDING,
            "city": city,
            "start_time": time.time(),
            "delay_seconds": delay_seconds,
            "result": None,
            "error": None
        }
        
        # 启动异步任务
        asyncio.create_task(self._execute_weather_query(task_id, city, delay_seconds))
        
        return task_id
    
    async def start_forecast_query(self, city: str, days: int = 3, delay_seconds: int = 60) -> str:
        """启动天气预报查询任务"""
        task_id = self.generate_task_id("forecast")
        
        self.tasks[task_id] = {
            "id": task_id,
            "type": TaskType.FORECAST,
            "status": TaskStatus.PENDING,
            "city": city,
            "days": days,
            "start_time": time.time(),
            "delay_seconds": delay_seconds,
            "result": None,
            "error": None
        }
        
        asyncio.create_task(self._execute_forecast_query(task_id, city, days, delay_seconds))
        
        return task_id
    
    async def start_time_query(self, format_type: str = "detailed") -> str:
        """启动时间查询任务（即时完成）"""
        task_id = self.generate_task_id("time")
        
        self.tasks[task_id] = {
            "id": task_id,
            "type": TaskType.TIME,
            "status": TaskStatus.PENDING,
            "format_type": format_type,
            "start_time": time.time(),
            "delay_seconds": 0,
            "result": None,
            "error": None
        }
        
        # 时间查询立即执行
        asyncio.create_task(self._execute_time_query(task_id, format_type))
        
        return task_id
    
    async def _execute_weather_query(self, task_id: str, city: str, delay_seconds: int):
        """执行天气查询（带延迟）"""
        try:
            self.tasks[task_id]["status"] = TaskStatus.RUNNING

            # 模拟延迟
            await asyncio.sleep(delay_seconds)

            # 调用真实的天气服务
            from service_integrator import ServiceIntegrator
            service = ServiceIntegrator()
            result = await service.execute_weather_query(city)

            self.tasks[task_id]["status"] = TaskStatus.COMPLETED
            self.tasks[task_id]["result"] = result
            self.tasks[task_id]["completed_time"] = time.time()

            # 通知任务完成
            await self.completed_tasks_queue.put(task_id)

        except Exception as e:
            self.tasks[task_id]["status"] = TaskStatus.FAILED
            self.tasks[task_id]["error"] = str(e)
    
    async def _execute_forecast_query(self, task_id: str, city: str, days: int, delay_seconds: int):
        """执行天气预报查询"""
        try:
            self.tasks[task_id]["status"] = TaskStatus.RUNNING

            await asyncio.sleep(delay_seconds)

            # 调用真实的预报服务
            from service_integrator import ServiceIntegrator
            service = ServiceIntegrator()
            result = await service.execute_forecast_query(city, days)

            self.tasks[task_id]["status"] = TaskStatus.COMPLETED
            self.tasks[task_id]["result"] = result
            self.tasks[task_id]["completed_time"] = time.time()

            await self.completed_tasks_queue.put(task_id)

        except Exception as e:
            self.tasks[task_id]["status"] = TaskStatus.FAILED
            self.tasks[task_id]["error"] = str(e)
    
    async def _execute_time_query(self, task_id: str, format_type: str):
        """执行时间查询（即时）"""
        try:
            self.tasks[task_id]["status"] = TaskStatus.RUNNING

            # 调用真实的时间服务
            from service_integrator import ServiceIntegrator
            service = ServiceIntegrator()
            result = await service.execute_time_query(format_type)

            self.tasks[task_id]["status"] = TaskStatus.COMPLETED
            self.tasks[task_id]["result"] = result
            self.tasks[task_id]["completed_time"] = time.time()

            await self.completed_tasks_queue.put(task_id)

        except Exception as e:
            self.tasks[task_id]["status"] = TaskStatus.FAILED
            self.tasks[task_id]["error"] = str(e)
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_pending_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有待处理和进行中的任务"""
        return {
            task_id: task for task_id, task in self.tasks.items()
            if task["status"] in [TaskStatus.PENDING, TaskStatus.RUNNING]
        }
    
    def get_completed_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有已完成的任务"""
        return {
            task_id: task for task_id, task in self.tasks.items()
            if task["status"] == TaskStatus.COMPLETED
        }
    
    async def get_next_completed_task(self) -> Optional[str]:
        """获取下一个完成的任务ID（非阻塞）"""
        try:
            return await asyncio.wait_for(self.completed_tasks_queue.get(), timeout=0.1)
        except asyncio.TimeoutError:
            return None
    
    def clear_completed_tasks(self):
        """清理已完成的任务"""
        completed_task_ids = [
            task_id for task_id, task in self.tasks.items()
            if task["status"] == TaskStatus.COMPLETED
        ]
        for task_id in completed_task_ids:
            del self.tasks[task_id]
    
    def get_task_summary(self) -> Dict[str, int]:
        """获取任务统计摘要"""
        summary = {
            "total": len(self.tasks),
            "pending": 0,
            "running": 0,
            "completed": 0,
            "failed": 0
        }
        
        for task in self.tasks.values():
            status = task["status"]
            if status == TaskStatus.PENDING:
                summary["pending"] += 1
            elif status == TaskStatus.RUNNING:
                summary["running"] += 1
            elif status == TaskStatus.COMPLETED:
                summary["completed"] += 1
            elif status == TaskStatus.FAILED:
                summary["failed"] += 1
        
        return summary

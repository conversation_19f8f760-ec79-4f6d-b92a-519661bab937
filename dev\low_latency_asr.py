#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
低延迟语音输入示例程序
使用SenseVoice模型和WebRTC VAD进行实时语音识别
支持富文本、情感检测、事件检测和语言过滤
"""

import pyaudio
import webrtcvad
import numpy as np
import threading
import queue
import time
import re
from collections import deque
from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess


class LowLatencyASR:
    def __init__(self, model_path="D:/huggingface_cache/hub/models--SenseVoice", 
                 sample_rate=16000, frame_duration=30):
        """
        初始化低延迟ASR系统
        
        Args:
            model_path: SenseVoice模型路径
            sample_rate: 采样率，必须是8000, 16000, 32000, 48000之一
            frame_duration: VAD帧长度(ms)，可选10, 20, 30
        """
        self.sample_rate = sample_rate
        self.frame_duration = frame_duration
        self.frame_size = int(sample_rate * frame_duration / 1000)
        self.chunk_size = self.frame_size
        
        # 语言配置
        self.allowed_languages = ["zh", "en", "yue"]  # 允许的语言
        self.blocked_languages = ["ja", "ko"]  # 禁用日语和韩文
        
        # 初始化WebRTC VAD
        self.vad = webrtcvad.Vad(0)  # 敏感度: 0-3, 2为中等敏感度
        
        # 初始化SenseVoice模型（不使用集成VAD）
        print("正在加载SenseVoice模型...")
        self.model = AutoModel(
            model=model_path,
            trust_remote_code=True,
            device="cuda:0" if self._check_cuda() else "cpu",
        )
        print("模型加载完成 - 使用WebRTC VAD")
        
        # 音频缓冲区和VAD状态
        self.audio_buffer = deque(maxlen=int(sample_rate * 5))  # 5秒缓冲
        self.speech_segments = []  # 存储多个语音片段用于批处理
        self.current_segment = []
        self.speech_frames = []
        self.silence_count = 0
        self.speech_count = 0
        
        # 线程控制
        self.is_recording = False
        self.audio_queue = queue.Queue()
        
        # 初始化PyAudio
        self.audio = pyaudio.PyAudio()
        
        # 批处理配置
        self.batch_size_s = 60  # 批处理时长
        self.merge_length_s = 15  # 合并长度
        
    def _check_cuda(self):
        """检查CUDA是否可用"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False
    
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """音频输入回调函数"""
        if self.is_recording:
            self.audio_queue.put(in_data)
        return (None, pyaudio.paContinue)
    
    def _process_audio_frame(self, frame):
        """处理单个音频帧 - 使用WebRTC VAD"""
        # 转换为int16格式用于VAD
        audio_int16 = np.frombuffer(frame, dtype=np.int16)
        
        # WebRTC VAD检测
        is_speech = self.vad.is_speech(frame, self.sample_rate)
        
        if is_speech:
            self.speech_count += 1
            self.silence_count = 0
            self.speech_frames.append(audio_int16)
            
            # 如果是新的语音开始
            if self.speech_count == 1:
                print("🎤 检测到语音开始...")
        else:
            self.silence_count += 1
            
            # 如果有语音数据且静音超过阈值，进行识别
            if self.speech_frames and self.silence_count > 50:  # 1.5秒静音 (50 * 30ms)
                print("🔇 检测到语音结束，开始识别...")
                self._process_speech_segment()
                self.speech_frames = []
                self.speech_count = 0
    
    def _process_speech_segment(self):
        """处理单个语音片段"""
        if not self.speech_frames:
            return
            
        # 合并语音帧
        audio_data = np.concatenate(self.speech_frames)
        
        # 最小长度检查
        if len(audio_data) < self.sample_rate * 0.5:  # 至少0.5秒
            return
            
        # 添加到批处理队列
        self.speech_segments.append(audio_data)
        
        # 如果累积了足够的片段，进行批处理识别
        if len(self.speech_segments) >= 3 or self._get_total_duration() >= 10:
            self._recognize_speech_batch()
    
    def _get_total_duration(self):
        """获取当前片段总时长"""
        total_samples = sum(len(segment) for segment in self.speech_segments)
        return total_samples / self.sample_rate
    
    def _recognize_speech_batch(self):
        """批处理识别多个语音片段"""
        if not self.speech_segments:
            return
            
        try:
            # 合并所有片段
            audio_data = np.concatenate(self.speech_segments)
            audio_float = audio_data.astype(np.float32) / 32768.0
            
            print(f"🔄 批处理识别 {len(self.speech_segments)} 个片段，总时长 {len(audio_data)/self.sample_rate:.1f}秒")
            
            # 使用SenseVoice进行富文本识别
            res = self.model.generate(
                input=audio_float,
                cache={},
                language="auto",  # 自动检测语言
                use_itn=True,     # 启用逆文本正则化
                batch_size_s=self.batch_size_s,  # 动态批处理
                merge_vad=True,   # 合并VAD片段
                merge_length_s=self.merge_length_s,  # 合并长度
                ban_emo_unk=True,  # 启用情感识别
            )
            
            if res and len(res) > 0:
                for i, result in enumerate(res):
                    raw_text = result.get("text", "")
                    if raw_text.strip():
                        # 语言过滤
                        if self._is_blocked_language(raw_text):
                            print(f"🚫 片段{i+1}: 检测到禁用语言，已过滤")
                            continue
                            
                        # 富文本后处理
                        processed_text = rich_transcription_postprocess(raw_text)
                        
                        # 解析情感和事件标签
                        emotion, events, clean_text = self._parse_rich_text(processed_text)
                        
                        # 输出结果
                        print(f"📝 片段{i+1} 识别结果: {clean_text}")
                        if emotion:
                            print(f"😊 情感检测: {emotion}")
                        if events:
                            print(f"🎵 事件检测: {', '.join(events)}")
                        print("-" * 50)
            
            # 清空批处理队列
            self.speech_segments = []
                    
        except Exception as e:
            print(f"❌ 批处理识别错误: {e}")
            self.speech_segments = []
    
    def _is_blocked_language(self, text):
        """检测是否包含被禁用的语言"""
        # 日文检测：平假名、片假名
        if re.search(r'[\u3040-\u309f\u30a0-\u30ff]', text):
            return True
            
        # 韩文检测：韩文字符
        if re.search(r'[\uac00-\ud7af\u1100-\u11ff\u3130-\u318f]', text):
            return True
            
        return False
    
    def _parse_rich_text(self, text):
        """解析富文本中的情感和事件标签"""
        emotion = None
        events = []
        clean_text = text
        
        # 情感标签检测
        emotion_patterns = [
            r'<\|HAPPY\|>', r'<\|SAD\|>', r'<\|ANGRY\|>', r'<\|NEUTRAL\|>',
            r'<\|EXCITED\|>', r'<\|CALM\|>', r'<\|SURPRISED\|>'
        ]
        
        for pattern in emotion_patterns:
            if re.search(pattern, text):
                emotion = pattern.replace('<|', '').replace('|>', '').lower()
                clean_text = re.sub(pattern, '', clean_text)
                break
        
        # 事件标签检测
        event_patterns = [
            (r'<\|MUSIC\|>', '音乐'),
            (r'<\|APPLAUSE\|>', '掌声'),
            (r'<\|LAUGHTER\|>', '笑声'),
            (r'<\|CRYING\|>', '哭声'),
            (r'<\|COUGH\|>', '咳嗽'),
            (r'<\|SNEEZE\|>', '喷嚏'),
            (r'<\|NOISE\|>', '噪声')
        ]
        
        for pattern, event_name in event_patterns:
            if re.search(pattern, text):
                events.append(event_name)
                clean_text = re.sub(pattern, '', clean_text)
        
        # 清理多余空格
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()
        
        return emotion, events, clean_text
    
    def _audio_processing_thread(self):
        """音频处理线程"""
        while self.is_recording:
            try:
                # 获取音频数据，超时1秒
                frame = self.audio_queue.get(timeout=1.0)
                self._process_audio_frame(frame)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ 音频处理错误: {e}")
    
    def start_recording(self):
        """开始录音和识别"""
        if self.is_recording:
            print("⚠️ 已在录音中...")
            return
            
        print("🎤 开始录音，使用WebRTC VAD自动检测...")
        print("📊 支持富文本、情感检测、事件检测")
        print("🚫 已禁用日语和韩语识别")
        print("⚡ 使用WebRTC VAD - 超低延迟模式")
        print("按Ctrl+C停止录音")
        
        self.is_recording = True
        
        # 启动音频处理线程
        self.processing_thread = threading.Thread(target=self._audio_processing_thread)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        # 启动音频流
        self.stream = self.audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size,
            stream_callback=self._audio_callback
        )
        
        self.stream.start_stream()
        
        try:
            while self.is_recording:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.stop_recording()
    
    def stop_recording(self):
        """停止录音"""
        if not self.is_recording:
            return
            
        print("\n🛑 停止录音...")
        self.is_recording = False
        
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        
        # 处理剩余的语音数据
        if self.speech_frames:
            self.speech_segments.append(np.concatenate(self.speech_frames))
            
        if self.speech_segments:
            print("🔄 处理剩余音频片段...")
            self._recognize_speech_batch()
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'audio'):
            self.audio.terminate()


def main():
    """主函数"""
    try:
        # 创建增强版ASR实例
        asr = LowLatencyASR()
        
        # 开始录音识别
        asr.start_recording()
        
    except Exception as e:
        print(f"❌ 程序错误: {e}")
    finally:
        print("✅ 程序结束")


if __name__ == "__main__":
    main()



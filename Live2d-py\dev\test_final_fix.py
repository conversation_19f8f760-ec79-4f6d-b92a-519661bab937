#!/usr/bin/env python3
"""
测试最终修复
验证使用正确参数后的API调用
"""

import sys
import os
import json
import time

# 添加路径
sys.path.append(os.path.dirname(__file__))

def test_with_correct_params():
    """使用正确参数测试API调用"""
    print("🔍 使用正确参数测试API调用...")
    
    try:
        from openai import OpenAI
        
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        # 使用与成功配置相同的参数
        correct_params = {
            "temperature": 1.15,
            "max_tokens": 32000,
            "top_p": 0.98,
            "presence_penalty": 0,
            "frequency_penalty": 0
        }
        
        print(f"API配置: {api_config}")
        print(f"使用参数: {correct_params}")
        
        client = OpenAI(
            base_url=api_config["base_url"],
            api_key=api_config["api_key"],
            timeout=10.0
        )
        
        print("📤 发送测试请求...")
        response = client.chat.completions.create(
            model=api_config["model"],
            messages=[{"role": "user", "content": "Hello"}],
            **correct_params
        )
        
        print(f"📥 响应类型: {type(response)}")
        print(f"📥 响应内容: {response}")
        
        # 使用成功代码的检查逻辑
        if (response and response.choices and 
            response.choices[0].message and
            response.choices[0].message.content):
            content = response.choices[0].message.content
            print(f"✅ 成功提取内容: {content}")
            return True
        else:
            print("❌ 无法提取内容")
            if response and response.choices:
                choice = response.choices[0]
                print(f"finish_reason: {getattr(choice, 'finish_reason', 'unknown')}")
                print(f"message: {getattr(choice, 'message', 'None')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixed_llm_client():
    """测试修复后的LLMClient"""
    print("\n🔍 测试修复后的LLMClient...")
    
    try:
        from llm_client import LLMClient
        
        # 创建配置管理器
        class TestConfig:
            def __init__(self):
                with open("config.json", "r", encoding="utf-8") as f:
                    self.config = json.load(f)
        
        config_manager = TestConfig()
        llm_client = LLMClient(config_manager)
        
        print("📤 发送测试消息...")
        response = llm_client.chat("Hello")
        
        if response and not response.startswith("❌"):
            print(f"✅ LLMClient成功: {response[:100]}...")
            return True
        else:
            print(f"❌ LLMClient失败: {response}")
            return False
            
    except Exception as e:
        print(f"❌ LLMClient测试异常: {e}")
        return False

def test_fixed_api_worker():
    """测试修复后的APITestWorker"""
    print("\n🔍 测试修复后的APITestWorker...")
    
    try:
        # 读取配置
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        from settings_dialog import APITestWorker
        
        # 创建结果收集器
        class ResultCollector:
            def __init__(self):
                self.success = False
                self.error = None
                self.finished = False
            
            def on_success(self):
                print("✅ APITestWorker成功")
                self.success = True
                self.finished = True
            
            def on_failed(self, error):
                print(f"❌ APITestWorker失败: {error}")
                self.error = error
                self.finished = True
        
        collector = ResultCollector()
        
        # 创建并启动worker
        worker = APITestWorker(api_config)
        worker.test_success.connect(collector.on_success)
        worker.test_failed.connect(collector.on_failed)
        
        print("📤 启动APITestWorker...")
        worker.start()
        
        # 等待完成
        timeout = 30
        elapsed = 0
        while not collector.finished and elapsed < timeout:
            time.sleep(0.1)
            elapsed += 0.1
        
        if not collector.finished:
            print("⏰ 超时")
            return False
        
        return collector.success
        
    except Exception as e:
        print(f"❌ APITestWorker测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终修复测试...")
    print("=" * 60)
    
    # 测试1: 使用正确参数的直接调用
    direct_result = test_with_correct_params()
    
    # 测试2: 修复后的LLMClient
    llm_result = test_fixed_llm_client()
    
    # 测试3: 修复后的APITestWorker
    worker_result = test_fixed_api_worker()
    
    print("\n" + "=" * 60)
    print("📊 最终测试结果:")
    print(f"- 正确参数直接调用: {'✅ 成功' if direct_result else '❌ 失败'}")
    print(f"- 修复后LLMClient: {'✅ 成功' if llm_result else '❌ 失败'}")
    print(f"- 修复后APITestWorker: {'✅ 成功' if worker_result else '❌ 失败'}")
    
    if direct_result and llm_result and worker_result:
        print("\n🎉 所有测试通过！")
        print("API测试功能现在应该完全正常工作了。")
        
        print("\n✅ 关键修复:")
        print("- 使用正确的max_tokens参数 (32000)")
        print("- 使用正确的temperature参数 (1.15)")
        print("- 使用正确的top_p参数 (0.98)")
        print("- 改进了响应格式检查逻辑")
        
    elif direct_result:
        print("\n🎯 参数问题已解决！")
        print("直接调用成功，说明参数配置是关键。")
        
    else:
        print("\n⚠️ 仍有问题需要进一步调试")

if __name__ == "__main__":
    main()

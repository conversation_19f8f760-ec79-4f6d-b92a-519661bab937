#!/usr/bin/env python3
"""
直接测试API调用
验证自定义API是否符合OpenAI格式
"""

import sys
import os
import json

# 添加路径
sys.path.append(os.path.dirname(__file__))

def test_api_direct():
    """直接测试API调用"""
    print("🔍 直接测试API调用...")
    
    # 从config.json读取配置
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        print(f"API配置: {api_config}")
        
        # 检查必要的配置
        if not api_config.get("api_key"):
            print("❌ API密钥为空")
            return False
        
        if not api_config.get("base_url"):
            print("❌ API地址为空")
            return False
        
        # 测试OpenAI库导入
        try:
            from openai import OpenAI
            print("✅ OpenAI库导入成功")
        except ImportError as e:
            print(f"❌ OpenAI库导入失败: {e}")
            return False
        
        # 创建OpenAI客户端
        try:
            client = OpenAI(
                base_url=api_config["base_url"],
                api_key=api_config["api_key"],
                timeout=10.0
            )
            print("✅ OpenAI客户端创建成功")
        except Exception as e:
            print(f"❌ OpenAI客户端创建失败: {e}")
            return False
        
        # 测试API调用
        try:
            print("📤 发送测试请求...")
            
            response = client.chat.completions.create(
                model=api_config["model"],
                messages=[
                    {"role": "user", "content": "Hello, this is a test message."}
                ],
                max_tokens=100,
                temperature=0.7
            )
            
            print("✅ API调用成功")
            print(f"📥 响应: {response.choices[0].message.content}")
            return True
            
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 配置读取失败: {e}")
        return False

def test_llm_client():
    """测试LLMClient"""
    print("\n🔍 测试LLMClient...")
    
    try:
        from llm_client import LLMClient
        from settings_dialog import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 创建LLM客户端
        llm_client = LLMClient(config_manager)
        
        print(f"LLM客户端配置状态: {'已配置' if llm_client.is_configured() else '未配置'}")
        
        if not llm_client.is_configured():
            print("❌ LLM客户端未正确配置")
            return False
        
        # 测试对话
        print("📤 发送测试消息...")
        response = llm_client.chat("Hello, this is a test message.")
        
        if response and not response.startswith("❌"):
            print("✅ LLMClient测试成功")
            print(f"📥 响应: {response}")
            return True
        else:
            print(f"❌ LLMClient测试失败: {response}")
            return False
            
    except Exception as e:
        print(f"❌ LLMClient测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_api_format():
    """检查API格式兼容性"""
    print("\n🔍 检查API格式兼容性...")
    
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        llm_config = config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        base_url = api_config.get("base_url", "")
        model = api_config.get("model", "")
        
        print(f"API地址: {base_url}")
        print(f"模型名称: {model}")
        
        # 检查URL格式
        if not base_url.endswith("/v1"):
            print("⚠️ 警告: API地址应该以 /v1 结尾")
        
        # 检查模型名称
        if not model:
            print("❌ 模型名称为空")
            return False
        
        print("✅ API格式检查通过")
        return True
        
    except Exception as e:
        print(f"❌ API格式检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API直接测试...")
    print("=" * 50)
    
    all_passed = True
    
    # 检查API格式
    if not check_api_format():
        all_passed = False
    
    # 直接测试API
    if not test_api_direct():
        all_passed = False
    
    # 测试LLMClient
    if not test_llm_client():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有API测试通过！")
        print("\n✅ 确认事项:")
        print("- OpenAI库正常工作")
        print("- API配置正确")
        print("- 自定义API兼容OpenAI格式")
        print("- LLMClient正常工作")
        
        print("\n💡 如果界面测试仍然失败，可能是:")
        print("- Qt信号连接问题")
        print("- 线程通信问题")
        print("- UI更新时机问题")
    else:
        print("❌ 部分API测试失败")
        print("\n🔧 建议检查:")
        print("- API密钥是否正确")
        print("- 网络连接是否正常")
        print("- API地址是否正确")
        print("- 模型名称是否正确")

if __name__ == "__main__":
    main()

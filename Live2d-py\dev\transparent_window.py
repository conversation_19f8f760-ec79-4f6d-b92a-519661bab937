#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TransparentWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        # 设置窗口属性
        self.setWindowTitle('透明背景窗口示例')
        self.setGeometry(300, 300, 400, 300)
        
        # 设置窗口背景透明
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        
        # 添加一个标签用于显示内容
        label = QLabel('这是一个透明背景窗口', self)
        label.setGeometry(50, 100, 300, 50)
        label.setAlignment(Qt.AlignCenter)
        label.setFont(QFont('Arial', 16, QFont.Bold))
        label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(0, 0, 0, 100);
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        # 添加切换按钮
        self.toggle_btn = QLabel('🔄', self)
        self.toggle_btn.setGeometry(320, 10, 30, 30)
        self.toggle_btn.setAlignment(Qt.AlignCenter)
        self.toggle_btn.setFont(QFont('Arial', 14, QFont.Bold))
        self.toggle_btn.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(0, 150, 255, 150);
                border-radius: 15px;
            }
            QLabel:hover {
                background-color: rgba(0, 150, 255, 200);
            }
        """)
        self.toggle_btn.mousePressEvent = lambda event: self.toggle_transparency()

        # 添加关闭按钮
        close_btn = QLabel('✕', self)
        close_btn.setGeometry(360, 10, 30, 30)
        close_btn.setAlignment(Qt.AlignCenter)
        close_btn.setFont(QFont('Arial', 14, QFont.Bold))
        close_btn.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255, 0, 0, 150);
                border-radius: 15px;
            }
            QLabel:hover {
                background-color: rgba(255, 0, 0, 200);
            }
        """)
        close_btn.mousePressEvent = lambda event: self.close()

        # 透明状态标志
        self.is_transparent = True
        
        self.show()
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def toggle_transparency(self):
        """切换窗口透明度"""
        if self.is_transparent:
            # 切换到不透明
            self.setAttribute(Qt.WA_TranslucentBackground, False)
            self.setStyleSheet("background-color: rgba(50, 50, 50, 200);")
            self.toggle_btn.setText('🌟')
            self.is_transparent = False
        else:
            # 切换到透明
            self.setAttribute(Qt.WA_TranslucentBackground, True)
            self.setStyleSheet("")
            self.toggle_btn.setText('🔄')
            self.is_transparent = True

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TransparentWindow()
    sys.exit(app.exec_())

#!/usr/bin/env python3
"""
调试API响应问题
分析"❌ API返回了空响应"的原因
"""

import sys
import os

# 添加正确的路径
project_root = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'dev'))

def debug_api_response():
    """调试API响应问题"""
    print("🔍 开始调试API响应问题...")
    print("=" * 60)
    
    try:
        from settings_dialog import ConfigManager
        from dialogue_system.core.llm_client import LLMClient
        
        # 创建配置管理器
        config_manager = ConfigManager()
        print("✅ 配置管理器创建成功")
        
        # 创建LLM客户端
        llm_client = LLMClient(config_manager)
        print("✅ LLM客户端创建成功")
        
        # 检查配置
        print(f"\n🔍 配置检查:")
        print(f"- 是否已配置: {llm_client.is_configured()}")
        print(f"- API密钥: {'已设置' if llm_client.api_config.get('api_key') else '未设置'}")
        print(f"- 模型: {llm_client.api_config.get('model', '未设置')}")
        print(f"- Base URL: {llm_client.api_config.get('base_url', '未设置')}")
        
        # 检查默认参数
        print(f"\n🔍 默认参数:")
        for key, value in llm_client.default_params.items():
            print(f"- {key}: {value}")
        
        # 检查系统提示词
        print(f"\n🔍 系统提示词:")
        print(f"- 当前系统提示词: '{llm_client.current_system_prompt}'")
        print(f"- 长度: {len(llm_client.current_system_prompt) if llm_client.current_system_prompt else 0}")
        
        if not llm_client.is_configured():
            print("\n❌ LLM客户端未正确配置，无法进行API测试")
            return False
        
        # 测试简单消息
        print(f"\n🔍 测试简单消息...")
        test_message = "你好"
        print(f"发送消息: '{test_message}'")
        
        # 准备消息格式
        messages = llm_client._prepare_messages(test_message)
        print(f"\n🔍 准备的消息格式:")
        for i, msg in enumerate(messages):
            print(f"  {i+1}. {msg['role']}: '{msg['content'][:50]}{'...' if len(msg['content']) > 50 else ''}'")
        
        # 发送请求
        print(f"\n🔍 发送API请求...")
        response = llm_client.chat(test_message)
        print(f"响应结果: '{response}'")
        
        if response and not response.startswith("❌"):
            print("✅ API请求成功")
            return True
        else:
            print("❌ API请求失败或返回空响应")
            
            # 尝试不同的参数
            print(f"\n🔍 尝试调整参数...")
            
            # 测试1: 增加max_tokens
            print("测试1: 增加max_tokens到2000")
            response1 = llm_client.chat(test_message, max_tokens=2000)
            print(f"结果1: '{response1}'")
            
            # 测试2: 降低temperature
            print("测试2: 降低temperature到0.1")
            response2 = llm_client.chat(test_message, temperature=0.1)
            print(f"结果2: '{response2}'")
            
            # 测试3: 使用更简单的消息
            print("测试3: 清空历史，使用更简单的消息")
            llm_client.clear_history()
            response3 = llm_client.chat("Hi")
            print(f"结果3: '{response3}'")
            
            # 测试4: 检查不同模型
            original_model = llm_client.api_config.get("model")
            if "gpt-4" in original_model.lower():
                print("测试4: 尝试使用gpt-3.5-turbo")
                llm_client.api_config["model"] = "gpt-3.5-turbo"
                llm_client._init_openai_client()
                response4 = llm_client.chat("Hello")
                print(f"结果4: '{response4}'")
                # 恢复原模型
                llm_client.api_config["model"] = original_model
                llm_client._init_openai_client()
            
            return False
            
    except Exception as e:
        print(f"❌ 调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_direct_api_call():
    """直接测试API调用"""
    print(f"\n🔍 直接API调用测试...")
    
    try:
        from settings_dialog import ConfigManager
        from openai import OpenAI
        
        config_manager = ConfigManager()
        llm_config = config_manager.config.get("llm", {})
        api_config = llm_config.get("api_config", {})
        
        if not api_config.get("api_key"):
            print("❌ 没有API密钥，跳过直接API测试")
            return False
        
        # 创建OpenAI客户端
        client = OpenAI(
            base_url=api_config.get("base_url", "https://api.openai.com/v1"),
            api_key=api_config["api_key"]
        )
        
        # 直接调用API
        print("发送直接API请求...")
        response = client.chat.completions.create(
            model=api_config.get("model", "gpt-3.5-turbo"),
            messages=[
                {"role": "system", "content": "你是一个友好的AI助手。"},
                {"role": "user", "content": "你好"}
            ],
            max_tokens=1000,
            temperature=0.7
        )
        
        print(f"直接API响应类型: {type(response)}")
        print(f"直接API响应: {response}")
        
        if hasattr(response, 'choices') and response.choices:
            choice = response.choices[0]
            print(f"Choice类型: {type(choice)}")
            print(f"Choice: {choice}")
            
            if hasattr(choice, 'message'):
                message = choice.message
                print(f"Message类型: {type(message)}")
                print(f"Message: {message}")
                
                if hasattr(message, 'content'):
                    content = message.content
                    print(f"Content: '{content}'")
                    print(f"Content类型: {type(content)}")
                    print(f"Content长度: {len(content) if content else 'None'}")
                    
                    if content:
                        print("✅ 直接API调用成功")
                        return True
                    else:
                        print("❌ 直接API调用返回空内容")
                        return False
        
        print("❌ 直接API调用响应格式异常")
        return False
        
    except Exception as e:
        print(f"❌ 直接API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 API响应调试工具")
    print("=" * 60)
    
    # 调试LLM客户端
    client_success = debug_api_response()
    
    # 直接API测试
    direct_success = test_direct_api_call()
    
    print("\n" + "=" * 60)
    print("📊 调试结果总结:")
    print(f"- LLM客户端测试: {'✅ 成功' if client_success else '❌ 失败'}")
    print(f"- 直接API测试: {'✅ 成功' if direct_success else '❌ 失败'}")
    
    if not client_success and not direct_success:
        print("\n🔧 可能的解决方案:")
        print("1. 检查API密钥是否正确")
        print("2. 检查网络连接")
        print("3. 检查API服务是否可用")
        print("4. 检查模型名称是否正确")
        print("5. 检查max_tokens参数是否合理")
        print("6. 检查系统提示词是否过长")
    elif not client_success and direct_success:
        print("\n🔧 LLM客户端问题:")
        print("1. 检查消息格式处理")
        print("2. 检查响应解析逻辑")
        print("3. 检查参数传递")
        print("4. 查看详细调试输出")
    else:
        print("\n🎉 API调用正常，问题可能在其他地方")


if __name__ == "__main__":
    main()

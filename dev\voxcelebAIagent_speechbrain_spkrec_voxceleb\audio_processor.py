import sounddevice as sd
import numpy as np
import threading
import time
from config import Config

class AudioProcessor:
    def __init__(self):
        self.sample_rate = Config.SAMPLE_RATE
        self.duration = Config.AUDIO_DURATION
        self.recording = False
        self.audio_data = None
    
    def record_audio(self):
        """录制音频"""
        print(f"开始录音 {self.duration} 秒...")
        try:
            self.audio_data = sd.rec(
                int(self.duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=1,
                dtype=np.float32
            )
            sd.wait()  # 等待录音完成
            print("录音完成")
            return self.audio_data.flatten()
        except Exception as e:
            raise Exception(f"录音失败: {str(e)}")
    
    def record_with_countdown(self):
        """带倒计时的录音"""
        print("准备录音...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        print("开始录音！")
        return self.record_audio()
    
    def get_audio_devices(self):
        """获取可用音频设备"""
        try:
            devices = sd.query_devices()
            input_devices = []
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    input_devices.append({
                        'id': i,
                        'name': device['name'],
                        'channels': device['max_input_channels']
                    })
            return input_devices
        except Exception as e:
            print(f"获取音频设备失败: {e}")
            return []
    
    def set_input_device(self, device_id):
        """设置输入设备"""
        try:
            sd.default.device[0] = device_id
            print(f"已设置输入设备: {device_id}")
        except Exception as e:
            print(f"设置输入设备失败: {e}")
    
    def test_audio_input(self):
        """测试音频输入"""
        print("测试音频输入（1秒）...")
        try:
            test_audio = sd.rec(
                int(1 * self.sample_rate),
                samplerate=self.sample_rate,
                channels=1,
                dtype=np.float32
            )
            sd.wait()
            
            # 检查音频强度
            rms = np.sqrt(np.mean(test_audio**2))
            print(f"音频强度: {rms:.4f}")
            
            if rms < 0.001:
                print("警告: 音频信号很弱，请检查麦克风")
                return False
            else:
                print("音频输入正常")
                return True
                
        except Exception as e:
            print(f"音频测试失败: {e}")
            return False

#!/usr/bin/env python3
"""
LLM客户端模块
为Live2D桌面宠物提供LLM对话功能
基于OpenAI API实现
"""

import json
import os
import time
from datetime import datetime
from typing import Optional, Dict, Any, List, Callable
from .chat_history_manager import ChatHistoryManager
try:
    from openai import OpenAI
except ImportError:
    try:
        # 兼容旧版本的openai库
        import openai
        OpenAI = openai.OpenAI if hasattr(openai, 'OpenAI') else None
    except ImportError:
        OpenAI = None


class LLMClient:
    """LLM客户端类，处理与OpenAI API的交互"""
    
    def __init__(self, config_manager=None):
        """初始化LLM客户端"""
        self.config_manager = config_manager
        self.openai_client = None
        self.conversation_history = []
        self.current_system_prompt = None
        self.default_system_prompt = "你是一个友好的AI助手。"
        self.last_user_message = None  # 保存最后一条用户消息，用于重新生成
        self.last_assistant_message = None  # 保存最后一条助手消息

        # 初始化历史管理器
        self.history_manager = ChatHistoryManager(config_manager)

        # 初始化配置
        self._init_config()
        self._init_openai_client()
    
    def _init_config(self):
        """初始化配置"""
        if self.config_manager:
            # 从配置管理器获取配置
            llm_config = self.config_manager.config.get("llm", {})
        else:
            # 使用默认配置
            llm_config = {}
        
        # 设置默认值
        self.api_config = llm_config.get("api_config", {
            "base_url": "https://api.openai.com/v1",
            "api_key": "",
            "model": "gpt-3.5-turbo",
            "timeout": 30.0
        })
        
        self.conversation_settings = llm_config.get("conversation_settings", {
            "system_prompt": self.default_system_prompt,
            "max_history": 20,
            "temperature": 0.7,
            "max_tokens": 1000
        })
        
        # 设置默认参数
        self.default_params = {
            "temperature": self.conversation_settings.get("temperature", 0.7),
            "max_tokens": self.conversation_settings.get("max_tokens", 1000),
            "top_p": 1.0,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0
        }
        
        self.current_system_prompt = self.conversation_settings.get("system_prompt", self.default_system_prompt)
    
    def _init_openai_client(self):
        """初始化OpenAI客户端"""
        if OpenAI is None:
            raise ImportError("OpenAI库未安装，请安装: pip install openai>=1.0.0")
        
        if not self.api_config.get("api_key"):
            print("⚠️ 警告: OpenAI API密钥未设置")
            return
        
        try:
            self.openai_client = OpenAI(
                base_url=self.api_config["base_url"],
                api_key=self.api_config["api_key"],
                timeout=self.api_config.get("timeout", 30.0)
            )
            print("✅ OpenAI客户端初始化成功")
        except Exception as e:
            print(f"❌ OpenAI客户端初始化失败: {e}")
            self.openai_client = None
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        print(f"🔍 更新LLM配置: {new_config}")

        if "api_config" in new_config:
            print(f"🔍 更新API配置: {new_config['api_config']}")
            self.api_config.update(new_config["api_config"])
            print(f"🔍 更新后的API配置: {self.api_config}")
            self._init_openai_client()

        if "conversation_settings" in new_config:
            print(f"🔍 更新对话设置: {new_config['conversation_settings']}")
            self.conversation_settings.update(new_config["conversation_settings"])

            # 更新默认参数
            if "temperature" in new_config["conversation_settings"]:
                self.default_params["temperature"] = new_config["conversation_settings"]["temperature"]
            if "max_tokens" in new_config["conversation_settings"]:
                self.default_params["max_tokens"] = new_config["conversation_settings"]["max_tokens"]

            # 更新系统提示词
            if "system_prompt" in new_config["conversation_settings"]:
                self.current_system_prompt = new_config["conversation_settings"]["system_prompt"]
                # 清空历史，使用新的系统提示词
                self.clear_history()

        print(f"🔍 配置更新完成，是否已配置: {self.is_configured()}")
    
    def add_message(self, role: str, content: str):
        """添加消息到历史"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        self.conversation_history.append(message)

        # 保存最后的消息
        if role == "user":
            self.last_user_message = content
        elif role == "assistant":
            self.last_assistant_message = content

        # 添加到历史管理器
        self.history_manager.add_message_to_current(role, content)

        # 限制历史长度
        max_history = self.conversation_settings.get("max_history", 20)
        if len(self.conversation_history) > max_history:
            # 保留系统消息，删除最旧的用户/助手消息
            system_messages = [msg for msg in self.conversation_history if msg["role"] == "system"]
            other_messages = [msg for msg in self.conversation_history if msg["role"] != "system"]

            # 保留最新的消息
            other_messages = other_messages[-(max_history - len(system_messages)):]
            self.conversation_history = system_messages + other_messages
    
    def clear_history(self):
        """清空对话历史"""
        self.conversation_history = []
        print("🗑️ 对话历史已清空")
    
    def get_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        return self.conversation_history.copy()
    
    def _prepare_messages(self, user_message: str) -> List[Dict[str, str]]:
        """准备发送给API的消息列表"""
        messages = []
        
        # 添加系统消息
        if self.current_system_prompt:
            messages.append({
                "role": "system",
                "content": self.current_system_prompt
            })
        
        # 添加历史消息（只包含role和content）
        for msg in self.conversation_history:
            if msg["role"] in ["user", "assistant"]:
                messages.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })
        
        # 添加当前用户消息
        messages.append({
            "role": "user",
            "content": user_message
        })
        
        return messages
    
    def _extract_response_content(self, response) -> Optional[str]:
        """从API响应中提取内容"""
        try:
            # 处理新版本openai库的响应格式
            if hasattr(response, 'choices') and response.choices:
                choice = response.choices[0]

                if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                    content = choice.message.content
                    if content and content.strip():  # 检查内容不为空且不只是空白字符
                        return content
                    else:
                        print("⚠️ API返回的content为空或只包含空白字符")
                        print(f"🔍 原始content: '{content}'")
                        return None
                elif hasattr(choice, 'text'):
                    text = choice.text
                    if text and text.strip():
                        return text
                    else:
                        print("⚠️ API返回的text为空或只包含空白字符")
                        return None
                else:
                    print("⚠️ choice对象既没有message也没有text属性")
                    print(f"🔍 choice属性: {[attr for attr in dir(choice) if not attr.startswith('_')]}")

            # 处理字典格式的响应
            elif isinstance(response, dict):
                if 'choices' in response and response['choices']:
                    choice = response['choices'][0]
                    if 'message' in choice and 'content' in choice['message']:
                        content = choice['message']['content']
                        if content and content.strip():
                            return content
                        else:
                            print("⚠️ 字典格式的content为空")
                            return None
                    elif 'text' in choice:
                        text = choice['text']
                        if text and text.strip():
                            return text
                        else:
                            print("⚠️ 字典格式的text为空")
                            return None

            print(f"⚠️ 无法解析API响应格式: {type(response)}")
            print(f"🔍 响应对象: {response}")
            return None

        except Exception as e:
            print(f"❌ 解析API响应时出错: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def is_configured(self) -> bool:
        """检查是否已正确配置"""
        return (self.openai_client is not None and 
                bool(self.api_config.get("api_key")) and
                bool(self.api_config.get("model")))
    
    async def chat_async(self, user_message: str, **kwargs) -> Optional[str]:
        """异步发送消息并获取回复"""
        if not self.openai_client:
            return "❌ OpenAI客户端未初始化，请检查API配置"
        
        try:
            # 准备消息
            messages = self._prepare_messages(user_message)
            
            # 合并参数
            params = self.default_params.copy()
            params.update(kwargs)
            
            # 发送请求
            response = await self.openai_client.chat.completions.create(
                model=self.api_config["model"],
                messages=messages,
                **params
            )
            
            # 提取回复 - 增强兼容性处理
            assistant_message = self._extract_response_content(response)

            if assistant_message:
                # 添加到历史
                self.add_message("user", user_message)
                self.add_message("assistant", assistant_message)
                return assistant_message
            else:
                return "❌ API返回了空响应"
            
        except Exception as e:
            error_msg = f"❌ API调用失败: {str(e)}"
            print(error_msg)
            return error_msg
    
    def _validate_config(self) -> bool:
        """验证配置完整性"""
        if not self.api_config.get("api_key"):
            print("⚠️ 警告: API密钥未设置，尝试重新加载配置")
            if self.config_manager:
                # 重新加载配置
                self._init_config()
                self._init_openai_client()

        if not self.is_configured():
            print("❌ 配置验证失败，请检查API设置")
            print(f"🔍 当前配置: API密钥={'已设置' if self.api_config.get('api_key') else '未设置'}, 模型={self.api_config.get('model', '未设置')}")
            return False
        return True

    def chat(self, user_message: str, **kwargs) -> Optional[str]:
        """同步发送消息并获取回复"""
        # 验证配置
        if not self._validate_config():
            return "❌ API配置不完整，请检查API密钥和模型设置"
        
        try:
            # 准备消息
            messages = self._prepare_messages(user_message)
            
            # 合并参数
            params = self.default_params.copy()
            params.update(kwargs)
            
            # 发送请求
            response = self.openai_client.chat.completions.create(
                model=self.api_config["model"],
                messages=messages,
                **params
            )
            
            # 提取回复 - 增强兼容性处理
            assistant_message = self._extract_response_content(response)

            if assistant_message:
                # 添加到历史
                self.add_message("user", user_message)
                self.add_message("assistant", assistant_message)
                return assistant_message
            else:
                return "❌ API返回了空响应"
            
        except Exception as e:
            error_msg = f"❌ API调用失败: {str(e)}"
            print(error_msg)
            return error_msg

    def regenerate_last_response(self) -> Optional[str]:
        """重新生成最后一条回复"""
        if not self.last_user_message:
            return "❌ 没有可重新生成的消息"

        # 移除最后一条助手消息（如果存在）
        if (self.conversation_history and
            self.conversation_history[-1]["role"] == "assistant"):
            self.conversation_history.pop()

        # 重新发送最后一条用户消息
        return self.chat(self.last_user_message)

    def continue_conversation(self, prompt: str = "请继续") -> Optional[str]:
        """继续对话（不需要用户输入新消息）"""
        return self.chat(prompt)

    def start_new_chat(self, title: str = None):
        """开始新的聊天会话"""
        # 清空当前对话历史
        self.conversation_history = []
        self.last_user_message = None
        self.last_assistant_message = None

        # 创建新的会话
        current_preset = getattr(self, 'current_preset_id', 'default')
        self.history_manager.create_new_session(title=title, preset_id=current_preset)

        print("🆕 开始新的聊天会话")

    def load_chat_session(self, session_id: str) -> bool:
        """加载指定的聊天会话"""
        if self.history_manager.set_current_session(session_id):
            # 加载会话消息到当前历史
            messages = self.history_manager.get_current_messages()
            self.conversation_history = messages.copy()

            # 更新最后的消息
            self.last_user_message = None
            self.last_assistant_message = None

            for msg in reversed(messages):
                if msg["role"] == "user" and not self.last_user_message:
                    self.last_user_message = msg["content"]
                elif msg["role"] == "assistant" and not self.last_assistant_message:
                    self.last_assistant_message = msg["content"]

                if self.last_user_message and self.last_assistant_message:
                    break

            print(f"📂 已加载聊天会话: {session_id}")
            return True

        return False

    def get_chat_sessions(self):
        """获取所有聊天会话"""
        return self.history_manager.get_all_sessions()

    def delete_chat_session(self, session_id: str) -> bool:
        """删除聊天会话"""
        return self.history_manager.delete_session(session_id)

    def get_current_session_info(self):
        """获取当前会话信息"""
        session = self.history_manager.get_current_session()
        if session:
            return {
                "session_id": session.session_id,
                "title": session.title,
                "message_count": len(session.messages),
                "created_time": session.created_time,
                "updated_time": session.updated_time
            }
        return None

    def set_session_title(self, title: str):
        """设置当前会话标题"""
        session = self.history_manager.get_current_session()
        if session:
            session.set_title(title)
            self.history_manager.save_session(session)

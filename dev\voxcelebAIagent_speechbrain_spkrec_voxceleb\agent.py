from speaker_recognition import Speaker<PERSON><PERSON>ognition
from identity_memory import IdentityMemory
from audio_processor import AudioProcessor
from llm_client import LLMClient

class VoxCelebAgent:
    def __init__(self):
        print("初始化VoxCeleb AI Agent...")
        self.speaker_recognition = SpeakerRecognition()
        self.identity_memory = IdentityMemory()
        self.audio_processor = AudioProcessor()
        self.llm_client = LLMClient()
        print("初始化完成！")
    
    def process_audio_input(self, user_message=None):
        """处理音频输入并进行对话"""
        try:
            # 录制音频
            audio_data = self.audio_processor.record_with_countdown()
            
            # 预处理音频
            audio_tensor = self.speaker_recognition.preprocess_audio(
                audio_data, self.audio_processor.sample_rate
            )
            
            # 提取说话人特征
            embedding = self.speaker_recognition.extract_embedding(audio_tensor)
            
            # 识别说话人
            user_id, similarity = self.identity_memory.identify_speaker(
                embedding, self.speaker_recognition
            )
            
            if user_id:
                # 已知用户
                identity_info = self.identity_memory.get_identity_info(user_id)
                print(f"识别到用户: {identity_info['name']} (相似度: {similarity:.3f})")
                
                # 获取对话上下文
                conversation_context = self.identity_memory.get_conversation_context(user_id)
                
            else:
                # 新用户
                print(f"检测到新用户 (最高相似度: {similarity:.3f})")
                name = input("请输入您的姓名: ").strip()
                if not name:
                    name = f"用户{len(self.identity_memory.identities) + 1}"
                
                user_id = self.identity_memory.register_identity(name, embedding)
                identity_info = self.identity_memory.get_identity_info(user_id)
                conversation_context = []
                print(f"已注册新用户: {name}")
            
            # 获取用户文本输入
            if not user_message:
                user_message = input("请输入您想说的话: ").strip()
                if not user_message:
                    return "请输入有效的消息"
            
            # 生成AI回复
            ai_response = self.llm_client.chat_with_identity(
                user_message, identity_info, conversation_context
            )
            
            # 更新对话历史
            self.identity_memory.update_conversation(user_id, user_message, ai_response)
            
            return ai_response
            
        except Exception as e:
            return f"处理失败: {str(e)}"
    
    def text_only_chat(self, user_message, user_name=None):
        """仅文本对话（用于测试）"""
        try:
            # 如果提供了用户名，尝试查找已知用户
            user_id = None
            if user_name:
                for uid, identity in self.identity_memory.identities.items():
                    if identity['name'].lower() == user_name.lower():
                        user_id = uid
                        break
            
            if user_id:
                identity_info = self.identity_memory.get_identity_info(user_id)
                conversation_context = self.identity_memory.get_conversation_context(user_id)
                print(f"找到用户: {identity_info['name']}")
            else:
                identity_info = None
                conversation_context = []
                print("新用户对话")
            
            # 生成AI回复
            ai_response = self.llm_client.chat_with_identity(
                user_message, identity_info, conversation_context
            )
            
            # 如果是已知用户，更新对话历史
            if user_id:
                self.identity_memory.update_conversation(user_id, user_message, ai_response)
            
            return ai_response
            
        except Exception as e:
            return f"对话失败: {str(e)}"
    
    def list_identities(self):
        """列出所有已注册身份"""
        if not self.identity_memory.identities:
            return "暂无已注册用户"
        
        result = "已注册用户:\n"
        for user_id, identity in self.identity_memory.identities.items():
            result += f"- {identity['name']} (ID: {user_id}, 交互: {identity['interaction_count']}次)\n"
        
        return result
    
    def get_user_stats(self, user_name):
        """获取用户统计信息"""
        for user_id, identity in self.identity_memory.identities.items():
            if identity['name'].lower() == user_name.lower():
                return f"""
用户: {identity['name']}
注册时间: {identity['created_at']}
最后见面: {identity['last_seen']}
交互次数: {identity['interaction_count']}
对话历史: {len(identity['conversation_history'])}条
偏好设置: {identity['preferences'] if identity['preferences'] else '暂无'}
"""
        return f"未找到用户: {user_name}"

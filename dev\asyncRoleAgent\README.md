# 异步角色对话系统 (AsyncRoleAgent)

基于LLM驱动的角色扮演异步对话系统，支持后台任务处理和主动对话。

## 🌟 核心特性

### 1. 角色驱动对话
- **角色设定**: 可爱的天气时间偶像小助手"小雨"
- **个性化回应**: 有自己的口吻和说话风格
- **情感表达**: 使用emoji和可爱的语气词

### 2. 异步任务处理
- **后台执行**: 耗时任务在后台异步执行
- **状态感知**: LLM能"感知"任务进行状态
- **智能提醒**: 任务完成时自然地提及结果

### 3. 主动对话机制
- **LLM驱动**: 基于prompt让LLM主动发起对话
- **高频互动**: 在等待期间保持对话活跃
- **自然转移**: 不说"正在查询中"，而是转移话题

### 4. 多功能支持
- **天气查询**: 支持当前天气查询（模拟1分钟延迟）
- **天气预报**: 支持未来几天天气预报
- **时间查询**: 支持时间、日期、星期查询（即时）

## 🏗️ 系统架构

```
AsyncRoleAgent (主控制器)
├── AsyncTaskManager (异步任务管理)
├── RolePromptSystem (角色prompt系统)
├── ConversationDriver (对话驱动器)
├── ConversationState (对话状态管理)
├── LLMClient (LLM客户端)
└── ServiceIntegrator (服务整合器)
```

## 📁 文件结构

```
asyncRoleAgent/
├── async_role_agent.py      # 主代理类
├── async_task_manager.py    # 异步任务管理器
├── role_prompt_system.py    # 角色prompt系统
├── conversation_driver.py   # 对话驱动器
├── service_integrator.py    # 服务整合器
├── llm_client.py           # LLM客户端
├── config.py               # 配置文件
├── main.py                 # 主程序
├── test_system.py          # 测试脚本
├── requirements.txt        # 依赖包
└── README.md              # 说明文档
```

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
确保 `../api.txt` 文件包含有效的LLM API密钥。

### 3. 运行系统
```bash
python main.py
```

### 4. 测试系统
```bash
python test_system.py
```

## 💬 使用示例

### 基本对话
```
你: 你好！
小雨: 嗨！我是小雨，你的可爱天气时间偶像小助手呢！✨ 有什么想聊的吗？
```

### 天气查询（异步）
```
你: 北京今天天气怎么样？
小雨: 哎呀，说到北京，你知道我最喜欢那里的什么吗？是那些小胡同！你去过吗？
[1分钟后]
小雨: 咦！我刚才查的北京天气结果出来了！北京现在晴天，温度22度，湿度65%呢！
```

### 时间查询（即时）
```
你: 现在几点了？
小雨: 现在是2024年08月04日 14:30:25，星期日呢！
```

## ⚙️ 配置选项

在 `config.py` 中可以调整：

- `WEATHER_QUERY_DELAY`: 天气查询延迟时间（默认60秒）
- `PROACTIVE_CHAT_ENABLED`: 是否启用主动聊天
- `MAX_PROACTIVE_MESSAGES`: 最大连续主动消息数
- `PROACTIVE_INTERVALS`: 主动消息间隔时间

## 🎯 核心工作流程

1. **用户输入** → 分析意图
2. **启动任务** → 异步执行（如天气查询）
3. **立即回应** → 转移话题，不说"正在查询"
4. **主动对话** → LLM驱动的高频互动
5. **任务完成** → 自然地提及结果
6. **继续对话** → 保持角色一致性

## 🔧 技术特点

### 异步任务管理
- 使用 `asyncio` 实现真正的异步处理
- 任务状态跟踪和通知机制
- 支持多个任务并发执行

### LLM状态感知
- 在prompt中注入任务状态信息
- LLM能"感知"后台任务进度
- 智能决定何时提及任务结果

### 主动对话驱动
- 基于prompt的主动对话生成
- 随机间隔的自然对话节奏
- 避免机械化的定时提醒

## 🐛 故障排除

### 常见问题

1. **LLM调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常

2. **天气查询失败**
   - 检查天气API密钥
   - 确认城市名称正确

3. **主动对话不工作**
   - 检查 `PROACTIVE_CHAT_ENABLED` 配置
   - 确认对话驱动器正常启动

## 📈 扩展建议

1. **添加更多服务**
   - 新闻查询
   - 股票信息
   - 翻译服务

2. **增强角色个性**
   - 更丰富的personality设定
   - 情绪状态管理
   - 记忆系统

3. **优化对话策略**
   - 上下文感知的话题选择
   - 用户偏好学习
   - 对话质量评估

## 📝 开发说明

这个系统展示了如何创建一个真正"活"的AI角色：
- 不是传统的问答机器人
- 有自己的节奏和感知能力
- 能在等待中保持对话活跃
- 自然地处理异步任务结果

核心理念是让LLM驱动整个对话流程，而不是依赖机械化的定时器或状态机。

import os
import sys
import ctypes
from ctypes import wintypes

import OpenGL.GL as gl
from PySide6.QtCore import QTimerEvent, Qt
from PySide6.QtGui import QSurfaceFormat
from PySide6.QtOpenGLWidgets import QOpenGLWidget
from PySide6.QtWidgets import QApplication

import live2d.v3 as live2d
import resources


# Windows API 常量
GWL_EXSTYLE = -20
WS_EX_LAYERED = 0x80000
WS_EX_TRANSPARENT = 0x20
LWA_COLORKEY = 0x1
LWA_ALPHA = 0x2

# Windows API 函数
user32 = ctypes.windll.user32
dwmapi = ctypes.windll.dwmapi

def make_window_transparent(hwnd):
    """使用 Windows API 设置窗口透明"""
    try:
        # 获取当前窗口样式
        ex_style = user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
        
        # 添加分层窗口样式
        new_ex_style = ex_style | WS_EX_LAYERED
        user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_ex_style)
        
        # 设置透明色键（黑色透明）
        user32.SetLayeredWindowAttributes(hwnd, 0x000000, 255, LWA_COLORKEY)
        
        print("✓ Windows API transparency applied")
        return True
    except Exception as e:
        print(f"✗ Windows API transparency failed: {e}")
        return False


class WinAPITransparentWindow(QOpenGLWidget):
    """使用 Windows API 的透明窗口"""

    def __init__(self) -> None:
        super().__init__()
        self.model: live2d.LAppModel | None = None
        
        # 基本的 OpenGL 格式
        format = QSurfaceFormat()
        format.setAlphaBufferSize(8)
        format.setDepthBufferSize(24)
        format.setStencilBufferSize(8)
        format.setVersion(2, 1)
        format.setProfile(QSurfaceFormat.OpenGLContextProfile.CompatibilityProfile)
        self.setFormat(format)
        
        # 基本窗口设置
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.WindowStaysOnTopHint
        )
        
        self.resize(400, 500)
        print("WinAPI transparent window initialized")

    def showEvent(self, event):
        """窗口显示时应用 Windows API 透明"""
        super().showEvent(event)
        
        # 获取窗口句柄并应用透明
        hwnd = int(self.winId())
        if hwnd:
            make_window_transparent(hwnd)

    def initializeGL(self) -> None:
        """初始化 OpenGL"""
        print("Initializing OpenGL...")
        
        live2d.glInit()
        
        # 基本 OpenGL 设置
        gl.glEnable(gl.GL_BLEND)
        gl.glBlendFunc(gl.GL_SRC_ALPHA, gl.GL_ONE_MINUS_SRC_ALPHA)
        gl.glDisable(gl.GL_DEPTH_TEST)
        
        print("OpenGL configured")

        # 创建模型
        self.model = live2d.LAppModel()

        if live2d.LIVE2D_VERSION == 3:
            model_path = os.path.join(resources.RESOURCES_DIRECTORY, "v3/Hiyori/Hiyori.model3.json")
        else:
            model_path = os.path.join(resources.RESOURCES_DIRECTORY, "v2/shizuku/shizuku.model.json")
            
        print(f"Loading model: {model_path}")
        self.model.LoadModelJson(model_path)

        self.startTimer(int(1000 / 60))
        print("OpenGL initialization complete")

    def resizeGL(self, w: int, h: int) -> None:
        """窗口大小改变时调用"""
        if self.model:
            self.model.Resize(w, h)

    def paintGL(self) -> None:
        """绘制函数 - 使用黑色背景（将被 Windows API 设为透明）"""
        # 使用纯黑色背景，Windows API 会将其设为透明
        live2d.clearBuffer(0.0, 0.0, 0.0, 1.0)

        if self.model:
            self.model.Update()
            self.model.Draw()

    def timerEvent(self, event: QTimerEvent) -> None:
        """定时器事件"""
        if not self.isVisible():
            return
        self.update()

    def keyPressEvent(self, event) -> None:
        """键盘事件"""
        if event.key() == Qt.Key.Key_Escape:
            print("ESC pressed - closing window")
            self.close()
        elif event.key() == Qt.Key.Key_R:
            # R 键重新应用透明
            hwnd = int(self.winId())
            if hwnd:
                print("Reapplying transparency...")
                make_window_transparent(hwnd)
        super().keyPressEvent(event)


def main():
    """主函数"""
    print("Starting Windows API Transparent Live2D Test...")
    
    # 基本 OpenGL 格式
    format = QSurfaceFormat()
    format.setAlphaBufferSize(8)
    format.setDepthBufferSize(24)
    format.setStencilBufferSize(8)
    format.setVersion(2, 1)
    format.setProfile(QSurfaceFormat.OpenGLContextProfile.CompatibilityProfile)
    QSurfaceFormat.setDefaultFormat(format)
    print("OpenGL format configured")
    
    # 初始化 Live2D
    live2d.init()
    print("Live2D initialized")

    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建透明窗口
    window = WinAPITransparentWindow()
    window.show()
    
    print("\n" + "="*60)
    print("WINDOWS API TRANSPARENT TEST")
    print("="*60)
    print("This version uses Windows API SetLayeredWindowAttributes")
    print("to make black background transparent.")
    print("")
    print("How it works:")
    print("1. Renders Live2D on black background")
    print("2. Uses Windows API to make black pixels transparent")
    print("3. Live2D character should appear floating")
    print("")
    print("Controls:")
    print("- ESC: Close window")
    print("- R: Reapply transparency")
    print("")
    print("If this doesn't work, the issue may be:")
    print("- Windows composition disabled")
    print("- Graphics driver limitations")
    print("- System-level transparency restrictions")
    print("="*60)
    
    # 运行应用程序
    try:
        app.exec()
    finally:
        print("Disposing Live2D...")
        live2d.dispose()
        print("Application closed")


if __name__ == "__main__":
    main()
